import { describe, it, expect } from 'vitest';
import { JSON_EXTRA_parse, JSON_EXTRA_stringify } from '../../../src/JSON-Extra/index';

describe('JSON-Extra', () => {
  describe('JSON_EXTRA_parse', () => {
    it('should parse regular JSON', () => {
      const json = '{"name":"test","value":123}';
      const result = JSON_EXTRA_parse(json);
      expect(result).toEqual({ name: 'test', value: 123 });
    });
  });

  describe('JSON_EXTRA_stringify', () => {
    it('should stringify regular JSON', () => {
      const obj = { name: 'test', value: 123 };
      const result = JSON_EXTRA_stringify(obj);
      expect(JSON.parse(result)).toEqual(obj);
    });
  });
});