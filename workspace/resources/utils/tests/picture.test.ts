import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getValidatedPictureURL } from '../src/picture';

describe('getValidatedPictureURL', () => {
  const placeholderPictureURL = 'https://storage.googleapis.com/divinci-bucket/placeholder-robot.png';
  
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });
  
  afterEach(() => {
    vi.restoreAllMocks();
  });
  
  it('should return the valid URL from userPicture', () => {
    const validURL = 'https://example.com/image.jpg';
    const json = { userPicture: validURL };
    
    expect(getValidatedPictureURL(json)).toBe(validURL);
  });
  
  it('should return placeholder URL when userPicture is undefined', () => {
    const json = {};
    
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
  });
  
  it('should return placeholder URL when userPicture is null', () => {
    const json = { userPicture: null as unknown as string };
    
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
  });
  
  it('should return placeholder URL when userPicture is an empty string', () => {
    const json = { userPicture: '' };
    
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
  });
  
  it('should return placeholder URL when userPicture is a whitespace string', () => {
    const json = { userPicture: '   ' };
    
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
  });
  
  it('should return placeholder URL when userPicture is an invalid URL', () => {
    const json = { userPicture: 'not-a-url' };
    
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
    expect(console.error).toHaveBeenCalled();
  });
  
  it('should handle relative URLs', () => {
    const json = { userPicture: '/relative/path/image.jpg' };
    
    // This should fail URL validation and return the placeholder
    expect(getValidatedPictureURL(json)).toBe(placeholderPictureURL);
    expect(console.error).toHaveBeenCalled();
  });
  
  it('should handle null input', () => {
    expect(getValidatedPictureURL(null as any)).toBe(placeholderPictureURL);
  });
});
