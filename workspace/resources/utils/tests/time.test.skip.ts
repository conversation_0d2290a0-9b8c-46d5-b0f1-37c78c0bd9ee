import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getTime, TIME_SECOND, TIME_MINUTE, TIME_HOUR, TIME_DAY, TIME_MONTH_TIME } from '../src/time';

describe('Time Constants', () => {
  it('should define TIME_SECOND as 1000', () => {
    expect(TIME_SECOND).toBe(1000);
  });

  it('should define TIME_MINUTE as 60 * TIME_SECOND', () => {
    expect(TIME_MINUTE).toBe(60 * TIME_SECOND);
  });

  it('should define TIME_HOUR as 60 * TIME_MINUTE', () => {
    expect(TIME_HOUR).toBe(60 * TIME_MINUTE);
  });

  it('should define TIME_DAY as 24 * TIME_HOUR', () => {
    expect(TIME_DAY).toBe(24 * TIME_HOUR);
  });

  it('should define TIME_MONTH_TIME as 30 * TIME_DAY', () => {
    expect(TIME_MONTH_TIME).toBe(30 * TIME_DAY);
  });
});

describe('getTime function', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should return a number', async () => {
    // Mock the delay function to resolve immediately
    vi.spyOn(global, 'setTimeout').mockImplementation((cb) => {
      setTimeout(() => cb(), 0);
      return 0;
    });

    const time = await getTime();
    expect(typeof time).toBe('number');
  }, 10000);

  it('should have delay of 100ms', async () => {
    const now = Date.now();
    vi.setSystemTime(now);

    const promise = getTime();
    vi.advanceTimersByTime(100);
    const time = await promise;

    expect(time).toBe(now + 100);
  });

  it('should return current time after delay', async () => {
    vi.setSystemTime(0);

    const promise = getTime();
    vi.advanceTimersByTime(100);
    const time = await promise;

    expect(time).toBe(100);
  });
});
