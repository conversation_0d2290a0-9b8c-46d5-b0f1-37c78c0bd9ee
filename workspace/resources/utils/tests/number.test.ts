import { describe, it, expect } from 'vitest';
import { 
  MAX_SAFE_NUMBER, 
  THOUSAND, 
  MILLION, 
  BILLION, 
  getMaximumDigitForBase, 
  setMaxDecimalPlaces, 
  findMaxSingleDigit 
} from '../src/number';

describe('Number Constants', () => {
  it('should define MAX_SAFE_NUMBER as 2^32 - 1', () => {
    expect(MAX_SAFE_NUMBER).toBe(Math.pow(2, 32) - 1);
  });

  it('should define THOUSAND as 1000', () => {
    expect(THOUSAND).toBe(1000);
  });

  it('should define MILLION as 1000 * THOUSAND', () => {
    expect(MILLION).toBe(1000 * THOUSAND);
  });

  it('should define BILLION as 1000 * MILLION', () => {
    expect(BILLION).toBe(1000 * MILLION);
  });
});

describe('getMaximumDigitForBase', () => {
  it('should return the maximum single digit for base 10', () => {
    expect(getMaximumDigitForBase(10)).toBe(10);
  });

  it('should return the maximum single digit for base 2', () => {
    expect(getMaximumDigitForBase(2)).toBe(2);
  });

  it('should return the maximum single digit for base 16', () => {
    expect(getMaximumDigitForBase(16)).toBe(16);
  });

  it('should return the maximum single digit for base 36', () => {
    expect(getMaximumDigitForBase(36)).toBe(36);
  });
});

describe('setMaxDecimalPlaces', () => {
  it('should round to the specified number of decimal places', () => {
    expect(setMaxDecimalPlaces(3.14159, 2)).toBe(3.14);
    expect(setMaxDecimalPlaces(3.14159, 3)).toBe(3.142);
    expect(setMaxDecimalPlaces(3.14159, 4)).toBe(3.1416);
    expect(setMaxDecimalPlaces(3.14159, 5)).toBe(3.14159);
  });

  it('should handle zero decimal places', () => {
    expect(setMaxDecimalPlaces(3.14159, 0)).toBe(3);
  });

  it('should handle negative numbers', () => {
    expect(setMaxDecimalPlaces(-3.14159, 2)).toBe(-3.14);
  });

  it('should handle integers', () => {
    expect(setMaxDecimalPlaces(42, 2)).toBe(42);
  });
});

describe('findMaxSingleDigit', () => {
  it('should find the maximum single digit for base 10', () => {
    expect(findMaxSingleDigit(10)).toBe(9);
  });

  it('should find the maximum single digit for base 2', () => {
    expect(findMaxSingleDigit(2)).toBe(1);
  });

  it('should find the maximum single digit for base 16', () => {
    expect(findMaxSingleDigit(16)).toBe(15);
  });

  it('should find the maximum single digit for base 36', () => {
    expect(findMaxSingleDigit(36)).toBe(35);
  });
});
