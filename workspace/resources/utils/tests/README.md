# Utils Tests

This directory contains tests for the Utils package.

## Test Structure

- `unit/`: Unit tests for individual utility functions
- `integration/`: Integration tests for utility modules

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/string/format` resolves to `src/string/format`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { formatString } from '@/string/format';

// Import from tests directory
import { testData } from '~/helpers/test-data';
```

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## Writing Tests

When writing tests, follow these guidelines:

1. Use Vitest's testing functions (`describe`, `it`, `expect`, etc.)
2. Use path aliases for imports when possible
3. Test both success and error cases
4. Keep tests focused and small

### Example Test

```typescript
import { describe, it, expect } from 'vitest';
import { formatString } from '@/string/format';

describe('formatString', () => {
  it('should format a string with placeholders', () => {
    const result = formatString('Hello, {0}!', 'World');
    expect(result).toBe('Hello, World!');
  });

  it('should handle multiple placeholders', () => {
    const result = formatString('{0} {1}!', 'Hello', 'World');
    expect(result).toBe('Hello World!');
  });

  it('should handle missing placeholders', () => {
    const result = formatString('Hello, {0}!', undefined);
    expect(result).toBe('Hello, !');
  });
});
```
