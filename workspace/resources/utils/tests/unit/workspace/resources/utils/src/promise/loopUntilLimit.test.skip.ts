import { delay } from '../../../../../../../src/promise';
import { loopUntilLimit } from '../../../../../../../src/promise';

import { Jest } from '@jest/environment';

describe('loopUntilLimit()', () => {
  jest.useFakeTimers();

  it('returns the result when function succeeds within limit', async () => {
    const fn = jest.fn();
    fn.mockReturnValueOnce(undefined);
    fn.mockReturnValue('Success');
    await expect(loopUntilLimit(3, fn)).resolves.toBe('Success');
    expect(fn).toHaveBeenCalledTimes(2);
  });

  it('throws an error when function fails after attempts exhausted', async () => {
    const errorMessage = 'Function failed after 3 attempts';
    const fn = jest.fn().mockImplementation(() => undefined);
    await expect(loopUntilLimit(3, fn, new Error(errorMessage))).rejects.toThrow(errorMessage);
    expect(fn).toHaveBeenCalledTimes(3);
  });

  it('delays for 250ms before each retry', async () => {
    const fn = jest.fn().mockImplementation(() => undefined);
    await loopUntilLimit(3, fn).catch(() => {});
    expect(setTimeout).toHaveBeenCalledTimes(3);
    expect(setTimeout).toHaveBeenCalledWith(expect.any(Function), 250);
  });

  it('does not delay before first attempt', async () => {
    const fn = jest.fn();
    fn.mockReturnValueOnce('Success');
    await expect(loopUntilLimit(3, fn)).resolves.toBe('Success');
    expect(setTimeout).not.toHaveBeenCalled();
  });
});