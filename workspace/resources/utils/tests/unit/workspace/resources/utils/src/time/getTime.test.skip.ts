import { getTime } from '../../../../../../../src/time';

describe('getTime function', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  test('should return a number', async () => {
    const time = await getTime();
    expect(typeof time).toBe('number');
  });

  test('should have delay of 100ms', async () => {
    const timeBefore = Date.now();
    await getTime();
    const timeAfter = Date.now();
    expect(timeAfter - timeBefore).toBeGreaterThanOrEqual(100);
  });

  test('should return current time', async () => {
    jest.setSystemTime(0);
    const time = await getTime();
    expect(time).toBe(100);
  });

});