import QueuedResolver from '../../../../../../../src/promise';

describe('QueuedResolver', () => {
  let queuedResolver: QueuedResolver;

  beforeEach(() => {
    queuedResolver = new QueuedResolver();
  });

  it('should have currentPromise property set to a resolved promise', async () => {
    const currentValue = await queuedResolver.currentPromise;
    expect(currentValue).toBeUndefined();
  });

  it('should support addition of cancelable function to currentPromise', async () => {
    const cancelablePromise: CancelablePromise<number> = new Promise((resolve) => resolve(5)) as CancelablePromise<number>;
    cancelablePromise.cancel = jest.fn();
    
    queuedResolver.currentPromise = cancelablePromise;
    (queuedResolver.currentPromise as CancelablePromise<number>).cancel();

    expect(cancelablePromise.cancel).toHaveBeenCalled();
  });

  it('should not fail when cancelling the default currentPromise', () => {
    expect(() => (queuedResolver.currentPromise as CancelablePromise<number>).cancel()).not.toThrow();
  });
});