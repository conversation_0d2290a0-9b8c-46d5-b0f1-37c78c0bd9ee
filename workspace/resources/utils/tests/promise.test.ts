import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  splitAndMergeWork,
  neverResolve,
  delay,
  alwaysDelay,
  loopUntilLimit,
  QueuedResolver,
  series,
  seriesReduce,
  eachSeries,
  eachSeriesReduce,
  waitForEventTarget,
  waitForEventEmitter
} from "../src/promise";
import { EventEmitter } from "events";
import { getUserCookieFromRequest } from "@divinci-ai/server-utils";
import { ChannelSubscriber } from "@divinci-ai/server-globals";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { WhiteLabelModel, TranscriptModel } from "@divinci-ai/server-models";
import { watchTranscriptUpdate } from "../../src/routes/ws/whitelabel-ws";

describe("splitAndMergeWork", ()=>{
  it("should split work into chunks and merge results", async ()=>{
    const items = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    const chunkSize = 3;

    const workFn = vi.fn(async (chunk, startIndex)=>{
      return chunk.map(item=>item * 2);
    });

    const result = await splitAndMergeWork(chunkSize, items, workFn);

    expect(workFn).toHaveBeenCalledTimes(4); // 10 items with chunk size 3 = 4 chunks
    expect(workFn).toHaveBeenNthCalledWith(1, [1, 2, 3], 0);
    expect(workFn).toHaveBeenNthCalledWith(2, [4, 5, 6], 3);
    expect(workFn).toHaveBeenNthCalledWith(3, [7, 8, 9], 6);
    expect(workFn).toHaveBeenNthCalledWith(4, [10], 9);

    expect(result).toEqual([2, 4, 6, 8, 10, 12, 14, 16, 18, 20]);
  });

  it("should handle empty array", async ()=>{
    const items = [];
    const workFn = vi.fn(async (chunk, startIndex)=>{
      return chunk.map(item=>item * 2);
    });

    const result = await splitAndMergeWork(3, items, workFn);

    expect(workFn).not.toHaveBeenCalled();
    expect(result).toEqual([]);
  });
});

describe("neverResolve", ()=>{
  it("should return a promise that never resolves", ()=>{
    const promise = neverResolve();
    expect(promise).toBeInstanceOf(Promise);

    // We can't really test that it never resolves in a unit test,
    // but we can verify it's a Promise
  });
});

describe("delay", ()=>{
  beforeEach(()=>{
    vi.useFakeTimers();
  });

  afterEach(()=>{
    vi.useRealTimers();
  });

  it("should delay for the specified time", async ()=>{
    const callback = vi.fn();
    const promise = delay(1000).then(callback);

    expect(callback).not.toHaveBeenCalled();

    vi.advanceTimersByTime(999);
    await Promise.resolve(); // Let any pending microtasks run
    expect(callback).not.toHaveBeenCalled();

    vi.advanceTimersByTime(1);
    await Promise.resolve(); // Let any pending microtasks run
    expect(callback).toHaveBeenCalledTimes(1);
  });
});

describe("alwaysDelay", ()=>{
  beforeEach(()=>{
    vi.useFakeTimers();
    vi.spyOn(Date, "now").mockImplementation(()=>1000);
  });

  afterEach(()=>{
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it("should handle successful execution", async ()=>{
    const toRun = vi.fn().mockResolvedValue("result");
    const end = vi.fn(x=>x);
    const next = vi.fn();

    // Mock the delay function to resolve immediately
    const originalDelay = delay;
    vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
      cb();
      return 0;
    });

    await alwaysDelay(500, toRun, end, next);

    expect(toRun).toHaveBeenCalledTimes(1);
    expect(end).toHaveBeenCalledWith("result");
    expect(next).not.toHaveBeenCalled();
  });

  it("should handle errors", async ()=>{
    const error = new Error("Test error");
    const toRun = vi.fn().mockRejectedValue(error);
    const end = vi.fn();
    const next = vi.fn();

    // Mock the delay function to resolve immediately
    vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
      cb();
      return 0;
    });

    await alwaysDelay(500, toRun, end, next);

    expect(toRun).toHaveBeenCalledTimes(1);
    expect(next).toHaveBeenCalledWith(error);
    expect(end).not.toHaveBeenCalled();
  });
});

describe("loopUntilLimit", ()=>{
  beforeEach(()=>{
    vi.useFakeTimers();
    // Mock the delay function to resolve immediately
    vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
      cb();
      return 0;
    });
  });

  afterEach(()=>{
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it("should return the result when function succeeds within limit", async ()=>{
    const fn = vi.fn();
    fn.mockReturnValueOnce(undefined);
    fn.mockReturnValue("Success");

    const result = await loopUntilLimit(3, fn);

    expect(fn).toHaveBeenCalledTimes(2);
    expect(result).toBe("Success");
  });

  it("should throw an error when function fails after attempts exhausted", async ()=>{
    const errorMessage = "Function failed after 3 attempts";
    const fn = vi.fn().mockImplementation(()=>undefined);

    await expect(loopUntilLimit(3, fn, new Error(errorMessage)))
      .rejects.toThrow(errorMessage);

    expect(fn).toHaveBeenCalledTimes(3);
  });

  it("should throw a default error when no error is provided", async ()=>{
    const fn = vi.fn().mockImplementation(()=>undefined);

    await expect(loopUntilLimit(2, fn))
      .rejects.toThrow("timeout");

    expect(fn).toHaveBeenCalledTimes(2);
  });
});

describe("QueuedResolver", ()=>{
  it("should execute functions in sequence", async ()=>{
    const resolver = new QueuedResolver();
    const results = [];

    // Mock the delay function to resolve immediately
    vi.spyOn(global, "setTimeout").mockImplementation((cb)=>{
      cb();
      return 0;
    });

    // Create three functions that resolve after different delays
    const fn1 = vi.fn(async ()=>{
      // We'll manually control the order instead of using delay
      results.push(1);
      return 1;
    });

    const fn2 = vi.fn(async ()=>{
      results.push(2);
      return 2;
    });

    const fn3 = vi.fn(async ()=>{
      results.push(3);
      return 3;
    });

    // Queue all functions
    const promise1 = resolver.wrap(fn1);
    const promise2 = resolver.wrap(fn2);
    const promise3 = resolver.wrap(fn3);

    // Wait for all to complete
    const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);

    // Check results
    expect(result1).toBe(1);
    expect(result2).toBe(2);
    expect(result3).toBe(3);

    // Check execution order
    expect(results).toEqual([1, 2, 3]);
  });

  it("should continue execution even if a function throws", async ()=>{
    const resolver = new QueuedResolver();
    const results = [];

    const fn1 = async ()=>{
      results.push("fn1");
      return "fn1 result";
    };

    const fn2 = async ()=>{
      results.push("fn2");
      throw new Error("fn2 error");
    };

    const fn3 = async ()=>{
      results.push("fn3");
      return "fn3 result";
    };

    // Queue all functions
    const promise1 = resolver.wrap(fn1);
    const promise2 = resolver.wrap(fn2).catch(e=>`caught: ${e.message}`);
    const promise3 = resolver.wrap(fn3);

    // Wait for all to complete
    const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);

    // Check results
    expect(result1).toBe("fn1 result");
    expect(result2).toBe("caught: fn2 error");
    expect(result3).toBe("fn3 result");

    // Check execution order
    expect(results).toEqual(["fn1", "fn2", "fn3"]);
  });
});

describe("series", ()=>{
  it("should execute functions in series", async ()=>{
    const results = [];

    const fn1 = vi.fn(async ()=>{
      results.push(1);
    });

    const fn2 = vi.fn(async ()=>{
      results.push(2);
    });

    const fn3 = vi.fn(async ()=>{
      results.push(3);
    });

    await series([fn1, fn2, fn3]);

    expect(fn1).toHaveBeenCalledTimes(1);
    expect(fn2).toHaveBeenCalledTimes(1);
    expect(fn3).toHaveBeenCalledTimes(1);
    expect(results).toEqual([1, 2, 3]);
  });

  it("should stop execution if a function throws", async ()=>{
    const results = [];

    const fn1 = vi.fn(async ()=>{
      results.push(1);
    });

    const fn2 = vi.fn(async ()=>{
      results.push(2);
      throw new Error("fn2 error");
    });

    const fn3 = vi.fn(async ()=>{
      results.push(3);
    });

    await expect(series([fn1, fn2, fn3])).rejects.toThrow("fn2 error");

    expect(fn1).toHaveBeenCalledTimes(1);
    expect(fn2).toHaveBeenCalledTimes(1);
    expect(fn3).not.toHaveBeenCalled();
    expect(results).toEqual([1, 2]);
  });
});

describe("seriesReduce", ()=>{
  it("should execute functions in series using reduce", async ()=>{
    const results = [];

    const fn1 = vi.fn(async ()=>{
      results.push(1);
    });

    const fn2 = vi.fn(async ()=>{
      results.push(2);
    });

    const fn3 = vi.fn(async ()=>{
      results.push(3);
    });

    await seriesReduce([fn1, fn2, fn3]);

    expect(fn1).toHaveBeenCalledTimes(1);
    expect(fn2).toHaveBeenCalledTimes(1);
    expect(fn3).toHaveBeenCalledTimes(1);
    expect(results).toEqual([1, 2, 3]);
  });
});

describe("eachSeries", ()=>{
  it("should execute callback for each item in series", async ()=>{
    const items = [1, 2, 3];
    const results = [];

    const callback = vi.fn(async (item)=>{
      results.push(item * 2);
    });

    await eachSeries(items, callback);

    expect(callback).toHaveBeenCalledTimes(3);
    expect(callback).toHaveBeenNthCalledWith(1, 1);
    expect(callback).toHaveBeenNthCalledWith(2, 2);
    expect(callback).toHaveBeenNthCalledWith(3, 3);
    expect(results).toEqual([2, 4, 6]);
  });
});

describe("eachSeriesReduce", ()=>{
  it("should execute callback for each item in series using reduce", async ()=>{
    const items = [1, 2, 3];
    const results = [];

    const callback = vi.fn(async (item)=>{
      results.push(item * 2);
    });

    await eachSeriesReduce(items, callback);

    expect(callback).toHaveBeenCalledTimes(3);
    expect(callback).toHaveBeenNthCalledWith(1, 1);
    expect(callback).toHaveBeenNthCalledWith(2, 2);
    expect(callback).toHaveBeenNthCalledWith(3, 3);
    expect(results).toEqual([2, 4, 6]);
  });
});

describe("waitForEventTarget", ()=>{
  it("should resolve when the event is triggered", async ()=>{
    const target = new EventTarget();
    const promise = waitForEventTarget(target, "test-event");

    const event = new Event("test-event");
    target.dispatchEvent(event);

    const result = await promise;
    expect(result).toBe(event);
  });

  it("should apply filter to events", async ()=>{
    const target = new EventTarget();
    const filter = vi.fn((event)=>{
      return event.detail && event.detail.pass === true;
    });

    const promise = waitForEventTarget(target, "test-event", filter);

    // Dispatch an event that doesn't pass the filter
    const event1 = new CustomEvent("test-event", { detail: { pass: false } });
    target.dispatchEvent(event1);

    // Dispatch an event that passes the filter
    const event2 = new CustomEvent("test-event", { detail: { pass: true } });
    target.dispatchEvent(event2);

    const result = await promise;
    expect(result).toBe(event2);
    expect(filter).toHaveBeenCalledTimes(2);
  });

  it("should be cancelable", async ()=>{
    const target = new EventTarget();
    const promise = waitForEventTarget(target, "test-event");

    promise.cancel();

    await expect(promise).rejects.toThrow("Canceled waiting for event");
  });

  it("should be cancelable with custom reason", async ()=>{
    const target = new EventTarget();
    const promise = waitForEventTarget(target, "test-event");
    const customError = new Error("Custom cancel reason");

    promise.cancel(customError);

    await expect(promise).rejects.toThrow("Custom cancel reason");
  });
});

describe("waitForEventEmitter", ()=>{
  it("should resolve when the event is triggered", async ()=>{
    const emitter = new EventEmitter();
    const promise = waitForEventEmitter(emitter, "test-event");

    const eventArgs = ["arg1", "arg2"];
    emitter.emit("test-event", ...eventArgs);

    const result = await promise;
    expect(result).toEqual(eventArgs);
  });

  it("should apply filter to events", async ()=>{
    const emitter = new EventEmitter();
    const filter = vi.fn((...args)=>{
      return args[0] === "pass";
    });

    const promise = waitForEventEmitter(emitter, "test-event", filter);

    // Emit an event that doesn't pass the filter
    emitter.emit("test-event", "fail");

    // Emit an event that passes the filter
    emitter.emit("test-event", "pass");

    const result = await promise;
    expect(result).toEqual(["pass"]);
    expect(filter).toHaveBeenCalledTimes(2);
  });

  it("should be cancelable", async ()=>{
    const emitter = new EventEmitter();
    const promise = waitForEventEmitter(emitter, "test-event");

    promise.cancel();

    await expect(promise).rejects.toThrow("Canceled waiting for event");
  });

  it("should be cancelable with custom reason", async ()=>{
    const emitter = new EventEmitter();
    const promise = waitForEventEmitter(emitter, "test-event");
    const customError = new Error("Custom cancel reason");

    promise.cancel(customError);

    await expect(promise).rejects.toThrow("Custom cancel reason");
  });
});
