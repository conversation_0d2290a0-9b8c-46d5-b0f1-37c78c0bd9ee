import { describe, it, expect } from 'vitest';
import { regexIndexOf, regexLastIndexOf } from '../../src/string/search';

describe('regexIndexOf', () => {
  it('should find the first index of a regex match', () => {
    expect(regexIndexOf('hello world', /world/)).toBe(6);
    expect(regexIndexOf('hello world', /o/)).toBe(4);
    expect(regexIndexOf('hello world', /l/)).toBe(2);
  });

  it('should return -1 if no match is found', () => {
    expect(regexIndexOf('hello world', /xyz/)).toBe(-1);
  });

  it('should start searching from the specified position', () => {
    expect(regexIndexOf('hello world', /l/, 3)).toBe(3);
    expect(regexIndexOf('hello world', /o/, 5)).toBe(7);
  });

  it('should handle regex with flags', () => {
    expect(regexIndexOf('Hello World', /world/i)).toBe(6);
    expect(regexIndexOf('hello\nworld', /^world/m)).toBe(6);
  });

  it('should make the regex global if it is not already', () => {
    const nonGlobalRegex = /o/;
    expect(nonGlobalRegex.global).toBe(false);
    
    // The function should still work with a non-global regex
    expect(regexIndexOf('hello world', nonGlobalRegex)).toBe(4);
  });
});

describe('regexLastIndexOf', () => {
  it('should find the last index of a regex match', () => {
    expect(regexLastIndexOf('hello world', /l/)).toBe(9);
    expect(regexLastIndexOf('hello world', /o/)).toBe(7);
    expect(regexLastIndexOf('hello hello', /hello/)).toBe(6);
  });

  it('should return -1 if no match is found', () => {
    expect(regexLastIndexOf('hello world', /xyz/)).toBe(-1);
  });

  it('should only search up to the specified position', () => {
    expect(regexLastIndexOf('hello world', /l/, 8)).toBe(3);
    expect(regexLastIndexOf('hello world', /o/, 5)).toBe(4);
  });

  it('should handle regex with flags', () => {
    expect(regexLastIndexOf('Hello World', /world/i)).toBe(6);
    expect(regexLastIndexOf('hello\nworld', /^world/m)).toBe(6);
  });

  it('should make the regex global if it is not already', () => {
    const nonGlobalRegex = /o/;
    expect(nonGlobalRegex.global).toBe(false);
    
    // The function should still work with a non-global regex
    expect(regexLastIndexOf('hello world', nonGlobalRegex)).toBe(7);
  });
});
