import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { uniqueId, padString, randomNumbers, escapeRegExp } from '../../src/string/random';

describe('uniqueId', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.spyOn(Math, 'random').mockReturnValue(0.5);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it('should generate a unique ID with default joiner', () => {
    vi.setSystemTime(new Date(2023, 0, 1));
    const id1 = uniqueId();
    
    // Increment counter
    const id2 = uniqueId();
    
    expect(id1).not.toBe(id2);
    expect(id1.length).toBeGreaterThan(0);
    expect(id2.length).toBeGreaterThan(0);
  });

  it('should use the provided joiner', () => {
    vi.setSystemTime(new Date(2023, 0, 1));
    const id = uniqueId('-');
    
    expect(id.includes('-')).toBe(true);
    expect(id.split('-').length).toBe(3);
  });

  it('should increment counter and wrap around at MAX_SAFE_NUMBER', () => {
    // This is hard to test directly, but we can at least verify the function doesn't throw
    for (let i = 0; i < 10; i++) {
      expect(() => uniqueId()).not.toThrow();
    }
  });
});

describe('padString', () => {
  it('should pad a string to the expected length', () => {
    expect(padString('123', 5)).toBe('00123');
    expect(padString('a', 3)).toBe('00a');
    expect(padString('', 2)).toBe('00');
  });

  it('should return the original string if it matches the expected length', () => {
    expect(padString('12345', 5)).toBe('12345');
  });

  it('should truncate the string if it exceeds the expected length', () => {
    expect(padString('123456', 5)).toBe('12345');
    expect(padString('abcdefg', 3)).toBe('abc');
  });
});

describe('randomNumbers', () => {
  beforeEach(() => {
    vi.spyOn(Math, 'random');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should generate a string of random numbers with the specified length', () => {
    vi.mocked(Math.random)
      .mockReturnValueOnce(0.1)
      .mockReturnValueOnce(0.2)
      .mockReturnValueOnce(0.3)
      .mockReturnValueOnce(0.4)
      .mockReturnValueOnce(0.5);
    
    const result = randomNumbers(5);
    
    expect(result.length).toBe(5);
    expect(/^\d{5}$/.test(result)).toBe(true);
  });

  it('should use the specified base', () => {
    vi.mocked(Math.random)
      .mockReturnValueOnce(0.9375) // 15/16
      .mockReturnValueOnce(0.0625) // 1/16
      .mockReturnValueOnce(0.5);   // 8/16
    
    const result = randomNumbers(3, 16);
    
    expect(result.length).toBe(3);
    expect(/^[0-9a-f]{3}$/.test(result)).toBe(true);
    expect(result).toBe('f18');
  });

  it('should handle length greater than what a single random call provides', () => {
    vi.mocked(Math.random).mockReturnValue(0.5);
    
    const result = randomNumbers(10);
    
    expect(result.length).toBe(10);
    expect(Math.random).toHaveBeenCalledTimes(10);
  });
});

describe('escapeRegExp', () => {
  it('should escape special regex characters', () => {
    expect(escapeRegExp('hello.world')).toBe('hello\\.world');
    expect(escapeRegExp('a+b*c?d^e$f|g(h)i{j}k[l]')).toBe('a\\+b\\*c\\?d\\^e\\$f\\|g\\(h\\)i\\{j\\}k\\[l\\]');
    expect(escapeRegExp('\\')).toBe('\\\\');
  });

  it('should not modify non-special characters', () => {
    expect(escapeRegExp('abcdefghijklmnopqrstuvwxyz')).toBe('abcdefghijklmnopqrstuvwxyz');
    expect(escapeRegExp('ABCDEFGHIJKLMNOPQRSTUVWXYZ')).toBe('ABCDEFGHIJKLMNOPQRSTUVWXYZ');
    expect(escapeRegExp('0123456789')).toBe('0123456789');
  });
});
