import { describe, it, expect } from 'vitest';
import { capitalizeFirst, capitalizeEachWord } from '../../src/string/locale';

describe('capitalizeFirst', () => {
  it('should capitalize the first letter of a string', () => {
    expect(capitalizeFirst('hello')).toBe('Hello');
    expect(capitalizeFirst('world')).toBe('World');
    expect(capitalizeFirst('hello world')).toBe('Hello world');
  });

  it('should handle already capitalized strings', () => {
    expect(capitalizeFirst('Hello')).toBe('Hello');
    expect(capitalizeFirst('WORLD')).toBe('WORLD');
  });

  it('should handle empty string', () => {
    expect(capitalizeFirst('')).toBe('');
  });

  it('should handle single character strings', () => {
    expect(capitalizeFirst('a')).toBe('A');
    expect(capitalizeFirst('Z')).toBe('Z');
  });

  it('should handle strings with non-letter first characters', () => {
    expect(capitalizeFirst('123abc')).toBe('123abc');
    expect(capitalizeFirst(' abc')).toBe(' abc');
  });
});

describe('capitalizeEachWord', () => {
  it('should capitalize the first letter of each word', () => {
    expect(capitalizeEachWord('hello world')).toBe('Hello World');
    expect(capitalizeEachWord('the quick brown fox')).toBe('The Quick Brown Fox');
  });

  it('should handle already capitalized words', () => {
    expect(capitalizeEachWord('Hello World')).toBe('Hello World');
    expect(capitalizeEachWord('HELLO WORLD')).toBe('HELLO WORLD');
  });

  it('should handle empty string', () => {
    expect(capitalizeEachWord('')).toBe('');
  });

  it('should handle single word', () => {
    expect(capitalizeEachWord('hello')).toBe('Hello');
  });

  it('should handle multiple spaces', () => {
    expect(capitalizeEachWord('hello  world')).toBe('Hello  World');
  });

  it('should handle leading and trailing spaces', () => {
    expect(capitalizeEachWord(' hello world ')).toBe(' Hello World ');
  });
});
