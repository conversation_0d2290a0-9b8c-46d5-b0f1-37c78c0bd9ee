import { describe, it, expect } from 'vitest';
import { EMAIL_REGEXP, testEmail, isGoodUsername, isGoodPassword } from '../../src/string/user';

describe('EMAIL_REGEXP', () => {
  it('should match valid email addresses', () => {
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(true);
  });

  it('should not match invalid email addresses', () => {
    expect(EMAIL_REGEXP.test('test')).toBe(false);
    expect(EMAIL_REGEXP.test('test@')).toBe(false);
    expect(EMAIL_REGEXP.test('@example.com')).toBe(false);
    expect(EMAIL_REGEXP.test('test@example')).toBe(false);
    expect(EMAIL_REGEXP.test('test@.com')).toBe(false);
    expect(EMAIL_REGEXP.test('<EMAIL>')).toBe(false);
    expect(EMAIL_REGEXP.test('test@exam ple.com')).toBe(false);
  });
});

describe('testEmail', () => {
  it('should not throw for valid email addresses', () => {
    expect(() => testEmail('<EMAIL>')).not.toThrow();
    expect(() => testEmail('<EMAIL>')).not.toThrow();
  });

  it('should throw for invalid email addresses', () => {
    expect(() => testEmail('test')).toThrow('Invalid email: test');
    expect(() => testEmail('test@')).toThrow('Invalid email: test@');
  });

  it('should throw the provided error if specified', () => {
    const customError = new Error('Custom error');
    expect(() => testEmail('test', customError)).toThrow(customError);
  });
});

describe('isGoodUsername', () => {
  it('should not throw for valid usernames', () => {
    expect(() => isGoodUsername('validusername')).not.toThrow();
    expect(() => isGoodUsername('valid_username')).not.toThrow();
    expect(() => isGoodUsername('12345')).not.toThrow();
  });

  it('should throw for usernames that are too short', () => {
    expect(() => isGoodUsername('abc')).toThrow('needs to be longer');
    expect(() => isGoodUsername('a')).toThrow('needs to be longer');
  });

  it('should throw the provided error if specified', () => {
    const customError = new Error('Custom error');
    expect(() => isGoodUsername('abc', customError)).toThrow(customError);
  });
});

describe('isGoodPassword', () => {
  it('should not throw for valid passwords', () => {
    expect(() => isGoodPassword('ValidP@ss123')).not.toThrow();
    expect(() => isGoodPassword('C0mplex!Password')).not.toThrow();
    expect(() => isGoodPassword('Abcd1234!')).not.toThrow();
  });

  it('should throw for passwords that are too short', () => {
    expect(() => isGoodPassword('Abc1!')).toThrow('needs to be longer');
  });

  it('should throw for passwords without lowercase letters', () => {
    expect(() => isGoodPassword('ABCDEF123!')).toThrow('need at least 1 lowercase letter');
  });

  it('should throw for passwords without uppercase letters', () => {
    expect(() => isGoodPassword('abcdef123!')).toThrow('need at least 1 uppercase letter');
  });

  it('should throw for passwords without numbers', () => {
    expect(() => isGoodPassword('AbcdefGHI!')).toThrow('need at least 1 number');
  });

  it('should throw for passwords without symbols', () => {
    expect(() => isGoodPassword('Abcdef123')).toThrow('need at least 1 non letter non number symbol');
  });

  it('should throw the provided error if specified', () => {
    const customError = new Error('Custom error');
    expect(() => isGoodPassword('abc', customError)).toThrow(customError);
  });
});
