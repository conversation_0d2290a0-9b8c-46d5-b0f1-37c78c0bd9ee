import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

describe('debug module', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.resetModules();
    process.env = { ...originalEnv };
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    process.env = originalEnv;
    vi.restoreAllMocks();
  });

  it('should not log when LOG_DEBUG is not set', async () => {
    delete process.env.LOG_DEBUG;

    const { logDebug } = await import('../../src/debug/index.js');

    logDebug('test message');

    expect(console.log).not.toHaveBeenCalledWith('test message');
  });

  it('should log when LOG_DEBUG is set', async () => {
    process.env.LOG_DEBUG = 'true';

    const { logDebug } = await import('../../src/debug/index.js');

    logDebug('test message');

    expect(console.log).toHaveBeenCalledWith('test message');
  });

  it('should log initialization message when LOG_DEBUG is set', async () => {
    process.env.LOG_DEBUG = 'true';

    await import('../../src/debug/index.js');

    expect(console.log).toHaveBeenCalledWith('🐞LOG_DEBUG MODE: ', 'true');
  });

  it('should pass multiple arguments to console.log', async () => {
    process.env.LOG_DEBUG = 'true';

    const { logDebug } = await import('../../src/debug/index.js');

    logDebug('message 1', 'message 2', { key: 'value' });

    expect(console.log).toHaveBeenCalledWith('message 1', 'message 2', { key: 'value' });
  });
});
