import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  jsonBasicResponse,
  jsonExtraResponse,
  jsonResponse,
  resolveUrl,
  relativePath,
  fetchJSONBody,
  fetchBody,
  handleFetch,
  handleEmptyFetch,
  replaceParams
} from '../src/fetch';

// Mock global fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

describe('jsonBasicResponse', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should return JSON for successful responses', async () => {
    const mockResponse = {
      ok: true,
      status: 200,
      json: vi.fn().mockResolvedValue({ data: 'test' })
    };
    mockFetch.mockResolvedValue(mockResponse);

    const result = await jsonBasicResponse(fetch('https://example.com'));
    expect(result).toEqual({ data: 'test' });
  });

  it('should throw for non-ok responses', async () => {
    const mockResponse = {
      ok: false,
      url: 'https://example.com',
      status: 404,
      statusText: 'Not Found',
      json: vi.fn().mockResolvedValue({ error: 'Not found' })
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(jsonBasicResponse(fetch('https://example.com'))).rejects.toEqual({
      url: 'https://example.com',
      statusCode: 404,
      message: 'Not Found',
      json: { error: 'Not found' }
    });
  });

  it('should throw for responses with status outside 200-399 range', async () => {
    const mockResponse = {
      ok: true, // This can happen in some fetch implementations
      status: 404,
      json: vi.fn().mockResolvedValue({ error: 'Not found' })
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(jsonBasicResponse(fetch('https://example.com'))).rejects.toEqual({ error: 'Not found' });
  });
});

// We need to mock the JSON_EXTRA_parse function
import * as JSONExtra from '../src/JSON-Extra';

describe('jsonExtraResponse', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    vi.spyOn(JSONExtra, 'JSON_EXTRA_parse').mockImplementation((text) => JSON.parse(text));
  });

  it('should return parsed JSON for successful responses', async () => {
    const mockResponse = {
      ok: true,
      status: 200,
      text: vi.fn().mockResolvedValue('{"data":"test"}')
    };
    mockFetch.mockResolvedValue(mockResponse);

    const result = await jsonExtraResponse(fetch('https://example.com'));
    expect(result).toEqual({ data: 'test' });
  });
});

describe('jsonResponse', () => {
  it('should be an alias for jsonExtraResponse', () => {
    expect(jsonResponse).toBe(jsonExtraResponse);
  });
});

describe('resolveUrl', () => {
  it('should resolve a relative URL against a base URL', () => {
    expect(resolveUrl('https://example.com/path/', 'subpath')).toBe('/path/subpath');
    expect(resolveUrl('https://example.com/path', '/subpath')).toBe('/subpath');
    expect(resolveUrl('https://example.com/path/', '../other')).toBe('/other');
    expect(resolveUrl('https://example.com/path/', '/absolute')).toBe('/absolute');
  });

  it('should include query parameters and hash', () => {
    expect(resolveUrl('https://example.com/path/', 'subpath?query=value#hash')).toBe('/path/subpath?query=value#hash');
  });
});

describe('relativePath', () => {
  it('should return the relative path if subpath starts with owner', () => {
    expect(relativePath('/owner/', '/owner/subpath')).toBe('subpath');
    expect(relativePath('/owner', '/owner/subpath')).toBe('subpath');
  });

  it('should throw if subpath does not start with owner', () => {
    expect(() => relativePath('/owner/', '/different/subpath')).toThrow();
  });
});

describe('fetchJSONBody and fetchBody', () => {
  it('should create a request object with JSON body', () => {
    const body = { key: 'value' };
    const expected = {
      method: 'POST',
      headers: {
        'content-type': 'application/json'
      },
      body: JSON.stringify(body)
    };

    expect(fetchJSONBody('POST', body)).toEqual(expected);
    expect(fetchBody('POST', body)).toEqual(expected);
  });
});

describe('handleFetch', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  it('should return parsed JSON for successful responses', async () => {
    const mockResponse = {
      ok: true,
      text: vi.fn().mockResolvedValue('{"data":"test"}')
    };
    mockFetch.mockResolvedValue(mockResponse);

    const result = await handleFetch(fetch('https://example.com'));
    expect(result).toEqual({ data: 'test' });
  });

  it('should throw a FetchError for non-ok responses', async () => {
    const mockResponse = {
      ok: false,
      status: 404,
      url: 'https://example.com',
      text: vi.fn().mockResolvedValue('{"error":"Not found"}')
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(handleFetch(fetch('https://example.com'))).rejects.toMatchObject({
      message: '❌ Bad Fetch Response.',
      json: { error: 'Not found' },
      statusCode: 404,
      url: 'https://example.com'
    });
  });

  it('should throw if JSON parsing fails', async () => {
    const mockResponse = {
      ok: true,
      url: 'https://example.com',
      text: vi.fn().mockResolvedValue('invalid json')
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(handleFetch(fetch('https://example.com'))).rejects.toThrow();
    expect(console.error).toHaveBeenCalled();
  });
});

describe('handleEmptyFetch', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('should not throw for successful responses', async () => {
    const mockResponse = {
      ok: true
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(handleEmptyFetch(fetch('https://example.com'))).resolves.not.toThrow();
  });

  it('should throw a FetchError for non-ok responses', async () => {
    const mockResponse = {
      ok: false,
      status: 404,
      url: 'https://example.com'
    };
    mockFetch.mockResolvedValue(mockResponse);

    await expect(handleEmptyFetch(fetch('https://example.com'))).rejects.toMatchObject({
      message: '❌ Bad Fetch Response.',
      statusCode: 404,
      url: 'https://example.com'
    });
  });
});

describe('replaceParams', () => {
  it('should replace path parameters with values', () => {
    expect(replaceParams('/users/:id/posts/:postId', { id: '123', postId: '456' }))
      .toBe('/users/123/posts/456');
  });

  it('should handle paths with trailing slash', () => {
    expect(replaceParams('/users/:id/', { id: '123' }))
      .toBe('/users/123/');
  });

  it('should handle paths without trailing slash', () => {
    expect(replaceParams('/users/:id', { id: '123' }))
      .toBe('/users/123');
  });

  it('should throw if no path is provided', () => {
    expect(() => replaceParams('', { id: '123' })).toThrow('Path is required');
  });

  it('should throw if no params are provided', () => {
    expect(() => replaceParams('/users/:id', {})).toThrow('replaceParams not necessary with no params');
  });

  it('should throw if a key is empty', () => {
    expect(() => replaceParams('/users/:id', { '': '123' })).toThrow('key should never be empty');
  });
});
