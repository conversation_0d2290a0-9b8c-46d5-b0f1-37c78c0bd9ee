import { describe, it, expect, vi } from 'vitest';
import { SimpleEmitter } from '../src/event';

describe('SimpleEmitter', () => {
  it('should add listeners with on()', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    emitter.on(listener);
    emitter.emit('test');
    
    expect(listener).toHaveBeenCalledWith('test');
    expect(listener).toHaveBeenCalledTimes(1);
  });
  
  it('should remove listeners with off()', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    emitter.on(listener);
    emitter.emit('test1');
    
    expect(listener).toHaveBeenCalledWith('test1');
    expect(listener).toHaveBeenCalledTimes(1);
    
    emitter.off(listener);
    emitter.emit('test2');
    
    expect(listener).toHaveBeenCalledTimes(1); // Still only called once
  });
  
  it('should return true when off() removes a listener', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    emitter.on(listener);
    const result = emitter.off(listener);
    
    expect(result).toBe(true);
  });
  
  it('should return false when off() does not find a listener', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    const result = emitter.off(listener);
    
    expect(result).toBe(false);
  });
  
  it('should provide a function to remove the listener with onReturnOff()', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    const off = emitter.onReturnOff(listener);
    emitter.emit('test1');
    
    expect(listener).toHaveBeenCalledWith('test1');
    expect(listener).toHaveBeenCalledTimes(1);
    
    off();
    emitter.emit('test2');
    
    expect(listener).toHaveBeenCalledTimes(1); // Still only called once
  });
  
  it('should call a listener only once with once()', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener = vi.fn();
    
    emitter.once(listener);
    emitter.emit('test1');
    
    expect(listener).toHaveBeenCalledWith('test1');
    expect(listener).toHaveBeenCalledTimes(1);
    
    emitter.emit('test2');
    
    expect(listener).toHaveBeenCalledTimes(1); // Still only called once
  });
  
  it('should call all listeners when emit() is called', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener1 = vi.fn();
    const listener2 = vi.fn();
    const listener3 = vi.fn();
    
    emitter.on(listener1);
    emitter.on(listener2);
    emitter.on(listener3);
    
    emitter.emit('test');
    
    expect(listener1).toHaveBeenCalledWith('test');
    expect(listener2).toHaveBeenCalledWith('test');
    expect(listener3).toHaveBeenCalledWith('test');
  });
  
  it('should continue calling listeners even if one throws', () => {
    const emitter = new SimpleEmitter<[string]>();
    const listener1 = vi.fn();
    const listener2 = vi.fn().mockImplementation(() => {
      throw new Error('Test error');
    });
    const listener3 = vi.fn();
    
    emitter.on(listener1);
    emitter.on(listener2);
    emitter.on(listener3);
    
    // This should not throw
    emitter.emit('test');
    
    expect(listener1).toHaveBeenCalledWith('test');
    expect(listener2).toHaveBeenCalledWith('test');
    expect(listener3).toHaveBeenCalledWith('test');
  });
  
  it('should support multiple arguments in emit()', () => {
    const emitter = new SimpleEmitter<[string, number, boolean]>();
    const listener = vi.fn();
    
    emitter.on(listener);
    emitter.emit('test', 42, true);
    
    expect(listener).toHaveBeenCalledWith('test', 42, true);
  });
});
