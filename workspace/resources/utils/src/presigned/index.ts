
export type PresignedConfig = {
  url: string,
  purpose: string,
  expiration: number,
  location: string,
  filename: string,
  byteSize: number,
  userId: string,
};

export type PresignedResult = PresignedConfig & { signiture: string };

export const MAXIMUM_PRESIGNED_UPLOAD_BYTES = 32 * 1_000_000;

export type PresignedCheck = "always" | "check-size" | "never";

export function checkUsePresigned(fileSize: number, check: PresignedCheck){
  switch(check){
    case "always": return true;
    case "never": return false;
    case "check-size": return fileSize >= MAXIMUM_PRESIGNED_UPLOAD_BYTES;
  }
  throw new Error("Bad Presigned Config");
}


