import { J<PERSON><PERSON>_Array, <PERSON><PERSON><PERSON>_Object, JSON_Unknown } from "../types";

export function castToBaseValidType(str: string): JSONShallowBaseValidTypes{
  switch(str){
    case "boolean": return str;
    case "number": return str;
    case "string": return str;
    default: {
      throw new Error("Invalid string: " + str);
    }
  }
}

export function castToObject(un: JSON_Unknown, e?: any): JSON_Object{
  if(typeof un !== "object"){
    throw (e || new Error("Json is not an object"));
  }
  if(un === null){
    throw (e || new Error("Json is null"));
  }
  if(Array.isArray(un)){
    throw (e || new Error("Json is an array"));
  }

  return un;
}

export function castToArray(un: JSON_Unknown, e?: any): JSON_Array{
  if(typeof un !== "object"){
    throw (e || new Error("J<PERSON> is not an object"));
  }
  if(un === null){
    throw (e || new Error("Json is null"));
  }
  if(!Array.isArray(un)){
    throw (e || new Error("Json is not an array"));
  }

  return un;
}

import { JSONShallowBaseValidTypes } from "./types";
export function castArray(
  value: JSON_Unknown,
  key: string,
  schemaType: JSONShallowBaseValidTypes,
  e?: any
){
  const valueType = typeof value;
  if(valueType !== "object"){
    throw (
      typeof e === "undefined" ? new Error(`Json at "${key}" is an object but not expecting one`) :
      typeof e === "function" ? e(`Json at "${key}" is an object but not expecting one`) :
      e
    );
  }
  if(!Array.isArray(value)){
    throw (
      typeof e === "undefined" ? new Error(`Json at "${key}" is expecting an array but got an object`) :
      typeof e === "function" ? e(`Json at "${key}" is expecting an array but got an object`) :
      e
    );
  }
  return value.map((arrayValue, index)=>{
    if(typeof arrayValue !== schemaType){
      throw (
        typeof e === "undefined" ? new Error(`Json at "${key}"[${index}] is not of type "${schemaType}"`) :
        typeof e === "function" ? e(`Json at "${key}"[${index}] is not of type "${schemaType}"`) :
        e
      );
    }
    return arrayValue;
  });
}


export function castBase(
  value: JSON_Unknown,
  key: string,
  schemaType: JSONShallowBaseValidTypes,
  e?: any
){
  const valueType = typeof value;
  if(valueType === "undefined"){
    throw (
      typeof e === "undefined" ? new Error(`Json at "${key}" does not exist`) :
      typeof e === "function" ? e(`Json at "${key}" does not exist`) :
      e
    );
  }
  if(valueType !== schemaType){
    throw (
      typeof e === "undefined" ? new Error(`Json at "${key}" is not of type "${schemaType}"`) :
      typeof e === "function" ? e(`Json at "${key}" is not of type "${schemaType}"`) :
      e
    );
  }
  return value;
}
