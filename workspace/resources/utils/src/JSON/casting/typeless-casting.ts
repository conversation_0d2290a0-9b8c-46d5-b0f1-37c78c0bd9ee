import { JSON_Object, JSON_Unknown } from "../types";
import { isArrayRegExp, isOptionalRegExp } from "./schema-testers";
import { castToBaseValidType, castToObject, castArray, castBase } from "./casters";

type BaseTypeMap = {
  "string": string,
  "number": number,
  "boolean": boolean,
  "string[]": string[],
  "number[]": number[],
  "boolean[]": boolean[],
};

export type SchemaToObject<S extends Record<string, string>> = {
  [K in keyof S]:
    S[K] extends `${infer Base}?`
      ? Base extends keyof BaseTypeMap
        ? BaseTypeMap[Base] | undefined
        : never
      : S[K] extends keyof BaseTypeMap
        ? BaseTypeMap[S[K]]
        : never;
};

type Expand<T> = T extends infer O ? { [K in keyof O]: O[K] } : never;

export function castShallowObject<
  S extends Record<string, keyof BaseTypeMap | `${keyof BaseTypeMap}?`>
>(
  unknown: JSON_Unknown,
  schema: S,
  e?: any
): Expand<SchemaToObject<S>>{
  const value = castToObject(unknown);
  const obj: JSON_Object = {};
  for(const key of Object.keys(schema)){
    const rawType = schema[key];
    const array = isArrayRegExp.exec(rawType);
    const optional = isOptionalRegExp.exec(rawType);
    if(array){
      const [,schemaType] = array;
      obj[key] = castArray(
        value[key],
        key,
        castToBaseValidType(schemaType),
        e
      );
    } else if(optional && typeof value[key] !== "undefined"){
      const [,schemaType] = optional;
      obj[key] = castBase(
        value[key],
        key,
        castToBaseValidType(schemaType),
        e
      );
    } else if(!array && !optional) {
      obj[key] = castBase(
        value[key],
        key,
        castToBaseValidType(rawType),
        e
      );
    }
  }
  return obj as Expand<SchemaToObject<S>>;
}

import { JSONShallowBaseValidTypes } from "./types";
type CastedKey = { key: string, type: JSONShallowBaseValidTypes, extra: "base" | "optional" | "array" };
export function createShallowCaster<
  S extends Record<string, keyof BaseTypeMap | `${keyof BaseTypeMap}?`>
>(schema: S){
  const castableKeys: Array<CastedKey> = Object.entries(schema).map(([key, rawType])=>{
    const array = isArrayRegExp.exec(rawType);
    const optional = isOptionalRegExp.exec(rawType);
    if(array){
      return { key, type: castToBaseValidType(array[1]), extra: "array" };
    }
    if(optional){
      return { key, type: castToBaseValidType(optional[1]), extra: "optional" };
    }
    return { key, type: castToBaseValidType(rawType), extra: "base" };
  });

  return function(unknown: JSON_Unknown, e?: any): Expand<SchemaToObject<S>>{
    const value = castToObject(unknown);
    const obj: JSON_Object = {};
    for(const { key, type: schemaType, extra } of castableKeys){
      if(extra === "array"){
        obj[key] = castArray(value[key], key, schemaType, e);
      } else if(extra === "optional" && typeof value[key] !== "undefined"){
        obj[key] = castBase(value[key], key, schemaType, e);
      } else if(extra === "base"){
        obj[key] = castBase(value[key], key, schemaType, e);
      }
    }
    return obj as Expand<SchemaToObject<S>>;
  };
}
/*
const catsed = castShallowObject("unCastedBody", {
  title: "string",
  description: "number[]",
  chunkingTool: "boolean[]",
  chunkingToolConfig: "string?",
  file: "string[]",
});

catsed.title
catsed.description
catsed.chunkingTool
catsed.chunkingToolConfig
catsed.file
*/
