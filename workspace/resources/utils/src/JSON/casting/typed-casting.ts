import { JSON_Object, JSON_Unknown } from "../types";

import { JSONShallowBaseValidTypes } from "./types";
import { castToBaseValidType, castToObject, castArray, castBase } from "./casters";


import { ShallowObject, ShallowObjectSchema } from "./types";
export { ShallowObject, ShallowObjectSchema };

import { isArrayRegExp, isOptionalRegExp } from "./schema-testers";
type CastedKey = { key: string, type: JSONShallowBaseValidTypes, extra: "base" | "optional" | "array" };
export function createShallowCaster<T extends ShallowObject>(schema: ShallowObjectSchema<T>){
  const castableKeys: Array<CastedKey> = Object.entries(schema).map(([key, rawType])=>{
    const array = isArrayRegExp.exec(rawType);
    const optional = isOptionalRegExp.exec(rawType);
    if(array){
      return { key, type: castToBaseValidType(array[1]), extra: "array" };
    }
    if(optional){
      return { key, type: castToBaseValidType(optional[1]), extra: "optional" };
    }
    return { key, type: castToBaseValidType(rawType), extra: "base" };
  });

  return function(unknown: JSON_Unknown, e?: any): T{
    const value = castToObject(unknown);
    return castableKeys.reduce((obj: JSON_Object, { key, type: schemaType, extra })=>{
      if(extra === "array"){
        obj[key] = castArray(value[key], key, schemaType, e);
      } else if(extra === "optional" && typeof value[key] !== "undefined"){
        obj[key] = castBase(value[key], key, schemaType, e);
      } else if(extra === "base"){
        obj[key] = castBase(value[key], key, schemaType, e);
      }
      return obj;
    }, {}) as T;
  };
}

export function castShallowObject<T extends ShallowObject>(
  unknown: JSON_Unknown, schema: ShallowObjectSchema<T>, e?: any
): T{
  const value = castToObject(unknown);
  const obj: JSON_Object = {};
  for(const key of Object.keys(schema)){
    const rawType = schema[key];
    const array = isArrayRegExp.exec(rawType);
    const optional = isOptionalRegExp.exec(rawType);
    if(array){
      const [,schemaType] = array;
      obj[key] = castArray(
        value[key],
        key,
        castToBaseValidType(schemaType),
        e
      );
    } else if(optional && typeof value[key] !== "undefined"){
      const [,schemaType] = optional;
      obj[key] = castBase(
        value[key],
        key,
        castToBaseValidType(schemaType),
        e
      );
    } else if(!array && !optional) {
      obj[key] = castBase(
        value[key],
        key,
        castToBaseValidType(rawType),
        e
      );
    }
  }
  return obj as T;
}
