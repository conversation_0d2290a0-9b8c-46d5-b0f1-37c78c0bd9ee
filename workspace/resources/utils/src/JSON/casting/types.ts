
export type JSONShallowBaseValidTypes = (
  | "boolean" | "number" | "string"
);

export type JSONShallowValidTypes = (
  JSONShallowBaseValidTypes
  | "boolean?" | "number?" | "string?"
  | "boolean[]" | "number[]" | "string[]"
);

// export type JSONShallowObjectConfig = Record<string, JSONShallowValidTypes>;

// If property is optional (like `string[] | undefined`), append "?"
export type BaseToSchemaString<T> = (
  [Extract<T, undefined>] extends [never] ? ( // does T include `undefined`?
    // property is not optional
    T extends (infer U)[] ? (
      U extends string ? "string[]" :
      U extends number ? "number[]" :
      U extends boolean ? "boolean[]" :
      never
    ) :
    T extends string ? "string" :
    T extends number ? "number" :
    T extends boolean ? "boolean" :
    never
  ) : (
    // property is optional
    T extends string ? "string?" :
    T extends number ? "number?" :
    T extends boolean ? "boolean?"
    : never
  )
);

export type ShallowObject = Record<string, boolean | number | string | boolean[] | number[] | string[]>;

export type ShallowObjectSchema<T extends ShallowObject> = {
  [K in keyof T]: BaseToSchemaString<T[K]>;
};
