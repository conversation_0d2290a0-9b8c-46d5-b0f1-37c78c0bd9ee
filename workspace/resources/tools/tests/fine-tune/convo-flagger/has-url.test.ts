import { describe, it, expect } from 'vitest';
import { hasURL } from '../../../src/fine-tune/convo-flagger/flaggers/has-url';
import { ConversationConfig, ConversationMessage } from '@divinci-ai/models';

describe('hasURL', () => {
  it('should flag messages containing URLs not in the valid origins list', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance with allowed domains
    const flagConfig = { "Valid Origins": ["example.com", "allowed-site.org"] };
    const flagger = hasURL.test(convoConfig, flagConfig);
    
    // Create mock messages with various URLs
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'Check out https://example.com/page' }, // Valid
      { date: 2000, user: 'responder', message: 'Visit https://disallowed-site.com/page' }, // Invalid
      { date: 3000, user: 'prompter', message: 'Look at https://allowed-site.org/page' }, // Valid
      { date: 4000, user: 'responder', message: 'See http://another-bad-site.net/page' }, // Invalid
      { date: 5000, user: 'prompter', message: 'No URL here' }, // No URL
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that only messages with disallowed URLs are flagged
    expect(flaggedIndexes.has(0)).toBe(false); // Valid URL
    expect(flaggedIndexes.has(1)).toBe(true);  // Invalid URL
    expect(flaggedIndexes.has(2)).toBe(false); // Valid URL
    expect(flaggedIndexes.has(3)).toBe(true);  // Invalid URL
    expect(flaggedIndexes.has(4)).toBe(false); // No URL
  });

  it('should not flag any messages when all URLs are in valid origins', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance with allowed domains
    const flagConfig = { "Valid Origins": ["example.com", "allowed-site.org"] };
    const flagger = hasURL.test(convoConfig, flagConfig);
    
    // Create mock messages with only valid URLs
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'Check out https://example.com/page' },
      { date: 2000, user: 'responder', message: 'Visit https://example.com/another-page' },
      { date: 3000, user: 'prompter', message: 'Look at https://allowed-site.org/page' },
      { date: 4000, user: 'responder', message: 'No URL here' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that no messages are flagged
    expect(flaggedIndexes.size).toBe(0);
  });

  it('should handle messages with multiple URLs', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance with allowed domains
    const flagConfig = { "Valid Origins": ["example.com"] };
    const flagger = hasURL.test(convoConfig, flagConfig);
    
    // Create mock messages with multiple URLs
    const messages: ConversationMessage[] = [
      { 
        date: 1000, 
        user: 'prompter', 
        message: 'Check these: https://example.com/page and https://bad-site.com/page' 
      },
      { 
        date: 2000, 
        user: 'responder', 
        message: 'Visit https://example.com/page1 and https://example.com/page2' 
      },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that only the message with a bad URL is flagged
    expect(flaggedIndexes.has(0)).toBe(true);  // Has both valid and invalid URLs
    expect(flaggedIndexes.has(1)).toBe(false); // Has only valid URLs
  });
});
