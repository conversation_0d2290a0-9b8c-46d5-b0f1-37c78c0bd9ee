import { describe, it, expect } from 'vitest';
import { duplicateMessageDate } from '../../../src/fine-tune/convo-flagger/flaggers/duplicate-date';
import { ConversationMessage } from '@divinci-ai/models';

describe('duplicateMessageDate', () => {
  it('should flag messages with duplicate dates', () => {
    // Create test instance
    const flagger = duplicateMessageDate.test({} as any);
    
    // Create mock messages with some duplicate dates
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'user1', message: 'Message 1' },
      { date: 2000, user: 'user2', message: 'Message 2' },
      { date: 1000, user: 'user3', message: 'Message 3' }, // Duplicate date with message 1
      { date: 3000, user: 'user4', message: 'Message 4' },
      { date: 3000, user: 'user5', message: 'Message 5' }, // Duplicate date with message 4
      { date: 4000, user: 'user6', message: 'Message 6' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that messages with duplicate dates are flagged
    expect(flaggedIndexes.has(0)).toBe(true); // First occurrence of date 1000
    expect(flaggedIndexes.has(2)).toBe(true); // Second occurrence of date 1000
    expect(flaggedIndexes.has(3)).toBe(true); // First occurrence of date 3000
    expect(flaggedIndexes.has(4)).toBe(true); // Second occurrence of date 3000
    expect(flaggedIndexes.has(1)).toBe(false); // Unique date 2000
    expect(flaggedIndexes.has(5)).toBe(false); // Unique date 4000
  });

  it('should not flag any messages when all dates are unique', () => {
    // Create test instance
    const flagger = duplicateMessageDate.test({} as any);
    
    // Create mock messages with unique dates
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'user1', message: 'Message 1' },
      { date: 2000, user: 'user2', message: 'Message 2' },
      { date: 3000, user: 'user3', message: 'Message 3' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that no messages are flagged
    expect(flaggedIndexes.size).toBe(0);
  });

  it('should correctly handle multiple duplicates of the same date', () => {
    // Create test instance
    const flagger = duplicateMessageDate.test({} as any);
    
    // Create mock messages with multiple duplicates
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'user1', message: 'Message 1' },
      { date: 1000, user: 'user2', message: 'Message 2' }, // Duplicate with message 1
      { date: 1000, user: 'user3', message: 'Message 3' }, // Duplicate with messages 1 and 2
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that all messages are flagged
    expect(flaggedIndexes.has(0)).toBe(true);
    expect(flaggedIndexes.has(1)).toBe(true);
    expect(flaggedIndexes.has(2)).toBe(true);
    expect(flaggedIndexes.size).toBe(3);
  });
});
