import { describe, it, expect } from 'vitest';
import { hasNoResponse } from '../../../src/fine-tune/convo-flagger/flaggers/no-responder';
import { ConversationConfig, ConversationMessage } from '@divinci-ai/models';

describe('hasNoResponse', () => {
  it('should flag all messages when there is no responder', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = hasNoResponse.test(convoConfig);
    
    // Create mock messages with no responder
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'I am a prompter message 1' },
      { date: 2000, user: 'prompter', message: 'I am a prompter message 2' },
      { date: 3000, user: 'prompter', message: 'I am a prompter message 3' },
      { date: 4000, user: 'other', message: 'I am another user message' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that all messages are flagged
    expect(flaggedIndexes.has(0)).toBe(true);
    expect(flaggedIndexes.has(1)).toBe(true);
    expect(flaggedIndexes.has(2)).toBe(true);
    expect(flaggedIndexes.has(3)).toBe(true);
  });

  it('should not flag any messages when there is a responder', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = hasNoResponse.test(convoConfig);
    
    // Create mock messages with a responder
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'I am a prompter message 1' },
      { date: 2000, user: 'responder', message: 'I am a responder message 1' },
      { date: 3000, user: 'prompter', message: 'I am a prompter message 2' },
      { date: 4000, user: 'other', message: 'I am another user message' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that no messages are flagged
    expect(flaggedIndexes.size).toBe(0);
  });

  it('should flag messages before the first responder message', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = hasNoResponse.test(convoConfig);
    
    // Create mock messages with responder appearing later
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'I am a prompter message 1' },
      { date: 2000, user: 'prompter', message: 'I am a prompter message 2' },
      { date: 3000, user: 'other', message: 'I am another user message' },
      { date: 4000, user: 'responder', message: 'I am a responder message 1' },
      { date: 5000, user: 'prompter', message: 'I am a prompter message 3' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that messages before the responder are flagged
    expect(flaggedIndexes.size).toBe(0); // Once a responder is found, no messages are flagged
  });
});
