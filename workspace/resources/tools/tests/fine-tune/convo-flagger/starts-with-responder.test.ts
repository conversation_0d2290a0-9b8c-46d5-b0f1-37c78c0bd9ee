import { describe, it, expect } from 'vitest';
import { startsWithResponder } from '../../../src/fine-tune/convo-flagger/flaggers/starts-with-responder';
import { ConversationConfig, ConversationMessage } from '@divinci-ai/models';

describe('startsWithResponder', () => {
  it('should flag messages at the beginning that are from the responder', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = startsWithResponder.test(convoConfig);
    
    // Create mock messages starting with responder
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'responder', message: 'I am a responder message 1' },
      { date: 2000, user: 'responder', message: 'I am a responder message 2' },
      { date: 3000, user: 'prompter', message: 'I am a prompter message' },
      { date: 4000, user: 'responder', message: 'I am a responder message 3' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that only the responder messages at the beginning are flagged
    expect(flaggedIndexes.has(0)).toBe(true);
    expect(flaggedIndexes.has(1)).toBe(true);
    expect(flaggedIndexes.has(2)).toBe(false);
    expect(flaggedIndexes.has(3)).toBe(false);
  });

  it('should not flag any messages when conversation starts with prompter', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = startsWithResponder.test(convoConfig);
    
    // Create mock messages starting with prompter
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'prompter', message: 'I am a prompter message 1' },
      { date: 2000, user: 'responder', message: 'I am a responder message 1' },
      { date: 3000, user: 'prompter', message: 'I am a prompter message 2' },
      { date: 4000, user: 'responder', message: 'I am a responder message 2' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that no messages are flagged
    expect(flaggedIndexes.size).toBe(0);
  });

  it('should stop flagging once a prompter message is encountered', () => {
    // Create mock conversation config
    const convoConfig: ConversationConfig = {
      user: {
        prompterId: 'prompter',
        responderId: 'responder'
      },
      thread: { key: 'thread' },
      date: { key: 'date' },
      message: { key: 'message' }
    };
    
    // Create test instance
    const flagger = startsWithResponder.test(convoConfig);
    
    // Create mock messages with responder at start, then prompter, then responder again
    const messages: ConversationMessage[] = [
      { date: 1000, user: 'responder', message: 'I am a responder message 1' },
      { date: 2000, user: 'responder', message: 'I am a responder message 2' },
      { date: 3000, user: 'prompter', message: 'I am a prompter message' },
      { date: 4000, user: 'responder', message: 'I am a responder message 3' },
      { date: 5000, user: 'responder', message: 'I am a responder message 4' },
    ];
    
    // Add messages to the flagger
    messages.forEach((message, index) => {
      flagger.addItem(index, message);
    });
    
    // Get flagged indexes
    const flaggedIndexes = flagger.getFlaggedIndexes();
    
    // Check that only the responder messages at the beginning are flagged
    expect(flaggedIndexes.has(0)).toBe(true);
    expect(flaggedIndexes.has(1)).toBe(true);
    expect(flaggedIndexes.has(2)).toBe(false);
    expect(flaggedIndexes.has(3)).toBe(false);
    expect(flaggedIndexes.has(4)).toBe(false);
  });
});
