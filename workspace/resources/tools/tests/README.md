# Tools Tests

This directory contains tests for the Tools package.

## Test Structure

- `unit/`: Unit tests for individual tool functions
- `integration/`: Integration tests for tool modules

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/tools/format` resolves to `src/tools/format`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { formatTool } from '@/tools/format';

// Import from tests directory
import { mockTool } from '~/helpers/mock-tool';
```

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## Writing Tests

When writing tests, follow these guidelines:

1. Use Vitest's testing functions (`describe`, `it`, `expect`, etc.)
2. Use path aliases for imports when possible
3. Test both success and error cases
4. Keep tests focused and small

### Example Test

```typescript
import { describe, it, expect } from 'vitest';
import { formatTool } from '@/tools/format';

describe('formatTool', () => {
  it('should format a tool correctly', () => {
    const tool = {
      name: 'test-tool',
      version: '1.0.0'
    };
    
    const result = formatTool(tool);
    expect(result).toEqual({
      name: 'test-tool',
      version: '1.0.0',
      formatted: true
    });
  });

  it('should handle missing version', () => {
    const tool = {
      name: 'test-tool'
    };
    
    const result = formatTool(tool);
    expect(result).toEqual({
      name: 'test-tool',
      version: 'unknown',
      formatted: true
    });
  });
});
```
