import { describe, it, expect, vi } from "vitest";
import { crawlUrl } from "../../../src/rag/crawl/crawl-promise-recursive";
import { RagCrawlerOptions, RagScraperResult } from "@divinci-ai/models";

describe("crawlUrl", ()=>{
  it("should crawl a URL and its linked pages", async ()=>{
    // Mock scraper function
    const mockScraper = vi.fn().mockImplementation((url: string): Promise<RagScraperResult>=>{
      if(url === "https://example.com/") {
        return Promise.resolve({
          url: "https://example.com/",
          html: "<html><body>Main page</body></html>",
          markdown: "Main page",
          links: ["https://example.com/page1", "https://example.com/page2"]
        });
      } else if(url === "https://example.com/page1") {
        return Promise.resolve({
          url: "https://example.com/page1",
          html: "<html><body>Page 1</body></html>",
          markdown: "Page 1",
          links: ["https://example.com/page3"]
        });
      } else if(url === "https://example.com/page2") {
        return Promise.resolve({
          url: "https://example.com/page2",
          html: "<html><body>Page 2</body></html>",
          markdown: "Page 2",
          links: []
        });
      } else if(url === "https://example.com/page3") {
        return Promise.resolve({
          url: "https://example.com/page3",
          html: "<html><body>Page 3</body></html>",
          markdown: "Page 3",
          links: []
        });
      }
      throw new Error(`Unexpected URL: ${url}`);
    });

    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };

    // Crawl the URL
    const result = await crawlUrl("https://example.com/", mockScraper, options);

    // Check the result
    expect(result.pages).toHaveLength(4);
    expect(result.errors).toHaveLength(0);

    // Check that all pages were crawled
    const urls = result.pages.map(page=>page.url);
    expect(urls).toContain("https://example.com/");
    expect(urls).toContain("https://example.com/page1");
    expect(urls).toContain("https://example.com/page2");
    expect(urls).toContain("https://example.com/page3");

    // Check that the scraper was called for each URL
    expect(mockScraper).toHaveBeenCalledTimes(4);
    expect(mockScraper).toHaveBeenCalledWith("https://example.com/");
    expect(mockScraper).toHaveBeenCalledWith("https://example.com/page1");
    expect(mockScraper).toHaveBeenCalledWith("https://example.com/page2");
    expect(mockScraper).toHaveBeenCalledWith("https://example.com/page3");
  });

  it("should respect the limit option", async ()=>{
    // Mock scraper function that returns many links
    const mockScraper = vi.fn().mockImplementation((url: string): Promise<RagScraperResult>=>{
      return Promise.resolve({
        url,
        html: "<html><body>Content</body></html>",
        markdown: "Content",
        links: [
          "https://example.com/page1",
          "https://example.com/page2",
          "https://example.com/page3",
          "https://example.com/page4",
          "https://example.com/page5"
        ]
      });
    });

    // Create mock options with a low limit
    const options: RagCrawlerOptions = {
      limit: 3, // Only crawl 3 pages
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };

    // Crawl the URL
    const result = await crawlUrl("https://example.com/", mockScraper, options);

    // Check the result
    expect(result.pages).toHaveLength(3); // Should only have 3 pages due to limit
    expect(result.errors).toHaveLength(0);

    // Check that the scraper was called at most 3 times
    expect(mockScraper).toHaveBeenCalledTimes(3);
  });

  it("should handle scraper errors", async ()=>{
    // Mock scraper function that sometimes fails
    const mockScraper = vi.fn().mockImplementation((url: string): Promise<RagScraperResult>=>{
      if(url === "https://example.com/") {
        return Promise.resolve({
          url: "https://example.com/",
          html: "<html><body>Main page</body></html>",
          markdown: "Main page",
          links: ["https://example.com/good-page", "https://example.com/error-page"]
        });
      } else if(url === "https://example.com/good-page") {
        return Promise.resolve({
          url: "https://example.com/good-page",
          html: "<html><body>Good page</body></html>",
          markdown: "Good page",
          links: []
        });
      } else if(url === "https://example.com/error-page") {
        return Promise.reject(new Error("Failed to fetch page"));
      }
      throw new Error(`Unexpected URL: ${url}`);
    });

    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };

    // Crawl the URL
    const result = await crawlUrl("https://example.com/", mockScraper, options);

    // Check the result
    expect(result.pages).toHaveLength(2); // Should have 2 successful pages
    expect(result.errors).toHaveLength(1); // Should have 1 error

    // Check the successful pages
    const urls = result.pages.map(page=>page.url);
    expect(urls).toContain("https://example.com/");
    expect(urls).toContain("https://example.com/good-page");

    // Check the error
    expect(result.errors[0].url).toBe("https://example.com/error-page");
    expect(result.errors[0].error).toBe("Failed to fetch page");
  });

  it("should handle URL redirects", async ()=>{
    // Mock scraper function that handles redirects
    const mockScraper = vi.fn().mockImplementation((url: string): Promise<RagScraperResult>=>{
      if(url === "https://example.com/") {
        return Promise.resolve({
          url: "https://example.com/", // No redirect
          html: "<html><body>Main page</body></html>",
          markdown: "Main page",
          links: ["https://example.com/redirect"]
        });
      } else if(url === "https://example.com/redirect") {
        return Promise.resolve({
          url: "https://example.com/redirected", // URL after redirect
          html: "<html><body>Redirected page</body></html>",
          markdown: "Redirected page",
          links: []
        });
      }
      throw new Error(`Unexpected URL: ${url}`);
    });

    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };

    // Crawl the URL
    const result = await crawlUrl("https://example.com/", mockScraper, options);

    // Check the result
    expect(result.pages).toHaveLength(2);
    expect(result.errors).toHaveLength(0);

    // Check that the redirected URL is in the result
    const urls = result.pages.map(page=>page.url);
    expect(urls).toContain("https://example.com/");
    expect(urls).toContain("https://example.com/redirected");
    expect(urls).not.toContain("https://example.com/redirect"); // Original URL before redirect
  });
});
