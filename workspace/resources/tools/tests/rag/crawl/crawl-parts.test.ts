import { describe, it, expect } from 'vitest';
import { canAddLink, CrawlState } from '../../../src/rag/crawl/crawl-parts';
import { RagCrawlerOptions } from '@divinci-ai/models';

describe('canAddLink', () => {
  it('should return true for valid links within the same origin', () => {
    // Create mock state
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set(['https://example.com/start'])
    };
    
    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };
    
    // Test valid links
    expect(canAddLink(state, options, 'https://example.com/page1')).toBe(true);
    expect(canAddLink(state, options, '/page2')).toBe(true);
    expect(canAddLink(state, options, './page3')).toBe(true);
    expect(canAddLink(state, options, '../page4')).toBe(true);
  });

  it('should return false for links that have already been consumed', () => {
    // Create mock state with consumed URLs
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set([
        'https://example.com/start',
        'https://example.com/page1',
        'https://example.com/page2'
      ])
    };
    
    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };
    
    // Test consumed links
    expect(canAddLink(state, options, 'https://example.com/page1')).toBe(false);
    expect(canAddLink(state, options, 'https://example.com/page2')).toBe(false);
    expect(canAddLink(state, options, 'https://example.com/page3')).toBe(true); // Not consumed
  });

  it('should return false for links from different origins', () => {
    // Create mock state
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set(['https://example.com/start'])
    };
    
    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };
    
    // Test links from different origins
    expect(canAddLink(state, options, 'https://different-domain.com/page')).toBe(false);
    expect(canAddLink(state, options, 'http://example.com/page')).toBe(false); // Different protocol
  });

  it('should respect excludePaths option', () => {
    // Create mock state
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set(['https://example.com/start'])
    };
    
    // Create mock options with excluded paths
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: ['/admin/*', '/private/*', '/users/*/settings'],
      allowedPaths: []
    };
    
    // Test excluded paths
    expect(canAddLink(state, options, 'https://example.com/admin/dashboard')).toBe(false);
    expect(canAddLink(state, options, 'https://example.com/private/data')).toBe(false);
    expect(canAddLink(state, options, 'https://example.com/users/john/settings')).toBe(false);
    
    // Test non-excluded paths
    expect(canAddLink(state, options, 'https://example.com/public/page')).toBe(true);
    expect(canAddLink(state, options, 'https://example.com/users/john/profile')).toBe(true);
  });

  it('should respect allowedPaths option', () => {
    // Create mock state
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set(['https://example.com/start'])
    };
    
    // Create mock options with allowed paths
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: ['/blog/*', '/products/*']
    };
    
    // Test allowed paths
    expect(canAddLink(state, options, 'https://example.com/blog/post1')).toBe(true);
    expect(canAddLink(state, options, 'https://example.com/products/item1')).toBe(true);
    
    // Test non-allowed paths
    expect(canAddLink(state, options, 'https://example.com/about')).toBe(false);
    expect(canAddLink(state, options, 'https://example.com/contact')).toBe(false);
  });

  it('should handle URLs with query parameters and hash fragments', () => {
    // Create mock state
    const state: CrawlState = {
      baseUrl: new URL('https://example.com/start'),
      consumedUrls: new Set(['https://example.com/page'])
    };
    
    // Create mock options
    const options: RagCrawlerOptions = {
      limit: 10,
      runSitemap: false,
      excludePaths: [],
      allowedPaths: []
    };
    
    // Test URLs with query parameters and hash fragments
    expect(canAddLink(state, options, 'https://example.com/page?param=value')).toBe(false); // Same as consumed URL after removing query
    expect(canAddLink(state, options, 'https://example.com/page#section')).toBe(false); // Same as consumed URL after removing hash
    expect(canAddLink(state, options, 'https://example.com/different-page?param=value')).toBe(true); // Different page
  });
});
