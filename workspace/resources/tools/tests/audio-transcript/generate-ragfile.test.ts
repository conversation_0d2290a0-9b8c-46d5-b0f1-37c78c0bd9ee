import { describe, it, expect } from 'vitest';
import { audioTranscriptToRagFile } from '../../src/audio-transcript/generate-ragfile';
import { AudioTranscriptProps } from '@divinci-ai/models';

describe('audioTranscriptToRagFile', () => {
  it('should generate a RAG file from audio transcript with selected speakers', () => {
    // Mock audio transcript data
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: 'speaker1', transcript: 'Hello, how are you?', start: 0, end: 3 },
        { speaker: 'speaker2', transcript: 'I am fine, thank you.', start: 3, end: 6 },
        { speaker: 'speaker1', transcript: 'What are you working on?', start: 6, end: 9 },
        { speaker: 'speaker3', transcript: 'Just some coding.', start: 9, end: 12 },
      ]
    };

    // Test with all speakers
    const allSpeakers = ['speaker1', 'speaker2', 'speaker3'];
    const resultAll = audioTranscriptToRagFile(mockAudioTranscript, allSpeakers);
    expect(resultAll).toContain('Hello, how are you?');
    expect(resultAll).toContain('I am fine, thank you.');
    expect(resultAll).toContain('What are you working on?');
    expect(resultAll).toContain('Just some coding.');

    // Test with selected speakers
    const selectedSpeakers = ['speaker1', 'speaker3'];
    const resultSelected = audioTranscriptToRagFile(mockAudioTranscript, selectedSpeakers);
    expect(resultSelected).toContain('Hello, how are you?');
    expect(resultSelected).not.toContain('I am fine, thank you.');
    expect(resultSelected).toContain('What are you working on?');
    expect(resultSelected).toContain('Just some coding.');
  });

  it('should throw an error if no speakers are found in the transcript', () => {
    // Mock audio transcript data with no matching speakers
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: 'speaker1', transcript: 'Hello, how are you?', start: 0, end: 3 },
        { speaker: 'speaker2', transcript: 'I am fine, thank you.', start: 3, end: 6 },
      ]
    };

    // Test with non-matching speakers
    const nonMatchingSpeakers = ['speaker3', 'speaker4'];
    expect(() => audioTranscriptToRagFile(mockAudioTranscript, nonMatchingSpeakers))
      .toThrow('No speakers found in transcript');
  });

  it('should join transcripts with double newlines', () => {
    // Mock audio transcript data
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: 'speaker1', transcript: 'First line.', start: 0, end: 3 },
        { speaker: 'speaker1', transcript: 'Second line.', start: 3, end: 6 },
      ]
    };

    // Test with matching speakers
    const speakers = ['speaker1'];
    const result = audioTranscriptToRagFile(mockAudioTranscript, speakers);
    expect(result).toBe('First line.\n\nSecond line.');
  });
});
