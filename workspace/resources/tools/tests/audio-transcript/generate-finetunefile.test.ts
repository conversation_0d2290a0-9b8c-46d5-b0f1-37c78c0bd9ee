import { describe, it, expect } from "vitest";
import { audioTranscriptToFineTuneFile } from "../../src/audio-transcript/generate-finetunefile";
import { AudioTranscriptProps } from "@divinci-ai/models";

describe("audioTranscriptToFineTuneFile", ()=>{
  it("should generate a fine-tune file from audio transcript with prompters and responders", ()=>{
    // Mock audio transcript data with alternating speakers
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: "prompter1", transcript: "What is the capital of France?", start: 0, end: 3 },
        { speaker: "responder1", transcript: "The capital of France is Paris.", start: 3, end: 6 },
        { speaker: "prompter1", transcript: "What is the capital of Italy?", start: 6, end: 9 },
        { speaker: "responder1", transcript: "The capital of Italy is Rome.", start: 9, end: 12 },
      ]
    };

    // Test with prompters and responders
    const prompters = ["prompter1"];
    const responders = ["responder1"];
    const result = audioTranscriptToFineTuneFile(mockAudioTranscript, prompters, responders);

    expect(result).toHaveLength(2);
    expect(result[0].prompt).toBe("What is the capital of France?");
    expect(result[0].response).toBe("The capital of France is Paris.");
    expect(result[1].prompt).toBe("What is the capital of Italy?");
    expect(result[1].response).toBe("The capital of Italy is Rome.");
  });

  it("should handle multiple consecutive prompts or responses", ()=>{
    // Mock audio transcript data with consecutive prompts and responses
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: "prompter1", transcript: "First question part 1.", start: 0, end: 3 },
        { speaker: "prompter1", transcript: "First question part 2.", start: 3, end: 6 },
        { speaker: "responder1", transcript: "First answer part 1.", start: 6, end: 9 },
        { speaker: "responder1", transcript: "First answer part 2.", start: 9, end: 12 },
        { speaker: "prompter1", transcript: "Second question.", start: 12, end: 15 },
        { speaker: "responder1", transcript: "Second answer.", start: 15, end: 18 },
      ]
    };

    // Test with prompters and responders
    const prompters = ["prompter1"];
    const responders = ["responder1"];
    const result = audioTranscriptToFineTuneFile(mockAudioTranscript, prompters, responders);

    expect(result).toHaveLength(2);
    expect(result[0].prompt).toBe("First question part 1.\n\nFirst question part 2.");
    expect(result[0].response).toBe("First answer part 1.\n\nFirst answer part 2.");
    expect(result[1].prompt).toBe("Second question.");
    expect(result[1].response).toBe("Second answer.");
  });

  it("should throw an error if no prompters are found", ()=>{
    // Mock audio transcript data with no matching prompters
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: "speaker1", transcript: "Hello, how are you?", start: 0, end: 3 },
        { speaker: "speaker2", transcript: "I am fine, thank you.", start: 3, end: 6 },
      ]
    };

    // Test with non-matching prompters
    const nonMatchingPrompters = ["prompter1"];
    const responders = ["speaker2"];
    expect(()=>audioTranscriptToFineTuneFile(mockAudioTranscript, nonMatchingPrompters, responders))
      .toThrow("None of the prompters were found");
  });

  it("should ignore speakers that are neither prompters nor responders", ()=>{
    // Mock audio transcript data with extra speakers
    const mockAudioTranscript: AudioTranscriptProps = {
      samples: [
        { speaker: "prompter1", transcript: "What is the capital of France?", start: 0, end: 3 },
        { speaker: "extra", transcript: "Irrelevant comment.", start: 3, end: 6 },
        { speaker: "responder1", transcript: "The capital of France is Paris.", start: 6, end: 9 },
        { speaker: "prompter1", transcript: "What is the capital of Italy?", start: 9, end: 12 },
        { speaker: "extra", transcript: "Another irrelevant comment.", start: 12, end: 15 },
        { speaker: "responder1", transcript: "The capital of Italy is Rome.", start: 15, end: 18 },
      ]
    };

    // Test with prompters and responders
    const prompters = ["prompter1"];
    const responders = ["responder1"];
    const result = audioTranscriptToFineTuneFile(mockAudioTranscript, prompters, responders);

    expect(result).toHaveLength(2);
    expect(result[0].prompt).toBe("What is the capital of France?");
    expect(result[0].response).toBe("The capital of France is Paris.");
    expect(result[1].prompt).toBe("What is the capital of Italy?");
    expect(result[1].response).toBe("The capital of Italy is Rome.");
  });
});
