import { describe, it, expect } from 'vitest';
import { 
  NANO_MULTIPLIER, 
  amountPartsToSingle, 
  amountSingleToParts, 
  amountToBalanceUSD 
} from '../../src/shared-config/money/constants';

describe('Money utilities', () => {
  describe('NANO_MULTIPLIER', () => {
    it('should be 10^9 as a BigInt', () => {
      expect(NANO_MULTIPLIER).toBe(1000000000n);
    });
  });

  describe('amountPartsToSingle', () => {
    it('should convert USD and nano parts to a single nano amount', () => {
      // Test with positive values
      expect(amountPartsToSingle({ usd: 5n, nano: 500000000n })).toBe(5500000000n);
      expect(amountPartsToSingle({ usd: 0n, nano: 750000000n })).toBe(750000000n);
      expect(amountPartsToSingle({ usd: 10n, nano: 0n })).toBe(10000000000n);
      
      // Test with negative values
      expect(amountPartsToSingle({ usd: -3n, nano: -500000000n })).toBe(-3500000000n);
      expect(amountPartsToSingle({ usd: -1n, nano: 0n })).toBe(-1000000000n);
    });
  });

  describe('amountSingleToParts', () => {
    it('should convert a single nano amount to USD and nano parts', () => {
      // Test with positive values
      expect(amountSingleToParts(5500000000n)).toEqual({ usd: 5n, nano: 500000000n });
      expect(amountSingleToParts(750000000n)).toEqual({ usd: 0n, nano: 750000000n });
      expect(amountSingleToParts(10000000000n)).toEqual({ usd: 10n, nano: 0n });
      
      // Test with negative values
      expect(amountSingleToParts(-3500000000n)).toEqual({ usd: -3n, nano: -500000000n });
      expect(amountSingleToParts(-1000000000n)).toEqual({ usd: -1n, nano: 0n });
    });

    it('should handle zero correctly', () => {
      expect(amountSingleToParts(0n)).toEqual({ usd: 0n, nano: 0n });
    });

    it('should be the inverse of amountPartsToSingle', () => {
      const testCases = [
        { usd: 5n, nano: 500000000n },
        { usd: 0n, nano: 750000000n },
        { usd: 10n, nano: 0n },
        { usd: -3n, nano: -500000000n },
        { usd: -1n, nano: 0n }
      ];
      
      for (const parts of testCases) {
        const single = amountPartsToSingle(parts);
        const roundTrip = amountSingleToParts(single);
        expect(roundTrip).toEqual(parts);
      }
    });
  });

  describe('amountToBalanceUSD', () => {
    it('should convert nano amount to USD balance with 2 decimal places', () => {
      // Test with various values
      expect(amountToBalanceUSD(1000000000n)).toBe(1); // 1 USD
      expect(amountToBalanceUSD(1500000000n)).toBe(1.5); // 1.5 USD
      expect(amountToBalanceUSD(1234567890n)).toBe(1.23); // 1.23 USD (rounded)
      expect(amountToBalanceUSD(50000000n)).toBe(0.05); // 0.05 USD
      expect(amountToBalanceUSD(0n)).toBe(0); // 0 USD
      
      // Test with negative values
      expect(amountToBalanceUSD(-1000000000n)).toBe(-1); // -1 USD
      expect(amountToBalanceUSD(-1500000000n)).toBe(-1.5); // -1.5 USD
    });
  });
});
