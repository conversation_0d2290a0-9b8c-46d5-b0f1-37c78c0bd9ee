import { describe, it, expect } from 'vitest';
import { RAG_RAW_TO_CHUNKS } from '../../src/shared-config/tools/rag';
import { AUDIO_TRANSCRIPTION } from '../../src/shared-config/tools/audio';
import { ToolAvailableStatus } from '../../src/shared-config/types';

describe('Shared Config Tools', () => {
  describe('RAG_RAW_TO_CHUNKS', () => {
    it('should export RAG tools with correct structure', () => {
      // Check that RAG_RAW_TO_CHUNKS is an object
      expect(RAG_RAW_TO_CHUNKS).toBeDefined();
      expect(typeof RAG_RAW_TO_CHUNKS).toBe('object');
      
      // Check that it has at least one tool
      expect(Object.keys(RAG_RAW_TO_CHUNKS).length).toBeGreaterThan(0);
      
      // Check the structure of each tool
      for (const [id, tool] of Object.entries(RAG_RAW_TO_CHUNKS)) {
        expect(tool.id).toBe(id);
        expect(tool.status).toBeDefined();
        expect(Object.values(ToolAvailableStatus)).toContain(tool.status);
        
        // Check info structure
        expect(tool.info).toBeDefined();
        expect(tool.info.id).toBe(id);
        expect(tool.info.title).toBeDefined();
        expect(tool.info.description).toBeDefined();
        expect(tool.info.url).toBeDefined();
        expect(tool.info.org).toBeDefined();
        expect(tool.info.orgUrl).toBeDefined();
        
        // Check pricing
        expect(tool.pricing).toBeDefined();
        expect(typeof tool.pricing.getEstimatedCost).toBe('function');
        expect(typeof tool.pricing.getFinalCost).toBe('function');
        expect(typeof tool.pricing.calculateTax).toBe('function');
        
        // Check input config if present
        if (tool.inputConfig) {
          expect(tool.inputConfig.schema).toBeDefined();
          expect(tool.inputConfig.default).toBeDefined();
          expect(typeof tool.inputConfig.validate).toBe('function');
        }
      }
    });
  });

  describe('AUDIO_TRANSCRIPTION', () => {
    it('should export audio transcription tools with correct structure', () => {
      // Check that AUDIO_TRANSCRIPTION is an object
      expect(AUDIO_TRANSCRIPTION).toBeDefined();
      expect(typeof AUDIO_TRANSCRIPTION).toBe('object');
      
      // Check that it has at least one tool
      expect(Object.keys(AUDIO_TRANSCRIPTION).length).toBeGreaterThan(0);
      
      // Check the structure of each tool
      for (const [id, tool] of Object.entries(AUDIO_TRANSCRIPTION)) {
        expect(tool.id).toBe(id);
        expect(tool.status).toBeDefined();
        expect(Object.values(ToolAvailableStatus)).toContain(tool.status);
        
        // Check info structure
        expect(tool.info).toBeDefined();
        expect(tool.info.id).toBe(id);
        expect(tool.info.title).toBeDefined();
        expect(tool.info.description).toBeDefined();
        expect(tool.info.url).toBeDefined();
        expect(tool.info.org).toBeDefined();
        expect(tool.info.orgUrl).toBeDefined();
        
        // Check pricing
        expect(tool.pricing).toBeDefined();
        expect(typeof tool.pricing.getEstimatedCost).toBe('function');
        expect(typeof tool.pricing.getFinalCost).toBe('function');
        expect(typeof tool.pricing.calculateTax).toBe('function');
      }
    });
  });
});
