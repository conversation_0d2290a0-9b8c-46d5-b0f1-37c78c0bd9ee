import { AudioTranscriptProps } from "@divinci-ai/models";

export function audioTranscriptToRagFile(
  doc: AudioTranscriptProps,
  speakers: Array<string>
){
  const speakerSet = new Set(speakers);
  const transcript: Array<string> = [];
  for(const sample of doc.samples){
    if(!speakerSet.has(sample.speaker)) continue;
    transcript.push(sample.transcript);
  }

  if(transcript.length === 0){
    throw new Error("No speakers found in transcript");
  }

  return transcript.join("\n\n");
}
