
import { AudioTranscriptProps } from "@divinci-ai/models";

enum TranscriptStatus {
  prompt,
  response,
}

export function audioTranscriptToFineTuneFile(
  doc: AudioTranscriptProps,
  prompterList: Array<string>, responderList: Array<string>
){
  const samples = doc.samples.sort((a, b)=>(a.start - b.start));
  const prompters = new Set(prompterList);
  const responders = new Set(responderList);

  const transcript: Array<{ prompt: string, response: string }> = [];
  let status = TranscriptStatus.prompt;
  const activePrompt: Array<string> = [];
  const activeResponse: Array<string> = [];
  let index = 0;
  for(; index < samples.length; index++){
    if(prompters.has(samples[index].speaker)) break;
  }
  if(index >= samples.length){
    throw new Error("None of the prompters were found");
  }
  activePrompt.push(samples[index].transcript);
  index++;
  if(index >= samples.length){
    throw new Error("Ended without response");
  }

  for(; index < samples.length; index++){
    const sample = samples[index];
    if(status === TranscriptStatus.prompt){
      if(prompters.has(sample.speaker)){
        activePrompt.push(sample.transcript);
        continue;
      }
      if(!responders.has(sample.speaker)){
        continue;
      }
      status = TranscriptStatus.response;
      activeResponse.push(sample.transcript);
      continue;
    }
    if(prompters.has(sample.speaker)){
      transcript.push({
        prompt: activePrompt.join("\n\n"),
        response: activeResponse.join("\n\n"),
      });
      activePrompt.length = 0;
      activeResponse.length = 0;
      status = TranscriptStatus.prompt;
      activePrompt.push(sample.transcript);
      continue;
    }
    if(!responders.has(sample.speaker)){
      continue;
    }
    status = TranscriptStatus.response;
    activeResponse.push(sample.transcript);
  }

  if(status === TranscriptStatus.response){
    transcript.push({
      prompt: activePrompt.join("\n\n"),
      response: activeResponse.join("\n\n"),
    });
  }

  if(transcript.length === 0){
    throw new Error("No speakers found in transcript");
  }

  return transcript;
}
