import { parseUrl } from "./util/error";
import { resolveSitemap } from "./util/sitemap";
import { canAddLink } from "./crawl-parts";

import { RagCrawlerOptions, RagScraperResult, RagCrawlerResult } from "@divinci-ai/models";


export const crawlUrl = async function(
  initialUrl: string,
  scrapeUrl: (url: string)=>Promise<RagScraperResult>,
  options: RagCrawlerOptions
): Promise<RagCrawlerResult> {
  const { limit } = options;
  const baseUrl = parseUrl(initialUrl);

  const consumedUrls = new Set<string>();
  const urlResults: RagCrawlerResult["pages"] = [];
  const urlErrors: RagCrawlerResult["errors"] = [];

  await consumeUrl(initialUrl);

  if(consumedUrls.size < limit && options.runSitemap){
    try {
      const sitemapUrls = await resolveSitemap(baseUrl.href);
      console.log("sitemapUrls:", sitemapUrls);
      await handleNewUrls(sitemapUrls);
    }catch(e){
      console.log("❌ Failed to resolve sitemap", e);
    }
  }

  async function consumeUrl(url: string){
    consumedUrls.add(url);
    try {
      const { url: urlAfterRedirect, html, markdown, links } = await scrapeUrl(url);
      if(url !== urlAfterRedirect && consumedUrls.has(urlAfterRedirect)) return;
      urlResults.push({ url: urlAfterRedirect, html, markdown, links });
      await handleNewUrls(links);
    }catch(e){
      console.log("❌ Failed to fetch", e);
      urlErrors.push({ url, error: (e as Error).message });
    }
  }

  async function handleNewUrls(urls: Array<string>){
    const promises: Array<Promise<any>> = [];
    for(const link of urls){
      if(consumedUrls.size >= limit) break;
      if(!canAddLink({ baseUrl, consumedUrls }, options, link)) continue;
      promises.push(consumeUrl(link));
    }
    await Promise.all(promises);
  }

  return { pages: urlResults, errors: urlErrors };

};

