import { Readable } from "stream";
import { parseUrl } from "./util/error";
import { resolveSitemap } from "./util/sitemap";

import { RagCrawlerOptions, RagScraperResult } from "@divinci-ai/models";
import { canAddLink } from "./crawl-parts";

export class CrawlUrlStream extends Readable {
  private state: { baseUrl: URL, consumedUrls: Set<string> };

  constructor(
    initialUrl: string,
    private scrapeUrl: (url: string)=>Promise<RagScraperResult>,
    private options: RagCrawlerOptions
  ){
    super({ objectMode: true });
    this.state = { baseUrl: parseUrl(initialUrl), consumedUrls: new Set<string>() };
    this.consumeUrl(initialUrl);
    if(this.options.runSitemap) this.handleSitemap();

  }

  async handleSitemap(){
    try {
      if(this.state.consumedUrls.size >= this.options.limit) return;
      const sitemapUrls = await resolveSitemap(this.state.baseUrl.href);
      console.log("sitemapUrls:", sitemapUrls);
      this.handleNewUrls(sitemapUrls);
    }catch(e){
      console.log("❌ Failed to resolve sitemap", e);
    }
  }

  async consumeUrl(url: string){
    this.state.consumedUrls.add(url);
    try {
      const { url: urlAfterRedirect, html, markdown, links } = await this.scrapeUrl(url);
      if(url !== urlAfterRedirect && this.state.consumedUrls.has(urlAfterRedirect)) return;
      this.push({ type: "page", value: { url: urlAfterRedirect, html, markdown, links } });
      this.handleNewUrls(links);
    }catch(e){
      console.log("❌ Failed to fetch", e);
      this.push({ type: "error", value: { url, error: (e as Error).message } });
    }
  }

  handleNewUrls(urls: Array<string>){
    for(const link of urls){
      if(this.readableEnded) return;
      if(this.state.consumedUrls.size >= this.options.limit){
        return this.push(null);
      }
      if(!canAddLink(this.state, this.options, link)) continue;
      this.consumeUrl(link);
    }
  }
}

