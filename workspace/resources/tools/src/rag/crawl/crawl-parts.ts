
import { RagCrawlerOptions } from "@divinci-ai/models";
import { pathMatches } from "./util/pathmatch";

export type CrawlState = { baseUrl: URL, consumedUrls: Set<string> };

export function canAddLink(state: CrawlState, options: RagCrawlerOptions, link: string){
  try {
    const linkUrl = new URL(link, state.baseUrl);
    linkUrl.search = "";
    linkUrl.hash = "";
    if(state.consumedUrls.has(linkUrl.href)) return false;
    if(linkUrl.origin !== state.baseUrl.origin) return false;
    // if link is matches an excluded patter, skip
    if(pathMatches(linkUrl, false, options.excludePaths)) return false;
    // if link does not match an allowed pattern, skip
    // if no allowed patterns, then all are allowed
    if(!pathMatches(linkUrl, true, options.allowedPaths)) return false;
    return true;
  }catch(e){
    console.error("❌ Failed to add link to crawl", e);
  }
}
