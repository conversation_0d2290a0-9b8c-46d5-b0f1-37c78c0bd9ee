import { JSON_Unknown } from "@divinci-ai/utils";

export class DivinciCrawlError extends Error {
  name = "CrawlError";
  constructor(public url: string, message: string, public info: JSON_Unknown) {
    super(message);
  }
}

export function parseUrl(url: string){
  try {
    return new URL(url);
  }catch(e){
    throw new DivinciCrawlError(
      url, `Failed to parse URL`, { error: (e as Error).message }
    );
  }
}
