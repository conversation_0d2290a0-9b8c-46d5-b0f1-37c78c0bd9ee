// We'll use BigInt for prices, but you could use Decimal or another precise type.

export interface PricingStrategy<InputArgs, OutputResult> {
  /**
   * Calculate the necessary cost for input usage,
   * plus how much to escrow for the output usage if needed.
   *
   * @param input - Data required to estimate the cost, such as file size, number of pages,
   *   or worst-case token usage.
   * @returns An object containing:
   *   - inputCost: The fixed cost charged immediately for the input usage.
   *   - outputEscrow: The maximum or estimated cost to escrow for variable output usage.
   */
  getEstimatedCost(input: InputArgs): Promise<{
    inputCost: bigint,
    outputEscrow: bigint,
    costStatus: "exact" | "estimated" | "maximum" | "minimum",
  }>,

  /**
   * Calculates the final cost after the task is complete,
   * typically based on actual resource usage.
   *
   * If the final cost differs from escrowOutputCost,
   * you'll either refund or charge the difference in your transaction manager.
   *
   * @param input - The original input used to generate the result.
   * @param output - The actual output or result used to compute final usage.
   * @return The final total cost for the usage actual output
   */
  getFinalCost(input: InputArgs, output: OutputResult): Promise<bigint>,

  /**
   * Computes the tax to be applied on top of the input and output costs.
   *
   * @param initialCost
   * @return The tax that will be applied to the input
   */
  calculateTax(initialCost: bigint): bigint,
}

