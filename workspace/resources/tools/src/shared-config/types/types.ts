
import { ShallowObject, ShallowObjectSchema } from "@divinci-ai/utils";

import { PricingStrategy } from "./PricingStrategy";
import { PublicToolInfo } from "@divinci-ai/models";


export enum ToolAvailableStatus {
  AVAILABLE = "available",
  DEPRECATED = "deprectaed",
}

export type ToolConfig<
  InputParam,
  InputConfig extends ShallowObject,
  OutputResult
> = {
  id: string,
  status: ToolAvailableStatus,
  info: PublicToolInfo,
  pricing: PricingStrategy<[InputParam, InputConfig], OutputResult>,
  validateInput?: (input: InputParam)=>boolean,
  inputConfig?: {
    schema: ShallowObjectSchema<InputConfig>,
    default: InputConfig,
    validate: (config: InputConfig) => Array<{ key: keyof InputConfig, error: string }>,
  },
};
