
export const NANO_MULTIPLIER = 1000000000n; // 10^9

export function amountPartsToSingle(amounts: { usd: bigint, nano: bigint }){
  return (
    (amounts.usd * NANO_MULTIPLIER) + amounts.nano
  );
}

export function amountSingleToParts(nanoAmount: bigint){
  const negative = nanoAmount < 0n;
  const absVal = nanoAmount < 0n ? -nanoAmount : nanoAmount;

  // normal = integer part
  const usd = absVal / NANO_MULTIPLIER;
  const nano = absVal % NANO_MULTIPLIER;

  return {
    usd: negative ? -usd : usd,
    nano: negative ? -nano : nano
  };
}

export function amountToBalanceUSD(
  nano_amount: bigint
){
  const usdCents = Number(nano_amount * 100n / NANO_MULTIPLIER);
  return usdCents / 100;
}
