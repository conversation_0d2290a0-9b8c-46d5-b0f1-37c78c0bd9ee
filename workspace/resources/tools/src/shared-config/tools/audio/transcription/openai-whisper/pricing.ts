
import { NANO_MULTIPLIER, taxQuarterOfCost } from "../../../../money";
import { ToolType } from "./type";

// https://platform.openai.com/docs/pricing#transcription-and-speech-generation
// $0.006 per audio minute
const COST_PER_MINUTE = NANO_MULTIPLIER * 6n / 1_000n;
const COST_PER_SECOND = COST_PER_MINUTE * 60n;
function getCostOfDuration(duration: number){
  return BigInt(Math.round(duration * 1_000_000)) * COST_PER_SECOND / 1_000_000n;
}

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost([filePointer]){
    const audioLength = await filePointer.getAudioLength();
    return {
      inputCost: 0n,
      outputEscrow: getCostOfDuration(audioLength),
      costStatus: "maximum",
    };
  },

  async getFinalCost(input, segments){
    let finalCost = 0n;
    for(const segment of segments){
      finalCost += getCostOfDuration(segment.end - segment.start);
    }
    return finalCost;
  },

  calculateTax: taxQuarterOfCost
};