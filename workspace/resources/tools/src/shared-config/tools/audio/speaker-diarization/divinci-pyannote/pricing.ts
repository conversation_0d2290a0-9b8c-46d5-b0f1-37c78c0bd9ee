
import { ToolType } from "./type";

import { NANO_MULTIPLIER, taxQuarterOfCost } from "../../../../money";

// https://developers.cloudflare.com/workers-ai/models/whisper-large-v3-turbo/
// $0.00051 per audio minute
const COST_PER_MINUTE = NANO_MULTIPLIER * 51n / 100_000n;
const COST_PER_SECOND = COST_PER_MINUTE * 60n;
function getCostOfDuration(duration: number){
  return BigInt(Math.round(duration * 1_000_000)) * COST_PER_SECOND / 1_000_000n;
}

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost([filePointer]){
    const audioLength = await filePointer.getAudioLength();
    return {
      inputCost: getCostOfDuration(audioLength),
      outputEscrow: 0n,
      costStatus: "exact",
    };
  },

  async getFinalCost(){
    return 0n;
  },

  calculateTax: taxQuarterOfCost
};

