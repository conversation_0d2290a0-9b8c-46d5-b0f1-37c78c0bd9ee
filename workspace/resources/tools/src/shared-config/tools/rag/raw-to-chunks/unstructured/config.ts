
import { ToolType } from "./type";

type ConfigType = ToolType["inputConfig"];

const CHUNKING_STRATEGY = ["by_title", "by_similarity", "by_page"];
export const CONFIG: ConfigType = {
  schema: {
    skipDeJunk: "boolean",
    semantic_chunking: "boolean",
    relevanceThreshold: "number",

    chunkingStrategy: "string?",
    maxCharacters: "number",
    similarityThreshold: "number",
    includeOriginalElements: "boolean",
    multipageSections: "boolean",
  /*
    NOTE:
    as any is not prepered for this
    however, while this schema is working correct internally in this package
    it doesn't seem to be correct when imported into other packages like web-client or public-api
   */
  },
  default: {
    skipDeJunk: false,
    semantic_chunking: false,
    relevanceThreshold: 0.5,

    chunkingStrategy: "by_title",
    maxCharacters: 1024,
    similarityThreshold: 0.5,
    includeOriginalElements: false,
    multipageSections: false,
  },
  validate: function(config){
    const errors: ReturnType<NonNullable<ConfigType>["validate"]> = [];
    if(typeof config.chunkingStrategy === "string"){
      if(!CHUNKING_STRATEGY.includes(config.chunkingStrategy)){
        errors.push({
          key: "chunkingStrategy",
          error: `should be one of ${CHUNKING_STRATEGY.join(",")}`
        });
      }
    }
    return errors;
  }
};
