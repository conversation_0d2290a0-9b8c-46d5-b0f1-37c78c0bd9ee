
type R2Pointer = {
  originalName: string,
  Bucket: string,
  Key: string,
  getByteLength: ()=>Promise<number>,
};

export type RawToChunks = {
  processFile(r2Pointer: R2Pointer): Promise<R2Pointer>,
};

type RawToChunksInput = Parameters<RawToChunks["processFile"]>[0];
type RawToChunksOutput = Awaited<ReturnType<RawToChunks["processFile"]>>;

import { ToolConfig } from "../../../types";
import { ShallowObject } from "@divinci-ai/utils";
export type RawToChunksBase<Config extends ShallowObject> = (
  ToolConfig<RawToChunksInput, Config, RawToChunksOutput>
);
