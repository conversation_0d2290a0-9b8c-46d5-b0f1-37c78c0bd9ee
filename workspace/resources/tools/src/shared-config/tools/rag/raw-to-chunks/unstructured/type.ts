import { ToolConfig } from "../../../../types";
import { RawToChunks } from "../types";

type InputParams = Parameters<RawToChunks["processFile"]>[0];
type OutputResult = Awaited<ReturnType<RawToChunks["processFile"]>>;

type OpenParseConfig = {
  skipDeJunk: boolean,
  semantic_chunking: boolean,
  relevanceThreshold: number,

  chunkingStrategy?: string,
  maxCharacters: number,
  similarityThreshold: number,
  includeOriginalElements: boolean,
  multipageSections: boolean,
};

export type ToolType = ToolConfig<InputParams, OpenParseConfig, OutputResult>;
