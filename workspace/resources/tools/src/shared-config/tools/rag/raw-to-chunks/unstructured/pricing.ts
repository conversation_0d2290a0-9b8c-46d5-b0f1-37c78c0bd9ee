
import { ToolType } from "./type";

import { NANO_MULTIPLIER, taxQuarterOfCost } from "../../../../money";

// https://unstructured.io/developers#pricing
// $2/1000 Simple Pages
// $20/1000 Complex Pages

const PAGES_OF_EATFORLIFE = 397;
const BYTE_SIZE_OF_EATFORLIFE = 4_245_415;
const BYTES_PER_PAGE = BYTE_SIZE_OF_EATFORLIFE/PAGES_OF_EATFORLIFE;

const COST_PER_PAGE = {
  simple: {
    extentions: ["csv", "txt", "md"],
    cost: NANO_MULTIPLIER * 2n / 1000n,
  },
  complex: {
    extentions: ["pdf"],
    cost: NANO_MULTIPLIER * 20n / 1000n
  }
};

import { parse as pathParse } from "path";
function getCostPerPage(filename: string){
  const ext = getFileExtension(filename);
  for(const costs of Object.values(COST_PER_PAGE)){
    if(costs.extentions.includes(ext)){
      return costs.cost;
    }
  }
  throw new Error("Can't process filename");
}

function getFileExtension(filename: string){
  const { ext } = pathParse(filename);
  if(!ext) throw new Error("Invalid filename extension");
  if(ext.startsWith(".")) return ext.slice(1);
  return ext;
}


export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost([filePointer]){
    const byteLength = await filePointer.getByteLength();
    const pages = byteLength / BYTES_PER_PAGE;
    const costPerPage = getCostPerPage(filePointer.originalName);
    return {
      inputCost: BigInt(pages) * costPerPage,
      outputEscrow: 0n,
      costStatus: "exact",
    };
  },

  async getFinalCost(){
    return 0n;
  },

  calculateTax: taxQuarterOfCost
};

