import { ToolConfig } from "../../../../types";
import { RawToChunks } from "../types";

type InputParams = Parameters<RawToChunks["processFile"]>[0];
type OutputResult = Awaited<ReturnType<RawToChunks["processFile"]>>;



export type ToolType = ToolConfig<InputParams, OpenParseConfig, OutputResult>;


export type OpenParseConfig = {
  skipDeJunk: boolean,
  semantic_chunking: boolean,
  relevanceThreshold: number,

  semantic: boolean,
  embeddingsProvider?: string,
  embeddingsModel?: string,

  minTokens: number,
  maxTokens: number,
  chunkOverlap?: number,
  useTokens?: boolean,
};

