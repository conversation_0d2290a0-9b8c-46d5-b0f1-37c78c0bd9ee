
import { ToolType } from "./type";

type ConfigType = ToolType["inputConfig"];

const EMBEDDINGS_PROVIDERS = ["ollama", "openai", "cloudflare"];
export const CONFIG: ConfigType = {
  schema: {
    skipDeJunk: "boolean",
    semantic_chunking: "boolean",
    relevanceThreshold: "number",

    semantic: "boolean",
    embeddingsProvider: "string?",
    embeddingsModel: "string?",

    minTokens: "number",
    maxTokens: "number",
    chunkOverlap: "number?",
    useTokens: "boolean?",
  /*
    NOTE:
    as any is not prepered for this
    however, while this schema is working correct internally in this package
    it doesn't seem to be correct when imported into other packages like web-client or public-api
   */
  },
  default: {
    skipDeJunk: false,
    semantic_chunking: false,
    relevanceThreshold: 0,

    semantic: false,
    embeddingsProvider: undefined,
    embeddingsModel: undefined,

    minTokens: 256,
    maxTokens: 1024,
    chunkOverlap: 85,
    useTokens: true,
  },
  validate: function(config){
    const errors: ReturnType<NonNullable<ConfigType>["validate"]> = [];
    if(typeof config.embeddingsProvider === "string"){
      if(!EMBEDDINGS_PROVIDERS.includes(config.embeddingsProvider)){
        errors.push({
          key: "embeddingsProvider",
          error: `should be one of ${EMBEDDINGS_PROVIDERS.join(",")}`
        });
      }
    }
    return errors;
  }
};
