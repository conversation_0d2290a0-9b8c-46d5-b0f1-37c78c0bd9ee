
import { ToolType } from "./type";

import { NANO_MULTIPLIER, taxQuarterOfCost } from "../../../../money";

// https://www.firecrawl.dev/pricing
// $16 per 3000 pages
const COST_PER_PAGE = NANO_MULTIPLIER * 16n / 3000n;

export const PRICING: ToolType["pricing"] = {
  async getEstimatedCost(){
    return {
      inputCost: COST_PER_PAGE,
      outputEscrow: 0n,
      costStatus: "exact",
    };
  },

  async getFinalCost(){
    return 0n;
  },

  calculateTax: taxQuarterOfCost
};

