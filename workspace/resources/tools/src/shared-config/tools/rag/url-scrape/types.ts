
import { RagScraperResult } from "@divinci-ai/models";
type HTTPScraperInput = Parameters<HTTPScraper["scrapeUrl"]>[0];
type HTTPScraperOutput = Awaited<ReturnType<HTTPScraper["scrapeUrl"]>>;

export type HTTPScraper = {
  scrapeUrl(url: string): Promise<RagScraperResult>,
};

import { ToolConfig } from "../../../types";
import { ShallowObject } from "@divinci-ai/utils";
export type HTTPScraperBase<Config extends ShallowObject> = (
  ToolConfig<HTTPScraperInput, Config, HTTPScraperOutput>
);
