
import { RawToChunks, RawToChunksBase } from "./raw-to-chunks/types";
export { RawToChunks };

import { DivinciOpenparse } from "./raw-to-chunks/divinci-openparse";
import { Unstructured } from "./raw-to-chunks/unstructured";


export const RAG_RAW_TO_CHUNKS: Record<string, RawToChunksBase<any>> = {
  [DivinciOpenparse.id]: DivinciOpenparse,
  [Unstructured.id]: Unstructured,
};

export * from "./url-scrape/types";
import { HTTPScraper, HTTPScraperBase } from "./url-scrape/types";
export { HTTPScraper };

import { DivinciFetchScrape  } from "./url-scrape/divinci-fetch";
import { MendableFirecrawl } from "./url-scrape/mendable-firecrawl";

export const RAG_URL_SCRAPE: Record<string, HTTPScraperBase<any>> = {
  [DivinciFetchScrape.id]: DivinciFetchScrape,
  [MendableFirecrawl.id]: MendableFirecrawl,
};
