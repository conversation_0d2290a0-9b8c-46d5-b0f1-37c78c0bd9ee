import { describe, it, expect, vi, beforeEach } from 'vitest';
import { jsonExtraResponse } from '../../src/http-response/json-extra-response';
import { JSON_EXTRA_stringify, JSON_EXTRA_parse } from '@divinci-ai/utils';

// Mock JSON_EXTRA_stringify and JSON_EXTRA_parse
vi.mock('@divinci-ai/utils', () => ({
  JSON_EXTRA_stringify: vi.fn(data => {
    return JSON.stringify(data);
  }),
  JSON_EXTRA_parse: vi.fn(jsonStr => {
    return JSON.parse(jsonStr);
  })
}));

describe('JSON Serialization', () => {
  describe('JSON_EXTRA_stringify', () => {
    it('should stringify regular JSON objects', () => {
      const data = { key: 'value', number: 42, bool: true };
      const result = JSON_EXTRA_stringify(data);

      expect(JSO<PERSON>_EXTRA_stringify).toHaveBeenCalledWith(data);
      expect(result).toBe(JSON.stringify(data));
    });

    it('should handle Date objects', () => {
      const date = new Date('2023-01-01T00:00:00.000Z');
      const data = { date };
      const result = JSON_EXTRA_stringify(data);

      expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
      expect(result).toContain('2023-01-01');
    });

    it('should handle number values', () => {
      const number = 9007199254740991;
      const data = { number };
      const result = JSON_EXTRA_stringify(data);

      expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
      expect(result).toContain('9007199254740991');
    });

    it('should handle nested objects with special types', () => {
      const date = new Date('2023-01-01T00:00:00.000Z');
      const data = {
        nested: {
          date,
          array: [1, 2, 3]
        }
      };
      const result = JSON_EXTRA_stringify(data);

      expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
      expect(result).toContain('2023-01-01');
      expect(result).toContain('array');
    });
  });

  describe('JSON_EXTRA_parse', () => {
    it('should parse regular JSON strings', () => {
      const jsonStr = '{"key":"value","number":42,"bool":true}';
      const result = JSON_EXTRA_parse(jsonStr);

      expect(JSON_EXTRA_parse).toHaveBeenCalledWith(jsonStr);
      expect(result).toEqual({ key: 'value', number: 42, bool: true });
    });

    it('should handle Date strings', () => {
      const jsonStr = '{"date":"2023-01-01T00:00:00.000Z"}';
      const result = JSON_EXTRA_parse(jsonStr);

      expect(JSON_EXTRA_parse).toHaveBeenCalledWith(jsonStr);
      expect(result.date).toBe('2023-01-01T00:00:00.000Z');
    });

    it('should handle number strings', () => {
      const jsonStr = '{"number":9007199254740991}';
      const result = JSON_EXTRA_parse(jsonStr);

      expect(JSON_EXTRA_parse).toHaveBeenCalledWith(jsonStr);
      expect(typeof result.number).toBe('number');
      expect(result.number).toBe(9007199254740991);
    });

    it('should handle nested objects with special types', () => {
      const jsonStr = '{"nested":{"string":"test","number":42,"array":[1,2,3]}}';
      const result = JSON_EXTRA_parse(jsonStr);

      expect(JSON_EXTRA_parse).toHaveBeenCalledWith(jsonStr);
      expect(result.nested.string).toBe('test');
      expect(result.nested.number).toBe(42);
      expect(result.nested.array).toEqual([1, 2, 3]);
    });
  });

  describe('jsonExtraResponse', () => {
    let mockRes: any;

    beforeEach(() => {
      // Create a mock response object
      mockRes = {
        setHeader: vi.fn(),
        end: vi.fn()
      };

      // Reset mocks
      vi.clearAllMocks();
    });

    it('should set the content-type header to application/json', () => {
      const data = { key: 'value' };
      jsonExtraResponse(mockRes, data);

      expect(mockRes.setHeader).toHaveBeenCalledWith('content-type', 'application/json');
    });

    it('should stringify the data using JSON_EXTRA_stringify', () => {
      const data = { key: 'value' };
      jsonExtraResponse(mockRes, data);

      expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
    });

    it('should end the response with the stringified data', () => {
      const data = { key: 'value' };
      const stringified = JSON.stringify(data);

      // Mock JSON_EXTRA_stringify to return our stringified data
      (JSON_EXTRA_stringify as any).mockReturnValueOnce(stringified);

      jsonExtraResponse(mockRes, data);

      expect(mockRes.end).toHaveBeenCalledWith(stringified);
    });

    it('should handle complex data with nested objects', () => {
      const data = {
        date: '2023-01-01T00:00:00.000Z',
        nested: {
          array: [1, 2, 3],
          object: { key: 'value' }
        }
      };

      jsonExtraResponse(mockRes, data);

      expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
    });
  });

  describe('Round-trip serialization', () => {
    it('should preserve data integrity through stringify and parse', () => {
      const originalData = {
        string: 'test',
        number: 42,
        boolean: true,
        nested: {
          array: [1, 2, 3],
          object: { key: 'value' }
        }
      };

      const stringified = JSON_EXTRA_stringify(originalData);
      const parsed = JSON_EXTRA_parse(stringified);

      // Check that the parsed data matches the original
      expect(parsed.string).toBe(originalData.string);
      expect(parsed.number).toBe(originalData.number);
      expect(parsed.boolean).toBe(originalData.boolean);
      expect(parsed.nested.array).toEqual(originalData.nested.array);
      expect(parsed.nested.object).toEqual(originalData.nested.object);
    });
  });
});
