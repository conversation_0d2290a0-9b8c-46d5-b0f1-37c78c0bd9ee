import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock console methods
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleInfo = console.info;
const originalConsoleDebug = console.debug;

describe('Logger', () => {
  // Create a simple logger implementation
  const createLogger = (options: { 
    level: 'debug' | 'info' | 'warn' | 'error',
    prefix?: string,
    includeTimestamp?: boolean
  }) => {
    const { level, prefix = '', includeTimestamp = true } = options;
    
    const levelPriority = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
    
    const shouldLog = (messageLevel: 'debug' | 'info' | 'warn' | 'error') => {
      return levelPriority[messageLevel] >= levelPriority[level];
    };
    
    const formatMessage = (message: string) => {
      let formattedMessage = message;
      
      if (prefix) {
        formattedMessage = `[${prefix}] ${formattedMessage}`;
      }
      
      if (includeTimestamp) {
        const timestamp = new Date().toISOString();
        formattedMessage = `${timestamp} ${formattedMessage}`;
      }
      
      return formattedMessage;
    };
    
    return {
      debug: (...args: any[]) => {
        if (!shouldLog('debug')) return;
        console.debug(formatMessage(args[0]), ...args.slice(1));
      },
      info: (...args: any[]) => {
        if (!shouldLog('info')) return;
        console.info(formatMessage(args[0]), ...args.slice(1));
      },
      warn: (...args: any[]) => {
        if (!shouldLog('warn')) return;
        console.warn(formatMessage(args[0]), ...args.slice(1));
      },
      error: (...args: any[]) => {
        if (!shouldLog('error')) return;
        console.error(formatMessage(args[0]), ...args.slice(1));
      },
      log: (...args: any[]) => {
        console.log(formatMessage(args[0]), ...args.slice(1));
      }
    };
  };
  
  beforeEach(() => {
    // Mock console methods
    console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();
    console.info = vi.fn();
    console.debug = vi.fn();
  });
  
  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
    console.info = originalConsoleInfo;
    console.debug = originalConsoleDebug;
    
    // Clear mocks
    vi.clearAllMocks();
  });
  
  describe('Log levels', () => {
    it('should respect log level priority', () => {
      const logger = createLogger({ level: 'info' });
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      
      expect(console.debug).not.toHaveBeenCalled();
      expect(console.info).toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
    });
    
    it('should only log errors when level is error', () => {
      const logger = createLogger({ level: 'error' });
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      
      expect(console.debug).not.toHaveBeenCalled();
      expect(console.info).not.toHaveBeenCalled();
      expect(console.warn).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
    });
    
    it('should log all messages when level is debug', () => {
      const logger = createLogger({ level: 'debug' });
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');
      
      expect(console.debug).toHaveBeenCalled();
      expect(console.info).toHaveBeenCalled();
      expect(console.warn).toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();
    });
  });
  
  describe('Message formatting', () => {
    it('should include timestamp in log messages', () => {
      const logger = createLogger({ level: 'info' });
      
      logger.info('Info message');
      
      const loggedMessage = (console.info as any).mock.calls[0][0];
      expect(loggedMessage).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z Info message$/);
    });
    
    it('should not include timestamp when includeTimestamp is false', () => {
      const logger = createLogger({ level: 'info', includeTimestamp: false });
      
      logger.info('Info message');
      
      const loggedMessage = (console.info as any).mock.calls[0][0];
      expect(loggedMessage).toBe('Info message');
    });
    
    it('should include prefix in log messages', () => {
      const logger = createLogger({ level: 'info', prefix: 'APP', includeTimestamp: false });
      
      logger.info('Info message');
      
      const loggedMessage = (console.info as any).mock.calls[0][0];
      expect(loggedMessage).toBe('[APP] Info message');
    });
    
    it('should format messages with both timestamp and prefix', () => {
      const logger = createLogger({ level: 'info', prefix: 'APP' });
      
      logger.info('Info message');
      
      const loggedMessage = (console.info as any).mock.calls[0][0];
      expect(loggedMessage).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z \[APP\] Info message$/);
    });
  });
  
  describe('Additional arguments', () => {
    it('should pass additional arguments to console methods', () => {
      const logger = createLogger({ level: 'info', includeTimestamp: false });
      
      const obj = { key: 'value' };
      logger.info('Info message', obj, 123);
      
      expect(console.info).toHaveBeenCalledWith('Info message', obj, 123);
    });
    
    it('should only format the first argument', () => {
      const logger = createLogger({ level: 'info', prefix: 'APP', includeTimestamp: false });
      
      const obj = { key: 'value' };
      logger.info('Info message', obj, 123);
      
      expect(console.info).toHaveBeenCalledWith('[APP] Info message', obj, 123);
    });
  });
  
  describe('Environment-based logging', () => {
    const originalEnv = process.env;
    
    beforeEach(() => {
      process.env = { ...originalEnv };
    });
    
    afterEach(() => {
      process.env = originalEnv;
    });
    
    it('should enable debug logging when LOG_LEVEL is debug', () => {
      process.env.LOG_LEVEL = 'debug';
      
      const getLogLevel = () => {
        return (process.env.LOG_LEVEL || 'info') as 'debug' | 'info' | 'warn' | 'error';
      };
      
      const logger = createLogger({ level: getLogLevel() });
      
      logger.debug('Debug message');
      logger.info('Info message');
      
      expect(console.debug).toHaveBeenCalled();
      expect(console.info).toHaveBeenCalled();
    });
    
    it('should disable debug logging when LOG_LEVEL is not set', () => {
      delete process.env.LOG_LEVEL;
      
      const getLogLevel = () => {
        return (process.env.LOG_LEVEL || 'info') as 'debug' | 'info' | 'warn' | 'error';
      };
      
      const logger = createLogger({ level: getLogLevel() });
      
      logger.debug('Debug message');
      logger.info('Info message');
      
      expect(console.debug).not.toHaveBeenCalled();
      expect(console.info).toHaveBeenCalled();
    });
  });
  
  describe('Error logging', () => {
    it('should log error objects with stack traces', () => {
      const logger = createLogger({ level: 'error', includeTimestamp: false });
      
      const error = new Error('Test error');
      logger.error('An error occurred', error);
      
      expect(console.error).toHaveBeenCalledWith('An error occurred', error);
    });
    
    it('should format error objects for better readability', () => {
      const logger = createLogger({ level: 'error', includeTimestamp: false });
      
      // Create a function that formats errors for logging
      const formatError = (err: any) => {
        if (!(err instanceof Error)) return err;
        
        return {
          name: err.name,
          message: err.message,
          stack: err.stack
        };
      };
      
      const error = new Error('Test error');
      const formattedError = formatError(error);
      
      logger.error('An error occurred', formattedError);
      
      expect(console.error).toHaveBeenCalledWith('An error occurred', {
        name: 'Error',
        message: 'Test error',
        stack: error.stack
      });
    });
  });
  
  describe('Contextual logging', () => {
    it('should create child loggers with additional context', () => {
      const parentLogger = createLogger({ level: 'info', prefix: 'PARENT', includeTimestamp: false });
      
      // Create a child logger with additional context
      const childLogger = createLogger({ level: 'info', prefix: 'PARENT:CHILD', includeTimestamp: false });
      
      parentLogger.info('Parent message');
      childLogger.info('Child message');
      
      expect(console.info).toHaveBeenCalledWith('[PARENT] Parent message');
      expect(console.info).toHaveBeenCalledWith('[PARENT:CHILD] Child message');
    });
    
    it('should create request-scoped loggers with request IDs', () => {
      // Create a function that generates a request ID
      const generateRequestId = () => {
        return Math.random().toString(36).substring(2, 15);
      };
      
      // Create a request logger factory
      const createRequestLogger = (requestId: string) => {
        return createLogger({ level: 'info', prefix: `REQ:${requestId}`, includeTimestamp: false });
      };
      
      const requestId1 = generateRequestId();
      const requestId2 = generateRequestId();
      
      const logger1 = createRequestLogger(requestId1);
      const logger2 = createRequestLogger(requestId2);
      
      logger1.info('Request 1 message');
      logger2.info('Request 2 message');
      
      expect(console.info).toHaveBeenCalledWith(`[REQ:${requestId1}] Request 1 message`);
      expect(console.info).toHaveBeenCalledWith(`[REQ:${requestId2}] Request 2 message`);
    });
  });
});
