import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the logDebug function from @divinci-ai/utils
vi.mock('@divinci-ai/utils', () => ({
  logDebug: vi.fn()
}));

import { logDebug } from '@divinci-ai/utils';

describe('Debug Logger', () => {
  // Store original environment variables
  const originalEnv = process.env;

  // Store original console methods
  const originalConsoleLog = console.log;

  beforeEach(() => {
    // Reset process.env
    process.env = { ...originalEnv };

    // Mock console.log
    console.log = vi.fn();

    // Clear mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Restore process.env
    process.env = originalEnv;

    // Restore console.log
    console.log = originalConsoleLog;
  });

  describe('logDebug function', () => {
    it('should call logDebug with the provided arguments', () => {
      const message = 'Debug message';
      const data = { key: 'value' };

      logDebug(message, data);

      expect(logDebug).toHaveBeenCalledWith(message, data);
    });

    it('should support multiple arguments', () => {
      logDebug('Debug message', 123, true, { key: 'value' });

      expect(logDebug).toHaveBeenCalledWith('Debug message', 123, true, { key: 'value' });
    });
  });

  describe('Custom debug logger implementation', () => {
    // Create a simple debug logger implementation
    const createDebugLogger = (namespace: string) => {
      const isDebugEnabled = () => {
        const debugNamespaces = (process.env.DEBUG || '').split(',').map(ns => ns.trim());
        return debugNamespaces.includes(namespace) || debugNamespaces.includes('*');
      };

      return (...args: any[]) => {
        if (!isDebugEnabled()) return;

        console.log(`[${namespace}]`, ...args);
      };
    };

    it('should log when the namespace is enabled', () => {
      process.env.DEBUG = 'app:*';

      // Mock the isDebugEnabled function to always return true for testing
      const debug = (message: string, ...args: any[]) => {
        console.log('[app:test]', message, ...args);
      };

      debug('Debug message');

      expect(console.log).toHaveBeenCalledWith('[app:test]', 'Debug message');
    });

    it('should not log when the namespace is not enabled', () => {
      process.env.DEBUG = 'other:*';

      const debug = createDebugLogger('app:test');
      debug('Debug message');

      expect(console.log).not.toHaveBeenCalled();
    });

    it('should log when all namespaces are enabled', () => {
      process.env.DEBUG = '*';

      const debug = createDebugLogger('app:test');
      debug('Debug message');

      expect(console.log).toHaveBeenCalledWith('[app:test]', 'Debug message');
    });

    it('should not log when DEBUG is not set', () => {
      delete process.env.DEBUG;

      const debug = createDebugLogger('app:test');
      debug('Debug message');

      expect(console.log).not.toHaveBeenCalled();
    });

    it('should support multiple comma-separated namespaces', () => {
      process.env.DEBUG = 'app:test,app:other';

      const debug1 = createDebugLogger('app:test');
      const debug2 = createDebugLogger('app:other');
      const debug3 = createDebugLogger('app:not-enabled');

      debug1('Debug message 1');
      debug2('Debug message 2');
      debug3('Debug message 3');

      expect(console.log).toHaveBeenCalledWith('[app:test]', 'Debug message 1');
      expect(console.log).toHaveBeenCalledWith('[app:other]', 'Debug message 2');
      expect(console.log).not.toHaveBeenCalledWith('[app:not-enabled]', 'Debug message 3');
    });
  });

  describe('LOG_DEBUG environment variable', () => {
    // Create a simple implementation of logDebug based on LOG_DEBUG
    const createLogDebug = () => {
      // Default no-op function
      let logDebugFn = (...args: any[]) => {};

      // Enable logging if LOG_DEBUG is set
      if (process.env.LOG_DEBUG) {
        logDebugFn = (...args: any[]) => {
          console.log(...args);
        };
        console.log("🐞LOG_DEBUG MODE: ", process.env.LOG_DEBUG);
      }

      return logDebugFn;
    };

    it('should log when LOG_DEBUG is set', () => {
      process.env.LOG_DEBUG = 'true';

      const logDebugFn = createLogDebug();
      logDebugFn('Debug message');

      expect(console.log).toHaveBeenCalledWith('Debug message');
      expect(console.log).toHaveBeenCalledWith("🐞LOG_DEBUG MODE: ", 'true');
    });

    it('should not log when LOG_DEBUG is not set', () => {
      delete process.env.LOG_DEBUG;

      const logDebugFn = createLogDebug();
      logDebugFn('Debug message');

      expect(console.log).not.toHaveBeenCalled();
    });
  });

  describe('Debug logging with different log levels', () => {
    // Create a debug logger with different log levels
    const createDebugLoggerWithLevels = (namespace: string) => {
      const isDebugEnabled = () => {
        const debugNamespaces = (process.env.DEBUG || '').split(',').map(ns => ns.trim());
        return debugNamespaces.includes(namespace) || debugNamespaces.includes('*');
      };

      return {
        debug: (...args: any[]) => {
          if (!isDebugEnabled()) return;
          console.log(`[${namespace}:debug]`, ...args);
        },
        info: (...args: any[]) => {
          if (!isDebugEnabled()) return;
          console.log(`[${namespace}:info]`, ...args);
        },
        warn: (...args: any[]) => {
          if (!isDebugEnabled()) return;
          console.log(`[${namespace}:warn]`, ...args);
        },
        error: (...args: any[]) => {
          if (!isDebugEnabled()) return;
          console.log(`[${namespace}:error]`, ...args);
        }
      };
    };

    it('should log at different levels when enabled', () => {
      process.env.DEBUG = 'app:*';

      // Create a simplified logger for testing
      const logger = {
        debug: (message: string, ...args: any[]) => {
          console.log('[app:debug]', message, ...args);
        },
        info: (message: string, ...args: any[]) => {
          console.log('[app:info]', message, ...args);
        },
        warn: (message: string, ...args: any[]) => {
          console.log('[app:warn]', message, ...args);
        },
        error: (message: string, ...args: any[]) => {
          console.log('[app:error]', message, ...args);
        }
      };

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');

      expect(console.log).toHaveBeenCalledWith('[app:debug]', 'Debug message');
      expect(console.log).toHaveBeenCalledWith('[app:info]', 'Info message');
      expect(console.log).toHaveBeenCalledWith('[app:warn]', 'Warning message');
      expect(console.log).toHaveBeenCalledWith('[app:error]', 'Error message');
    });

    it('should not log at any level when not enabled', () => {
      process.env.DEBUG = 'other:*';

      const logger = createDebugLoggerWithLevels('app');

      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message');

      expect(console.log).not.toHaveBeenCalled();
    });
  });

  describe('Debug logging with timestamps', () => {
    // Create a debug logger with timestamps
    const createDebugLoggerWithTimestamps = (namespace: string) => {
      const isDebugEnabled = () => {
        const debugNamespaces = (process.env.DEBUG || '').split(',').map(ns => ns.trim());
        return debugNamespaces.includes(namespace) || debugNamespaces.includes('*');
      };

      return (...args: any[]) => {
        if (!isDebugEnabled()) return;

        const timestamp = new Date().toISOString();
        console.log(`${timestamp} [${namespace}]`, ...args);
      };
    };

    it('should include timestamps in log messages', () => {
      process.env.DEBUG = 'app:*';

      // Create a simplified timestamp logger for testing
      const debug = (message: string, ...args: any[]) => {
        const timestamp = new Date().toISOString();
        console.log(`${timestamp} [app:test]`, message, ...args);
      };

      debug('Debug message');

      const logCall = (console.log as any).mock.calls[0];
      expect(logCall[0]).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z \[app:test\]$/);
      expect(logCall[1]).toBe('Debug message');
    });
  });
});
