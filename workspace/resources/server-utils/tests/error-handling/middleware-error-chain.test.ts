import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';
import { IncomingMessage, ServerResponse } from 'http';

// Define middleware types
type NextFunction = (err?: any) => void;
type Middleware = (req: IncomingMessage, res: ServerResponse, next: NextFunction) => void;
type AsyncMiddleware = (req: IncomingMessage, res: ServerResponse, next: NextFunction) => Promise<void>;

// Create a middleware chain executor
function executeMiddlewareChain(
  middlewares: Array<Middleware | AsyncMiddleware>,
  req: IncomingMessage,
  res: ServerResponse,
  finalHandler: (err?: any) => void
) {
  let index = 0;
  
  function next(err?: any) {
    if (err) {
      return finalHandler(err);
    }
    
    const middleware = middlewares[index++];
    if (!middleware) {
      return finalHandler();
    }
    
    try {
      const result = middleware(req, res, next);
      
      // Handle async middleware
      if (result && typeof result.catch === 'function') {
        result.catch((err: any) => {
          finalHandler(err);
        });
      }
    } catch (err) {
      finalHandler(err);
    }
  }
  
  next();
}

describe('Middleware Error Chain', () => {
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockFinalHandler: (err?: any) => void;
  
  beforeEach(() => {
    mockReq = {} as IncomingMessage;
    mockRes = {
      statusCode: 200,
      setHeader: vi.fn(),
      end: vi.fn()
    } as unknown as ServerResponse;
    mockFinalHandler = vi.fn();
  });
  
  describe('Synchronous middleware error handling', () => {
    it('should pass errors to the final handler', () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: Middleware = (req, res, next) => {
        throw HTTP_ERRORS.UNAUTHORIZED;
      };
      
      const middleware3: Middleware = (req, res, next) => {
        next();
      };
      
      executeMiddlewareChain([middleware1, middleware2, middleware3], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
    });
    
    it('should stop execution after an error', () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: Middleware = (req, res, next) => {
        next(HTTP_ERRORS.UNAUTHORIZED);
      };
      
      const middleware3: Middleware = (req, res, next) => {
        next();
      };
      
      executeMiddlewareChain([middleware1, middleware2, middleware3], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
      expect(mockFinalHandler).toHaveBeenCalledTimes(1);
    });
    
    it('should handle errors with context', () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: Middleware = (req, res, next) => {
        next(HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({ 
          requiredRole: 'admin', 
          userRole: 'user' 
        }));
      };
      
      executeMiddlewareChain([middleware1, middleware2], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 403,
        message: 'forbidden',
        context: { requiredRole: 'admin', userRole: 'user' }
      }));
    });
  });
  
  describe('Asynchronous middleware error handling', () => {
    it('should handle errors in async middleware', async () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: AsyncMiddleware = async (req, res, next) => {
        throw HTTP_ERRORS.NOT_FOUND;
      };
      
      executeMiddlewareChain([middleware1, middleware2], mockReq, mockRes, mockFinalHandler);
      
      // Wait for promises to resolve
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
    });
    
    it('should handle rejected promises in async middleware', async () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: AsyncMiddleware = async (req, res, next) => {
        await Promise.reject(HTTP_ERRORS.SERVER_ERROR);
      };
      
      executeMiddlewareChain([middleware1, middleware2], mockReq, mockRes, mockFinalHandler);
      
      // Wait for promises to resolve
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.SERVER_ERROR);
    });
    
    it('should handle errors in next() calls in async middleware', async () => {
      const middleware1: Middleware = (req, res, next) => {
        next();
      };
      
      const middleware2: AsyncMiddleware = async (req, res, next) => {
        next(HTTP_ERRORS.BAD_FORM);
      };
      
      executeMiddlewareChain([middleware1, middleware2], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
    });
  });
  
  describe('Error handling middleware', () => {
    it('should allow error handling middleware to recover from errors', () => {
      const middleware1: Middleware = (req, res, next) => {
        next(new Error('Test error'));
      };
      
      const errorHandler: Middleware = (err, req, res, next) => {
        // Handle the error and continue
        (req as any).error = err;
        next();
      };
      
      const middleware3: Middleware = (req, res, next) => {
        next();
      };
      
      // Create a custom middleware chain with error handling
      const middlewareChain = [middleware1];
      
      executeMiddlewareChain(middlewareChain, mockReq, mockRes, (err) => {
        if (err) {
          // Call the error handler
          errorHandler(err, mockReq, mockRes, (nextErr) => {
            if (nextErr) {
              mockFinalHandler(nextErr);
            } else {
              // Continue with the next middleware
              middleware3(mockReq, mockRes, mockFinalHandler);
            }
          });
        } else {
          mockFinalHandler();
        }
      });
      
      expect(mockFinalHandler).toHaveBeenCalledWith();
      expect((mockReq as any).error).toBeInstanceOf(Error);
      expect((mockReq as any).error.message).toBe('Test error');
    });
    
    it('should allow error handling middleware to transform errors', () => {
      const middleware1: Middleware = (req, res, next) => {
        next(new Error('Database error'));
      };
      
      const errorHandler: Middleware = (err, req, res, next) => {
        // Transform the error
        next(HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR({
          originalError: err.message,
          component: 'Database'
        }));
      };
      
      // Create a custom middleware chain with error handling
      const middlewareChain = [middleware1];
      
      executeMiddlewareChain(middlewareChain, mockReq, mockRes, (err) => {
        if (err) {
          // Call the error handler
          errorHandler(err, mockReq, mockRes, mockFinalHandler);
        } else {
          mockFinalHandler();
        }
      });
      
      expect(mockFinalHandler).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 500,
        message: 'something went wrong internally, someone should have been notified',
        context: {
          originalError: 'Database error',
          component: 'Database'
        }
      }));
    });
  });
  
  describe('Complex middleware chains', () => {
    it('should handle nested middleware chains', () => {
      // Create middleware that executes a sub-chain
      const createSubChainMiddleware = (subMiddlewares: Middleware[]): Middleware => {
        return (req, res, next) => {
          executeMiddlewareChain(subMiddlewares, req, res, (err) => {
            if (err) {
              next(err);
            } else {
              next();
            }
          });
        };
      };
      
      const subMiddleware1: Middleware = (req, res, next) => {
        (req as any).subMiddleware1Called = true;
        next();
      };
      
      const subMiddleware2: Middleware = (req, res, next) => {
        (req as any).subMiddleware2Called = true;
        next(HTTP_ERRORS.FORBIDDEN);
      };
      
      const middleware1: Middleware = (req, res, next) => {
        (req as any).middleware1Called = true;
        next();
      };
      
      const middleware2 = createSubChainMiddleware([subMiddleware1, subMiddleware2]);
      
      const middleware3: Middleware = (req, res, next) => {
        (req as any).middleware3Called = true;
        next();
      };
      
      executeMiddlewareChain([middleware1, middleware2, middleware3], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
      expect((mockReq as any).middleware1Called).toBe(true);
      expect((mockReq as any).subMiddleware1Called).toBe(true);
      expect((mockReq as any).subMiddleware2Called).toBe(true);
      expect((mockReq as any).middleware3Called).toBeUndefined();
    });
    
    it('should handle conditional middleware execution', () => {
      // Create middleware that conditionally executes other middleware
      const createConditionalMiddleware = (
        condition: (req: IncomingMessage) => boolean,
        trueMiddleware: Middleware,
        falseMiddleware: Middleware
      ): Middleware => {
        return (req, res, next) => {
          if (condition(req)) {
            trueMiddleware(req, res, next);
          } else {
            falseMiddleware(req, res, next);
          }
        };
      };
      
      const successMiddleware: Middleware = (req, res, next) => {
        (req as any).success = true;
        next();
      };
      
      const errorMiddleware: Middleware = (req, res, next) => {
        next(HTTP_ERRORS.UNAUTHORIZED);
      };
      
      const middleware1: Middleware = (req, res, next) => {
        (req as any).authenticated = false;
        next();
      };
      
      const middleware2 = createConditionalMiddleware(
        (req) => !!(req as any).authenticated,
        successMiddleware,
        errorMiddleware
      );
      
      executeMiddlewareChain([middleware1, middleware2], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
      expect((mockReq as any).success).toBeUndefined();
      
      // Reset and try with authentication
      vi.clearAllMocks();
      mockReq = {} as IncomingMessage;
      
      const authenticatedMiddleware: Middleware = (req, res, next) => {
        (req as any).authenticated = true;
        next();
      };
      
      executeMiddlewareChain([authenticatedMiddleware, middleware2], mockReq, mockRes, mockFinalHandler);
      
      expect(mockFinalHandler).toHaveBeenCalledWith();
      expect((mockReq as any).success).toBe(true);
    });
  });
});
