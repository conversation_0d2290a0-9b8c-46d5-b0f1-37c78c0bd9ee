import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

// Mock mongoose errors
class ValidationError extends Error {
  name = 'ValidationError';
  errors: Record<string, { message: string }> = {};
  
  constructor(errors: Record<string, { message: string }>) {
    super('Validation failed');
    this.errors = errors;
  }
}

class CastError extends Error {
  name = 'CastError';
  path: string;
  value: any;
  
  constructor(path: string, value: any) {
    super(`Cast to ObjectId failed for value "${value}" at path "${path}"`);
    this.path = path;
    this.value = value;
  }
}

// Create a database error handler
function handleDatabaseError(err: any) {
  if (err instanceof ValidationError) {
    return HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      Object.entries(err.errors).map(([key, value]) => ({
        path: key,
        message: value.message
      }))
    );
  }
  
  if (err instanceof CastError) {
    return HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      `Failed casting ${err.value} to ObjectId at path ${err.path}`
    );
  }
  
  if (err.name === 'MongoNetworkError') {
    return HTTP_ERRORS_WITH_CONTEXT.UNAVAILABLE(
      `Database connection error: ${err.message}`
    );
  }
  
  if (err.name === 'MongoTimeoutError') {
    return HTTP_ERRORS_WITH_CONTEXT.TIMEOUT(
      `Database operation timed out: ${err.message}`
    );
  }
  
  if (err.code === 11000) {
    // Duplicate key error
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];
    
    return HTTP_ERRORS_WITH_CONTEXT.ALREADY_EXISTS(
      `${field} "${value}" already exists`
    );
  }
  
  // Default to server error
  return HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR(
    `Database error: ${err.message}`
  );
}

describe('Database Error Handling', () => {
  describe('Mongoose validation errors', () => {
    it('should handle validation errors', () => {
      const validationError = new ValidationError({
        username: { message: 'Username is required' },
        email: { message: 'Email is invalid' }
      });
      
      const httpError = handleDatabaseError(validationError);
      
      expect(httpError.statusCode).toBe(400);
      expect(httpError.message).toBe('bad form data');
      expect(httpError.context).toEqual([
        { path: 'username', message: 'Username is required' },
        { path: 'email', message: 'Email is invalid' }
      ]);
    });
    
    it('should handle cast errors', () => {
      const castError = new CastError('_id', 'invalid-id');
      
      const httpError = handleDatabaseError(castError);
      
      expect(httpError.statusCode).toBe(400);
      expect(httpError.message).toBe('bad form data');
      expect(httpError.context).toBe('Failed casting invalid-id to ObjectId at path _id');
    });
  });
  
  describe('MongoDB connection errors', () => {
    it('should handle network errors', () => {
      const networkError = new Error('Connection refused');
      networkError.name = 'MongoNetworkError';
      
      const httpError = handleDatabaseError(networkError);
      
      expect(httpError.statusCode).toBe(503);
      expect(httpError.message).toBe('service unavailable, try again');
      expect(httpError.context).toBe('Database connection error: Connection refused');
    });
    
    it('should handle timeout errors', () => {
      const timeoutError = new Error('Operation timed out');
      timeoutError.name = 'MongoTimeoutError';
      
      const httpError = handleDatabaseError(timeoutError);
      
      expect(httpError.statusCode).toBe(500);
      expect(httpError.message).toBe('something went wrong internally, someone should have been notified');
      expect(httpError.context).toBe('Database operation timed out: Operation timed out');
    });
  });
  
  describe('MongoDB duplicate key errors', () => {
    it('should handle duplicate key errors', () => {
      const duplicateKeyError = {
        name: 'MongoError',
        code: 11000,
        keyValue: { email: '<EMAIL>' }
      };
      
      const httpError = handleDatabaseError(duplicateKeyError);
      
      expect(httpError.statusCode).toBe(409);
      expect(httpError.message).toBe('the item you want to add already exists');
      expect(httpError.context).toBe('email "<EMAIL>" already exists');
    });
  });
  
  describe('Other database errors', () => {
    it('should handle unknown database errors', () => {
      const unknownError = new Error('Unknown database error');
      
      const httpError = handleDatabaseError(unknownError);
      
      expect(httpError.statusCode).toBe(500);
      expect(httpError.message).toBe('something went wrong internally, someone should have been notified');
      expect(httpError.context).toBe('Database error: Unknown database error');
    });
  });
  
  describe('Database operation with error handling', () => {
    // Create a mock database operation
    async function createUser(userData: any) {
      try {
        // Validate user data
        if (!userData.username) {
          throw new ValidationError({
            username: { message: 'Username is required' }
          });
        }
        
        if (!userData.email) {
          throw new ValidationError({
            email: { message: 'Email is required' }
          });
        }
        
        // Check for duplicate email
        if (userData.email === '<EMAIL>') {
          throw {
            name: 'MongoError',
            code: 11000,
            keyValue: { email: userData.email }
          };
        }
        
        // Return created user
        return {
          id: '123',
          ...userData
        };
      } catch (err) {
        // Convert database error to HTTP error
        throw handleDatabaseError(err);
      }
    }
    
    it('should create a user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>'
      };
      
      const user = await createUser(userData);
      
      expect(user).toEqual({
        id: '123',
        username: 'testuser',
        email: '<EMAIL>'
      });
    });
    
    it('should handle validation errors', async () => {
      const userData = {
        email: '<EMAIL>'
        // Missing username
      };
      
      await expect(createUser(userData)).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data',
          context: [
            { path: 'username', message: 'Username is required' }
          ]
        })
      );
    });
    
    it('should handle duplicate key errors', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>'
      };
      
      await expect(createUser(userData)).rejects.toEqual(
        expect.objectContaining({
          statusCode: 409,
          message: 'the item you want to add already exists',
          context: 'email "<EMAIL>" already exists'
        })
      );
    });
  });
  
  describe('Transaction error handling', () => {
    // Create a mock transaction function
    async function runTransaction(operations: Array<() => Promise<any>>) {
      const results: any[] = [];
      
      try {
        // Start transaction
        console.log('Starting transaction');
        
        // Run operations
        for (const operation of operations) {
          const result = await operation();
          results.push(result);
        }
        
        // Commit transaction
        console.log('Committing transaction');
        
        return results;
      } catch (err) {
        // Abort transaction
        console.log('Aborting transaction');
        
        // Convert database error to HTTP error
        throw handleDatabaseError(err);
      }
    }
    
    it('should run all operations successfully', async () => {
      const operations = [
        () => Promise.resolve({ id: '1', name: 'Operation 1' }),
        () => Promise.resolve({ id: '2', name: 'Operation 2' })
      ];
      
      const results = await runTransaction(operations);
      
      expect(results).toEqual([
        { id: '1', name: 'Operation 1' },
        { id: '2', name: 'Operation 2' }
      ]);
    });
    
    it('should abort transaction on error', async () => {
      const consoleLogSpy = vi.spyOn(console, 'log');
      
      const operations = [
        () => Promise.resolve({ id: '1', name: 'Operation 1' }),
        () => Promise.reject(new ValidationError({
          field: { message: 'Field is required' }
        })),
        () => Promise.resolve({ id: '3', name: 'Operation 3' })
      ];
      
      await expect(runTransaction(operations)).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data',
          context: [
            { path: 'field', message: 'Field is required' }
          ]
        })
      );
      
      expect(consoleLogSpy).toHaveBeenCalledWith('Starting transaction');
      expect(consoleLogSpy).toHaveBeenCalledWith('Aborting transaction');
      expect(consoleLogSpy).not.toHaveBeenCalledWith('Committing transaction');
    });
  });
});
