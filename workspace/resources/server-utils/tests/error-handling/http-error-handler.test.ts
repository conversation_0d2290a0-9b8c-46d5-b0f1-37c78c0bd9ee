import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, HTTP_ERRORS_WITH_STACK } from '../../src/http-request/errors';
import { IncomingMessage, ServerResponse } from 'http';

// Create a mock HTTP error handler
function handleHttpError(err: any, res: ServerResponse) {
  if (typeof err !== 'object' || err === null) {
    err = {
      statusCode: 500,
      message: 'Unknown error',
      context: err
    };
  }

  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';
  const context = err.context || undefined;

  res.statusCode = statusCode;
  res.setHeader('Content-Type', 'application/json');
  res.end(JSON.stringify({
    status: 'error',
    message,
    context
  }));
}

describe('HTTP Error Handler', () => {
  let mockRes: ServerResponse;

  beforeEach(() => {
    // Create a mock response
    mockRes = {
      statusCode: 200,
      setHeader: vi.fn(),
      end: vi.fn()
    } as unknown as ServerResponse;
  });

  describe('Error handling in HTTP responses', () => {
    it('should set the correct status code for HTTP_ERRORS', () => {
      handleHttpError(HTTP_ERRORS.BAD_FORM, mockRes);
      expect(mockRes.statusCode).toBe(400);

      handleHttpError(HTTP_ERRORS.UNAUTHORIZED, mockRes);
      expect(mockRes.statusCode).toBe(401);

      handleHttpError(HTTP_ERRORS.FORBIDDEN, mockRes);
      expect(mockRes.statusCode).toBe(403);

      handleHttpError(HTTP_ERRORS.NOT_FOUND, mockRes);
      expect(mockRes.statusCode).toBe(404);

      handleHttpError(HTTP_ERRORS.SERVER_ERROR, mockRes);
      expect(mockRes.statusCode).toBe(500);
    });

    it('should set the correct status code for HTTP_ERRORS_WITH_CONTEXT', () => {
      handleHttpError(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM({ field: 'username' }), mockRes);
      expect(mockRes.statusCode).toBe(400);

      handleHttpError(HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED({ token: 'invalid' }), mockRes);
      expect(mockRes.statusCode).toBe(401);
    });

    it('should set the correct status code for HTTP_ERRORS_WITH_STACK', () => {
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      handleHttpError(new ForbiddenError(), mockRes);
      expect(mockRes.statusCode).toBe(403);

      const NotFoundError = HTTP_ERRORS_WITH_STACK.NOT_FOUND;
      handleHttpError(new NotFoundError(), mockRes);
      expect(mockRes.statusCode).toBe(404);
    });

    it('should set the Content-Type header to application/json', () => {
      handleHttpError(HTTP_ERRORS.BAD_FORM, mockRes);
      expect(mockRes.setHeader).toHaveBeenCalledWith('Content-Type', 'application/json');
    });

    it('should include the error message in the response', () => {
      handleHttpError(HTTP_ERRORS.BAD_FORM, mockRes);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('bad form data');
    });

    it('should include the error context in the response if available', () => {
      handleHttpError(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM({ field: 'username' }), mockRes);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.context).toEqual({ field: 'username' });
    });

    it('should handle non-object errors', () => {
      handleHttpError('string error', mockRes);

      expect(mockRes.statusCode).toBe(500);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Unknown error');
      expect(responseBody.context).toBe('string error');
    });

    it('should handle null errors', () => {
      handleHttpError(null, mockRes);

      expect(mockRes.statusCode).toBe(500);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Unknown error');
      expect(responseBody.context).toBe(undefined);
    });

    it('should handle errors without a statusCode', () => {
      handleHttpError({ message: 'Custom error' }, mockRes);

      expect(mockRes.statusCode).toBe(500);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Custom error');
    });

    it('should handle errors without a message', () => {
      handleHttpError({ statusCode: 400 }, mockRes);

      expect(mockRes.statusCode).toBe(400);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Internal Server Error');
    });
  });

  describe('Error handling in middleware chains', () => {
    it('should handle errors in middleware chains', () => {
      // Create a mock middleware function
      const middleware = (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
        try {
          // Simulate an error
          throw HTTP_ERRORS.UNAUTHORIZED;
        } catch (err) {
          next(err);
        }
      };

      // Create a mock next function
      const mockNext = (err?: any) => {
        if (err) {
          handleHttpError(err, mockRes);
        }
      };

      // Call the middleware
      middleware({} as IncomingMessage, mockRes, mockNext);

      // Check that the error was handled correctly
      expect(mockRes.statusCode).toBe(401);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('unauthorized');
    });

    it('should handle async errors in middleware chains', async () => {
      // Create a mock async middleware function
      const asyncMiddleware = async (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
        try {
          // Simulate an async error
          await Promise.reject(HTTP_ERRORS.FORBIDDEN);
        } catch (err) {
          next(err);
        }
      };

      // Create a mock next function
      const mockNext = (err?: any) => {
        if (err) {
          handleHttpError(err, mockRes);
        }
      };

      // Call the middleware
      await asyncMiddleware({} as IncomingMessage, mockRes, mockNext);

      // Check that the error was handled correctly
      expect(mockRes.statusCode).toBe(403);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('forbidden');
    });
  });

  describe('Error handling with custom error classes', () => {
    // Create a custom error class
    class ValidationError extends Error {
      statusCode = 400;
      constructor(public context: Record<string, any>) {
        super('Validation failed');
        this.name = 'ValidationError';
      }
    }

    it('should handle custom error classes', () => {
      const validationError = new ValidationError({ field: 'username', message: 'Username is required' });

      handleHttpError(validationError, mockRes);

      expect(mockRes.statusCode).toBe(400);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Validation failed');
      expect(responseBody.context).toEqual({ field: 'username', message: 'Username is required' });
    });

    it('should handle errors with a status property instead of statusCode', () => {
      const errorWithStatus = {
        status: 429,
        message: 'Too Many Requests',
        context: { retryAfter: 60 }
      };

      // Modify the handleHttpError function to handle status property
      function handleHttpErrorWithStatus(err: any, res: ServerResponse) {
        if (typeof err !== 'object' || err === null) {
          err = {
            statusCode: 500,
            message: 'Unknown error',
            context: err
          };
        }

        const statusCode = err.statusCode || err.status || 500;
        const message = err.message || 'Internal Server Error';
        const context = err.context || undefined;

        res.statusCode = statusCode;
        res.setHeader('Content-Type', 'application/json');
        res.end(JSON.stringify({
          status: 'error',
          message,
          context
        }));
      }

      handleHttpErrorWithStatus(errorWithStatus, mockRes);

      expect(mockRes.statusCode).toBe(429);

      const responseBody = JSON.parse((mockRes.end as any).mock.calls[0][0]);
      expect(responseBody.message).toBe('Too Many Requests');
      expect(responseBody.context).toEqual({ retryAfter: 60 });
    });
  });
});
