import { describe, it, expect, vi } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, HTTP_ERRORS_WITH_STACK } from '../../src/http-request/errors';

describe('Error Formatter', () => {
  describe('Error formatting for API responses', () => {
    it('should format errors with consistent structure', () => {
      // Create a function that formats errors for API responses
      const formatErrorForResponse = (err: any) => {
        if (typeof err !== 'object' || err === null) {
          err = {
            statusCode: 500,
            message: 'Unknown error',
            context: err
          };
        }

        const statusCode = err.statusCode || 500;
        const message = err.message || 'Internal Server Error';
        const context = err.context || undefined;

        return {
          status: 'error',
          statusCode,
          message,
          context
        };
      };

      // Test with HTTP_ERRORS
      const unauthorizedError = HTTP_ERRORS.UNAUTHORIZED;
      const formattedUnauthorized = formatErrorForResponse(unauthorizedError);

      expect(formattedUnauthorized).toEqual({
        status: 'error',
        statusCode: 401,
        message: 'unauthorized',
        context: undefined
      });

      // Test with HTTP_ERRORS_WITH_CONTEXT
      const forbiddenWithContext = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({
        requiredRole: 'admin',
        userRole: 'user'
      });
      const formattedForbidden = formatErrorForResponse(forbiddenWithContext);

      expect(formattedForbidden).toEqual({
        status: 'error',
        statusCode: 403,
        message: 'forbidden',
        context: { requiredRole: 'admin', userRole: 'user' }
      });

      // Test with HTTP_ERRORS_WITH_STACK
      const NotFoundError = HTTP_ERRORS_WITH_STACK.NOT_FOUND;
      const notFoundWithStack = new NotFoundError({ resourceId: 'document-123' });
      const formattedNotFound = formatErrorForResponse(notFoundWithStack);

      expect(formattedNotFound).toEqual({
        status: 'error',
        statusCode: 404,
        message: 'not found',
        context: { resourceId: 'document-123' }
      });

      // Test with non-object error
      const stringError = 'Something went wrong';
      const formattedStringError = formatErrorForResponse(stringError);

      expect(formattedStringError).toEqual({
        status: 'error',
        statusCode: 500,
        message: 'Unknown error',
        context: 'Something went wrong'
      });

      // Test with null error
      const nullError = null;
      const formattedNullError = formatErrorForResponse(nullError);

      expect(formattedNullError).toEqual({
        status: 'error',
        statusCode: 500,
        message: 'Unknown error',
        context: undefined
      });
    });
  });

  describe('Error chaining and propagation', () => {
    it('should preserve error context when propagating errors', () => {
      // Create a function that wraps errors with additional context
      const wrapError = (originalError: any, additionalContext: any) => {
        if (typeof originalError !== 'object' || originalError === null) {
          return HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR({
            originalError,
            ...additionalContext
          });
        }

        const statusCode = originalError.statusCode || 500;
        const message = originalError.message || 'Internal Server Error';
        const originalContext = originalError.context || undefined;

        return {
          statusCode,
          message,
          context: {
            ...originalContext,
            ...additionalContext
          }
        };
      };

      // Test with HTTP_ERRORS
      const notFoundError = HTTP_ERRORS.NOT_FOUND;
      const wrappedNotFound = wrapError(notFoundError, {
        location: 'UserController.getUser',
        userId: '123'
      });

      expect(wrappedNotFound).toEqual({
        statusCode: 404,
        message: 'not found',
        context: {
          location: 'UserController.getUser',
          userId: '123'
        }
      });

      // Test with HTTP_ERRORS_WITH_CONTEXT
      const forbiddenWithContext = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({
        requiredRole: 'admin'
      });
      const wrappedForbidden = wrapError(forbiddenWithContext, {
        location: 'DocumentController.updateDocument',
        documentId: '456'
      });

      expect(wrappedForbidden).toEqual({
        statusCode: 403,
        message: 'forbidden',
        context: {
          requiredRole: 'admin',
          location: 'DocumentController.updateDocument',
          documentId: '456'
        }
      });

      // Test with string error
      const stringError = 'Database connection failed';
      const wrappedStringError = wrapError(stringError, {
        location: 'DatabaseService.connect',
        attempt: 3
      });

      expect(wrappedStringError).toEqual({
        statusCode: 500,
        message: 'something went wrong internally, someone should have been notified',
        context: {
          originalError: 'Database connection failed',
          location: 'DatabaseService.connect',
          attempt: 3
        }
      });
    });
  });

  describe('Error classification', () => {
    it('should classify errors by type', () => {
      // Create a function that classifies errors
      const classifyError = (err: any) => {
        if (typeof err !== 'object' || err === null) {
          return 'UNKNOWN_ERROR';
        }

        if (err.statusCode === 400) return 'VALIDATION_ERROR';
        if (err.statusCode === 401) return 'AUTHENTICATION_ERROR';
        if (err.statusCode === 403) return 'AUTHORIZATION_ERROR';
        if (err.statusCode === 404) return 'NOT_FOUND_ERROR';
        if (err.statusCode >= 500) return 'SERVER_ERROR';

        return 'UNKNOWN_ERROR';
      };

      // Test with HTTP_ERRORS
      expect(classifyError(HTTP_ERRORS.BAD_FORM)).toBe('VALIDATION_ERROR');
      expect(classifyError(HTTP_ERRORS.UNAUTHORIZED)).toBe('AUTHENTICATION_ERROR');
      expect(classifyError(HTTP_ERRORS.FORBIDDEN)).toBe('AUTHORIZATION_ERROR');
      expect(classifyError(HTTP_ERRORS.NOT_FOUND)).toBe('NOT_FOUND_ERROR');
      expect(classifyError(HTTP_ERRORS.SERVER_ERROR)).toBe('SERVER_ERROR');

      // Test with HTTP_ERRORS_WITH_CONTEXT
      expect(classifyError(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM({ field: 'username' }))).toBe('VALIDATION_ERROR');
      expect(classifyError(HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED({ token: 'invalid' }))).toBe('AUTHENTICATION_ERROR');

      // Test with HTTP_ERRORS_WITH_STACK
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      expect(classifyError(new ForbiddenError())).toBe('AUTHORIZATION_ERROR');

      // Test with non-object error
      expect(classifyError('string error')).toBe('UNKNOWN_ERROR');
      expect(classifyError(null)).toBe('UNKNOWN_ERROR');
    });
  });

  describe('Error serialization', () => {
    it('should serialize errors for logging', () => {
      // Create a function that serializes errors for logging
      const serializeError = (err: any) => {
        if (typeof err !== 'object' || err === null) {
          return {
            type: 'non-object-error',
            value: String(err)
          };
        }

        const serialized: Record<string, any> = {
          message: err.message || 'Unknown error',
          statusCode: err.statusCode,
          name: err.name,
          stack: err.stack
        };

        if (err.context) {
          serialized.context = err.context;
        }

        return serialized;
      };

      // Test with HTTP_ERRORS
      const serializedUnauthorized = serializeError(HTTP_ERRORS.UNAUTHORIZED);
      expect(serializedUnauthorized.message).toBe('unauthorized');
      expect(serializedUnauthorized.statusCode).toBe(401);

      // Test with HTTP_ERRORS_WITH_CONTEXT
      const forbiddenWithContext = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({
        requiredRole: 'admin',
        userRole: 'user'
      });
      const serializedForbidden = serializeError(forbiddenWithContext);
      expect(serializedForbidden.message).toBe('forbidden');
      expect(serializedForbidden.statusCode).toBe(403);
      expect(serializedForbidden.context).toEqual({
        requiredRole: 'admin',
        userRole: 'user'
      });

      // Test with HTTP_ERRORS_WITH_STACK
      const NotFoundError = HTTP_ERRORS_WITH_STACK.NOT_FOUND;
      const notFoundWithStack = new NotFoundError({ resourceId: 'document-123' });
      const serializedNotFound = serializeError(notFoundWithStack);
      expect(serializedNotFound.message).toBe('not found');
      expect(serializedNotFound.statusCode).toBe(404);
      expect(serializedNotFound.context).toEqual({ resourceId: 'document-123' });
      expect(serializedNotFound.stack).toBeDefined();

      // Test with non-object error
      const serializedString = serializeError('string error');
      expect(serializedString).toEqual({
        type: 'non-object-error',
        value: 'string error'
      });

      // Test with null error
      const serializedNull = serializeError(null);
      expect(serializedNull).toEqual({
        type: 'non-object-error',
        value: 'null'
      });
    });
  });
});
