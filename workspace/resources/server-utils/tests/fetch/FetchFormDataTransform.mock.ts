import { vi } from 'vitest';
import { PassThrough } from 'stream';

// Create a mock class that mimics the behavior of FetchFormDataTransform
export class FetchFormDataTransform {
  private reqStream = new PassThrough();
  private controller = { abort: vi.fn() };
  public fetchPromise: Promise<any>;

  constructor(url: string, options: any) {
    // Check if mimetype can be determined
    if (options.file.filename.endsWith('.unknown')) {
      throw new Error("Couldn't find the mimetype of the file");
    }

    // Check if file key is also in body
    if (options.body && options.body[options.file.key]) {
      throw new Error(`Key (${options.file.key}) on the body is the same as the file's key`);
    }

    // Mock the fetchPromise
    this.fetchPromise = Promise.resolve({ ok: true, json: () => Promise.resolve({ success: true }) });
  }

  _write(chunk: string, encoding: BufferEncoding | undefined, callback: (e?: any) => any) {
    this.reqStream.write(chunk, encoding as BufferEncoding, callback);
  }

  _final(callback: (e?: any) => any) {
    this.reqStream.end();
    callback();
  }

  _read() {}

  cancel() {
    this.controller.abort();
  }
}
