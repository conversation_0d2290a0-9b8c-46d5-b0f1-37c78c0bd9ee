import { describe, it, expect, vi, beforeEach } from 'vitest';
import { httpUpgradeToWSRequest } from '../../src/ws/WebSocketRequest';
import { WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { Duplex } from 'stream';

describe('WebSocketRequest', () => {
  let mockServer;
  let mockRequest;
  let mockSocket;
  let mockHead;
  let wsRequest;

  beforeEach(() => {
    // Create a mock WebSocketServer
    mockServer = {
      handleUpgrade: vi.fn((req, socket, head, callback) => {
        callback({
          send: vi.fn(),
          close: vi.fn()
        });
      })
    } as unknown as WebSocketServer;

    // Create a mock IncomingMessage
    mockRequest = {
      headers: {
        host: 'localhost:8080',
        'sec-websocket-protocol': 'protocol1, protocol2'
      },
      url: '/ws'
    } as unknown as IncomingMessage;

    // Create a mock Duplex stream
    mockSocket = {
      end: vi.fn()
    } as unknown as <PERSON><PERSON>;

    // Create a mock Buffer
    mockHead = Buffer.from([]);

    // Create a WebSocketRequest instance
    wsRequest = httpUpgradeToWSRequest(mockServer, mockRequest, mockSocket, mockHead);
  });

  it('should initialize with the correct properties', () => {
    expect(wsRequest.httpRequest).toBe(mockRequest);
    expect(wsRequest.requestedProtocols).toEqual(['protocol1', 'protocol2']);
    expect(wsRequest.resourceURL.toString()).toBe('ws://localhost:8080/ws');
    expect(typeof wsRequest.accept).toBe('function');
    expect(typeof wsRequest.reject).toBe('function');
  });

  it('should handle accept', async () => {
    const ws = await wsRequest.accept();

    expect(mockServer.handleUpgrade).toHaveBeenCalledWith(
      mockRequest,
      mockSocket,
      mockHead,
      expect.any(Function)
    );

    expect(ws).toBeDefined();
    expect(ws.send).toBeDefined();
    expect(ws.close).toBeDefined();
  });

  it('should throw an error if accept is called twice', async () => {
    await wsRequest.accept();

    await expect(wsRequest.accept()).rejects.toThrow('Already Accepted');
  });

  it('should throw an error if accept is called after reject', () => {
    wsRequest.reject();

    expect(() => wsRequest.accept()).rejects.toThrow('Request already rejected');
  });

  it('should handle reject', () => {
    wsRequest.reject();

    expect(() => wsRequest.reject()).toThrow('Already Rejected');
  });

  it('should throw an error if reject is called after accept', async () => {
    await wsRequest.accept();

    expect(() => wsRequest.reject()).toThrow('Already Accepted');
  });
});
