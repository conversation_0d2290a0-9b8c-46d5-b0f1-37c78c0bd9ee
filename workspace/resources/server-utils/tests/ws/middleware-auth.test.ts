import { describe, it, expect, vi, beforeEach } from 'vitest';
import { WebSocketRequest } from '../../src/ws/WebSocketRequest';
import { WebSocketHandlerCallback } from '../../src/ws/router-callback';
import { IncomingMessage } from 'http';
import { Duplex } from 'stream';

// Mock types for WebSocket authentication
type WebSocketRequestWithAuth = WebSocketRequest & {
  auth?: {
    payload: {
      sub: string;
      scope?: string;
      permissions?: string[];
    };
  };
};

// Create mock middleware functions for WebSocket authentication
const createAuthMiddleware = (requireAuth: boolean = true): WebSocketHandlerCallback => {
  return (req: WebSocketRequest, params, next) => {
    try {
      // Get JWT from URL
      const url = req.httpRequest.url;
      if (typeof url === 'undefined') {
        throw new Error('No URL in request');
      }
      
      const jwt = new URL(url, 'ws://localhost').searchParams.get('jwt');
      
      if (!jwt) {
        if (requireAuth) {
          throw new Error('No JWT token provided');
        } else {
          return next();
        }
      }
      
      // Simulate JWT verification
      let auth = null;
      if (jwt === 'valid-token') {
        auth = {
          payload: {
            sub: 'user-123',
            scope: 'read:documents write:documents',
            permissions: ['read:documents', 'write:documents']
          }
        };
      }
      
      if (!auth && requireAuth) {
        throw new Error('Invalid JWT token');
      }
      
      if (auth) {
        // Add auth info to request
        const reqWithAuth = req as WebSocketRequestWithAuth;
        reqWithAuth.auth = auth;
      }
      
      next();
    } catch (e) {
      if (!requireAuth) {
        next();
      } else {
        next(e);
      }
    }
  };
};

const createPermissionMiddleware = (requiredPermission: string): WebSocketHandlerCallback => {
  return (req: WebSocketRequest, params, next) => {
    try {
      const reqWithAuth = req as WebSocketRequestWithAuth;
      
      if (!reqWithAuth.auth) {
        throw new Error('Unauthorized: No authentication information');
      }
      
      const permissions = reqWithAuth.auth.payload.permissions || [];
      
      if (!permissions.includes(requiredPermission)) {
        throw new Error(`Forbidden: Missing required permission: ${requiredPermission}`);
      }
      
      next();
    } catch (e) {
      next(e);
    }
  };
};

// Create error handling middleware for WebSocket
const errorHandlingMiddleware: WebSocketHandlerCallback = (req, params, next) => {
  try {
    next((err) => {
      if (err) {
        // In a real implementation, this would send an error response to the client
        console.error('WebSocket error:', err);
        req.reject({
          status: err.message.includes('Unauthorized') ? 401 : 403,
          reason: err.message
        });
        return;
      }
      next();
    });
  } catch (e) {
    req.reject({
      status: 500,
      reason: 'Internal server error'
    });
  }
};

describe('WebSocket Authentication Middleware', () => {
  let mockHttpRequest: IncomingMessage;
  let mockSocket: Duplex;
  let mockRequest: WebSocketRequest;
  let mockNext: (err?: any) => void;
  
  beforeEach(() => {
    // Create mock HTTP request
    mockHttpRequest = {
      url: 'ws://localhost/socket?jwt=valid-token',
      headers: {
        host: 'localhost',
        'sec-websocket-protocol': 'json'
      }
    } as IncomingMessage;
    
    // Create mock socket
    mockSocket = {
      end: vi.fn()
    } as unknown as Duplex;
    
    // Create mock WebSocketRequest
    mockRequest = {
      httpRequest: mockHttpRequest,
      requestedProtocols: ['json'],
      resourceURL: new URL(mockHttpRequest.url || '', 'ws://localhost'),
      accept: vi.fn().mockResolvedValue({}),
      reject: vi.fn()
    };
    
    // Create mock next function
    mockNext = vi.fn();
  });
  
  describe('Authentication Middleware', () => {
    it('should pass request when valid token is provided', () => {
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
      expect((mockRequest as WebSocketRequestWithAuth).auth).toBeDefined();
      expect((mockRequest as WebSocketRequestWithAuth).auth?.payload.sub).toBe('user-123');
    });
    
    it('should call next with error when no token is provided', () => {
      mockHttpRequest.url = 'ws://localhost/socket';
      mockRequest.resourceURL = new URL(mockHttpRequest.url, 'ws://localhost');
      
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toBe('No JWT token provided');
    });
    
    it('should call next with error when invalid token is provided', () => {
      mockHttpRequest.url = 'ws://localhost/socket?jwt=invalid-token';
      mockRequest.resourceURL = new URL(mockHttpRequest.url, 'ws://localhost');
      
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toBe('Invalid JWT token');
    });
    
    it('should pass request when auth is optional and no token is provided', () => {
      mockHttpRequest.url = 'ws://localhost/socket';
      mockRequest.resourceURL = new URL(mockHttpRequest.url, 'ws://localhost');
      
      const optionalAuthMiddleware = createAuthMiddleware(false);
      optionalAuthMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
      expect((mockRequest as WebSocketRequestWithAuth).auth).toBeUndefined();
    });
  });
  
  describe('Permission Middleware', () => {
    it('should pass request when user has required permission', () => {
      // Set up auth info
      (mockRequest as WebSocketRequestWithAuth).auth = {
        payload: {
          sub: 'user-123',
          permissions: ['read:documents', 'write:documents']
        }
      };
      
      const permissionMiddleware = createPermissionMiddleware('read:documents');
      permissionMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
    });
    
    it('should call next with error when user lacks required permission', () => {
      // Set up auth info
      (mockRequest as WebSocketRequestWithAuth).auth = {
        payload: {
          sub: 'user-123',
          permissions: ['read:documents']
        }
      };
      
      const permissionMiddleware = createPermissionMiddleware('admin:documents');
      permissionMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toContain('Forbidden');
    });
    
    it('should call next with error when user is not authenticated', () => {
      const permissionMiddleware = createPermissionMiddleware('read:documents');
      permissionMiddleware(mockRequest, {}, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toContain('Unauthorized');
    });
  });
  
  describe('Error Handling Middleware', () => {
    it('should reject connection with 401 for unauthorized errors', () => {
      // Create a middleware that throws an unauthorized error
      const failingMiddleware: WebSocketHandlerCallback = (req, params, next) => {
        next(new Error('Unauthorized: No token provided'));
      };
      
      // Create a next function that simulates the error handler
      const nextWithErrorHandler = (err?: any) => {
        if (err) {
          // This simulates what the error handling middleware would do
          mockRequest.reject({
            status: 401,
            reason: err.message
          });
        }
      };
      
      failingMiddleware(mockRequest, {}, nextWithErrorHandler);
      
      expect(mockRequest.reject).toHaveBeenCalledWith({
        status: 401,
        reason: 'Unauthorized: No token provided'
      });
    });
    
    it('should reject connection with 403 for forbidden errors', () => {
      // Create a middleware that throws a forbidden error
      const failingMiddleware: WebSocketHandlerCallback = (req, params, next) => {
        next(new Error('Forbidden: Insufficient permissions'));
      };
      
      // Create a next function that simulates the error handler
      const nextWithErrorHandler = (err?: any) => {
        if (err) {
          // This simulates what the error handling middleware would do
          mockRequest.reject({
            status: 403,
            reason: err.message
          });
        }
      };
      
      failingMiddleware(mockRequest, {}, nextWithErrorHandler);
      
      expect(mockRequest.reject).toHaveBeenCalledWith({
        status: 403,
        reason: 'Forbidden: Insufficient permissions'
      });
    });
  });
  
  describe('Middleware Chains', () => {
    it('should pass through multiple middleware when all conditions are met', () => {
      // Set up auth info via the auth middleware
      const authMiddleware = createAuthMiddleware();
      const permissionMiddleware = createPermissionMiddleware('read:documents');
      
      // Create a chain of middleware calls
      authMiddleware(mockRequest, {}, (err) => {
        if (err) return mockNext(err);
        permissionMiddleware(mockRequest, {}, mockNext);
      });
      
      expect(mockNext).toHaveBeenCalledWith();
    });
    
    it('should stop at first failing middleware and pass error to next', () => {
      // Set up a request that will fail permission check
      mockHttpRequest.url = 'ws://localhost/socket?jwt=valid-token';
      mockRequest.resourceURL = new URL(mockHttpRequest.url, 'ws://localhost');
      
      const authMiddleware = createAuthMiddleware();
      const permissionMiddleware = createPermissionMiddleware('admin:documents'); // This will fail
      
      // Create a chain of middleware calls
      authMiddleware(mockRequest, {}, (err) => {
        if (err) return mockNext(err);
        permissionMiddleware(mockRequest, {}, mockNext);
      });
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      expect(mockNext.mock.calls[0][0].message).toContain('Forbidden');
    });
    
    it('should handle errors in the middleware chain and reject the connection', () => {
      // Set up a request that will fail authentication
      mockHttpRequest.url = 'ws://localhost/socket?jwt=invalid-token';
      mockRequest.resourceURL = new URL(mockHttpRequest.url, 'ws://localhost');
      
      const authMiddleware = createAuthMiddleware();
      const permissionMiddleware = createPermissionMiddleware('read:documents');
      
      // Create a chain of middleware calls with error handling
      authMiddleware(mockRequest, {}, (err) => {
        if (err) {
          // Simulate error handling middleware
          mockRequest.reject({
            status: 401,
            reason: err.message
          });
          return;
        }
        permissionMiddleware(mockRequest, {}, (err) => {
          if (err) {
            // Simulate error handling middleware
            mockRequest.reject({
              status: 403,
              reason: err.message
            });
            return;
          }
          mockNext();
        });
      });
      
      expect(mockRequest.reject).toHaveBeenCalledWith({
        status: 401,
        reason: 'Invalid JWT token'
      });
    });
  });
});
