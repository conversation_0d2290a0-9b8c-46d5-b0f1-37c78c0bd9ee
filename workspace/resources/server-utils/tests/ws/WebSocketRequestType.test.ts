import { describe, it, expect, vi, beforeEach } from 'vitest';
import { httpUpgradeToWSRequest, WebSocketRequest } from '../../src/ws/WebSocketRequest';
import { Duplex } from 'stream';
import { IncomingMessage } from 'http';
import { WebSocketServer } from 'ws';

// Mock dependencies
vi.mock('ws', () => ({
  WebSocketServer: vi.fn().mockImplementation(() => ({
    handleUpgrade: vi.fn((request, socket, head, callback) => {
      callback({ send: vi.fn(), close: vi.fn() });
    })
  }))
}));

describe('WebSocketRequest', () => {
  let mockServer: WebSocketServer;
  let mockRequest: IncomingMessage;
  let mockSocket: Duplex;
  let mockHead: Buffer;
  let wsRequest: WebSocketRequest;

  beforeEach(() => {
    // Create mock objects
    mockServer = new WebSocketServer();
    mockRequest = {
      headers: {
        host: 'localhost:8080',
        'sec-websocket-protocol': 'chat, echo'
      },
      url: '/ws'
    } as unknown as IncomingMessage;
    mockSocket = {
      end: vi.fn()
    } as unknown as Duplex;
    mockHead = Buffer.from([]);

    // Create a WebSocketRequest
    wsRequest = httpUpgradeToWSRequest(mockServer, mockRequest, mockSocket, mockHead);
  });

  it('should create a WebSocketRequest object with the correct properties', () => {
    expect(wsRequest.httpRequest).toBe(mockRequest);
    expect(wsRequest.resourceURL.toString()).toBe('ws://localhost:8080/ws');
    expect(wsRequest.requestedProtocols).toEqual(['chat', 'echo']);
  });

  it('should have an accept method that returns a Promise', async () => {
    expect(typeof wsRequest.accept).toBe('function');

    const ws = await wsRequest.accept();

    expect(ws).toBeDefined();
    expect(mockServer.handleUpgrade).toHaveBeenCalledWith(
      mockRequest,
      mockSocket,
      mockHead,
      expect.any(Function)
    );
  });

  it('should throw an error if accept is called twice', async () => {
    await wsRequest.accept();

    await expect(wsRequest.accept()).rejects.toThrow('Already Accepted');
  });

  it('should have a reject method', () => {
    expect(typeof wsRequest.reject).toBe('function');

    // We can't fully test the reject method without mocking handleReject
    // But we can at least call it to ensure it doesn't throw
    expect(() => wsRequest.reject()).not.toThrow();
  });

  it('should throw an error if reject is called after accept', async () => {
    await wsRequest.accept();

    expect(() => wsRequest.reject()).toThrow('Already Accepted');
  });

  it('should throw an error if reject is called twice', () => {
    wsRequest.reject();

    expect(() => wsRequest.reject()).toThrow('Already Rejected');
  });

  it('should handle requests with no protocol', () => {
    const requestWithNoProtocol = {
      headers: {
        host: 'localhost:8080'
      },
      url: '/ws'
    } as unknown as IncomingMessage;

    const wsRequestNoProtocol = httpUpgradeToWSRequest(
      mockServer,
      requestWithNoProtocol,
      mockSocket,
      mockHead
    );

    expect(wsRequestNoProtocol.requestedProtocols).toEqual([]);
  });
});
