import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Duplex } from 'stream';
import { handleReject, RejectInfo } from '../../src/ws/reject';

describe('WebSocket Authentication Rejection', () => {
  let mockSocket: Duplex;
  
  beforeEach(() => {
    // Create a mock Duplex stream
    mockSocket = {
      end: vi.fn()
    } as unknown as Duplex;
  });

  it('should reject with 401 Unauthorized status code', () => {
    const rejectInfo: RejectInfo = {
      status: 401,
      reason: 'Authentication required'
    };
    
    handleReject(mockSocket, rejectInfo);
    
    expect(mockSocket.end).toHaveBeenCalled();
    const response = (mockSocket.end as any).mock.calls[0][0];
    
    expect(response).toContain('HTTP/1.1 401 Unauthorized');
    expect(response).toContain('Connection: close');
    expect(response).toContain('X-WebSocket-Reject-Reason: Authentication required');
  });

  it('should reject with 403 Forbidden status code', () => {
    const rejectInfo: RejectInfo = {
      status: 403,
      reason: 'Insufficient permissions'
    };
    
    handleReject(mockSocket, rejectInfo);
    
    expect(mockSocket.end).toHaveBeenCalled();
    const response = (mockSocket.end as any).mock.calls[0][0];
    
    expect(response).toContain('HTTP/1.1 403 Forbidden');
    expect(response).toContain('Connection: close');
    expect(response).toContain('X-WebSocket-Reject-Reason: Insufficient permissions');
  });

  it('should default to 403 Forbidden if status is not provided', () => {
    const rejectInfo: RejectInfo = {
      reason: 'Access denied'
    };
    
    handleReject(mockSocket, rejectInfo);
    
    expect(mockSocket.end).toHaveBeenCalled();
    const response = (mockSocket.end as any).mock.calls[0][0];
    
    expect(response).toContain('HTTP/1.1 403 Forbidden');
  });

  it('should include extra headers if provided', () => {
    const rejectInfo: RejectInfo = {
      status: 401,
      reason: 'Token expired',
      extraHeaders: {
        'WWW-Authenticate': 'Bearer realm="api"',
        'Retry-After': '3600'
      }
    };
    
    handleReject(mockSocket, rejectInfo);
    
    expect(mockSocket.end).toHaveBeenCalled();
    const response = (mockSocket.end as any).mock.calls[0][0];
    
    expect(response).toContain('HTTP/1.1 401 Unauthorized');
    expect(response).toContain('WWW-Authenticate: Bearer realm="api"');
    expect(response).toContain('Retry-After: 3600');
  });

  it('should sanitize header values', () => {
    const rejectInfo: RejectInfo = {
      status: 401,
      reason: 'Invalid\r\ntoken',
      extraHeaders: {
        'X-Custom-Header': 'Value\r\nwith\r\nnewlines'
      }
    };
    
    handleReject(mockSocket, rejectInfo);
    
    expect(mockSocket.end).toHaveBeenCalled();
    const response = (mockSocket.end as any).mock.calls[0][0];
    
    // Should not contain newlines in header values
    expect(response).not.toContain('Invalid\r\ntoken');
    expect(response).not.toContain('Value\r\nwith\r\nnewlines');
    
    // Should contain sanitized values
    expect(response).toContain('X-WebSocket-Reject-Reason: Invalidtoken');
    expect(response).toContain('X-Custom-Header: Valuewithnewlines');
  });
});
