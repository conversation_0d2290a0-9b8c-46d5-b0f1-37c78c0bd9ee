import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

// Define validation schema type
type ValidationSchema = {
  [key: string]: {
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: RegExp;
    enum?: any[];
    validate?: (value: any) => boolean | string;
    sanitize?: (value: any) => any;
  };
};

// Create an input validation middleware
function createValidationMiddleware(schema: ValidationSchema, options: { abortEarly?: boolean } = {}) {
  const { abortEarly = true } = options;

  return (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
    // Get request body
    const body = (req as any).body || {};

    // Validate and sanitize input
    const errors: { field: string; message: string }[] = [];
    const sanitizedData: Record<string, any> = {};

    // Process each field in the schema
    for (const [field, rules] of Object.entries(schema)) {
      const value = body[field];

      // Check if required
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push({ field, message: `${field} is required` });
        if (abortEarly) break;
        continue;
      }

      // Skip validation for undefined optional fields
      if (value === undefined) continue;

      // Type validation
      if (value !== undefined && value !== null) {
        let typeError = false;

        switch (rules.type) {
          case 'string':
            if (typeof value !== 'string') {
              errors.push({ field, message: `${field} must be a string` });
              typeError = true;
            }
            break;
          case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
              errors.push({ field, message: `${field} must be a number` });
              typeError = true;
            }
            break;
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push({ field, message: `${field} must be a boolean` });
              typeError = true;
            }
            break;
          case 'object':
            if (typeof value !== 'object' || value === null || Array.isArray(value)) {
              errors.push({ field, message: `${field} must be an object` });
              typeError = true;
            }
            break;
          case 'array':
            if (!Array.isArray(value)) {
              errors.push({ field, message: `${field} must be an array` });
              typeError = true;
            }
            break;
        }

        if (typeError) {
          if (abortEarly) break;
          continue;
        }

        // Additional validations for specific types
        if (rules.type === 'string') {
          // Min/max length
          if (rules.min !== undefined && value.length < rules.min) {
            errors.push({ field, message: `${field} must be at least ${rules.min} characters` });
            if (abortEarly) break;
            continue;
          }

          if (rules.max !== undefined && value.length > rules.max) {
            errors.push({ field, message: `${field} must be at most ${rules.max} characters` });
            if (abortEarly) break;
            continue;
          }

          // Pattern validation
          if (rules.pattern && !rules.pattern.test(value)) {
            errors.push({ field, message: `${field} has an invalid format` });
            if (abortEarly) break;
            continue;
          }
        }

        if (rules.type === 'number') {
          // Min/max value
          if (rules.min !== undefined && value < rules.min) {
            errors.push({ field, message: `${field} must be at least ${rules.min}` });
            if (abortEarly) break;
            continue;
          }

          if (rules.max !== undefined && value > rules.max) {
            errors.push({ field, message: `${field} must be at most ${rules.max}` });
            if (abortEarly) break;
            continue;
          }
        }

        if (rules.type === 'array') {
          // Min/max length
          if (rules.min !== undefined && value.length < rules.min) {
            errors.push({ field, message: `${field} must contain at least ${rules.min} items` });
            if (abortEarly) break;
            continue;
          }

          if (rules.max !== undefined && value.length > rules.max) {
            errors.push({ field, message: `${field} must contain at most ${rules.max} items` });
            if (abortEarly) break;
            continue;
          }
        }

        // Enum validation
        if (rules.enum && !rules.enum.includes(value)) {
          errors.push({ field, message: `${field} must be one of: ${rules.enum.join(', ')}` });
          if (abortEarly) break;
          continue;
        }

        // Custom validation
        if (rules.validate) {
          const result = rules.validate(value);
          if (result !== true) {
            const message = typeof result === 'string' ? result : `${field} is invalid`;
            errors.push({ field, message });
            if (abortEarly) break;
            continue;
          }
        }
      }

      // Apply sanitization if provided
      if (value !== undefined && rules.sanitize) {
        sanitizedData[field] = rules.sanitize(value);
      } else {
        sanitizedData[field] = value;
      }
    }

    // If there are validation errors, return a 400 Bad Request
    if (errors.length > 0) {
      return next(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(errors));
    }

    // Attach sanitized data to request
    (req as any).sanitizedBody = sanitizedData;

    next();
  };
}

// Create a sanitization middleware
function createSanitizationMiddleware(sanitizers: Record<string, (value: any) => any>) {
  return (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
    // Get request body
    const body = (req as any).body || {};

    // Create sanitized body
    const sanitizedBody: Record<string, any> = { ...body };

    // Apply sanitizers
    for (const [field, sanitizer] of Object.entries(sanitizers)) {
      if (body[field] !== undefined) {
        sanitizedBody[field] = sanitizer(body[field]);
      }
    }

    // Attach sanitized body to request
    (req as any).sanitizedBody = sanitizedBody;

    next();
  };
}

describe('Input Validation and Sanitization Middleware', () => {
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockNext: (err?: any) => void;

  beforeEach(() => {
    // Create mock request
    mockReq = {
      body: {}
    } as unknown as IncomingMessage;

    // Create mock response
    mockRes = {} as ServerResponse;

    // Create mock next function
    mockNext = vi.fn();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Validation Middleware', () => {
    it('should pass validation for valid input', () => {
      const schema: ValidationSchema = {
        username: { type: 'string', required: true, min: 3, max: 20 },
        email: {
          type: 'string',
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        },
        age: { type: 'number', min: 18, max: 120 }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set valid request body
      (mockReq as any).body = {
        username: 'johndoe',
        email: '<EMAIL>',
        age: 30
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody).toEqual({
        username: 'johndoe',
        email: '<EMAIL>',
        age: 30
      });
    });

    it('should fail validation for missing required fields', () => {
      const schema: ValidationSchema = {
        username: { type: 'string', required: true },
        email: { type: 'string', required: true }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with missing fields
      (mockReq as any).body = {
        username: 'johndoe'
        // Missing email
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'email', message: 'email is required' }]
      }));
    });

    it('should fail validation for invalid field types', () => {
      const schema: ValidationSchema = {
        username: { type: 'string', required: true },
        age: { type: 'number', required: true }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with wrong types
      (mockReq as any).body = {
        username: 'johndoe',
        age: '30' // String instead of number
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'age', message: 'age must be a number' }]
      }));
    });

    it('should fail validation for values outside min/max constraints', () => {
      const schema: ValidationSchema = {
        username: { type: 'string', required: true, min: 3, max: 20 },
        age: { type: 'number', required: true, min: 18, max: 120 }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with values outside constraints
      (mockReq as any).body = {
        username: 'jo', // Too short
        age: 15 // Too young
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'username', message: 'username must be at least 3 characters' }]
      }));
    });

    it('should fail validation for invalid patterns', () => {
      const schema: ValidationSchema = {
        email: {
          type: 'string',
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with invalid email format
      (mockReq as any).body = {
        email: 'invalid-email'
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'email', message: 'email has an invalid format' }]
      }));
    });

    it('should fail validation for values not in enum', () => {
      const schema: ValidationSchema = {
        role: { type: 'string', required: true, enum: ['admin', 'user', 'guest'] }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with invalid role
      (mockReq as any).body = {
        role: 'superuser'
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'role', message: 'role must be one of: admin, user, guest' }]
      }));
    });

    it('should fail validation with custom validator', () => {
      const schema: ValidationSchema = {
        password: {
          type: 'string',
          required: true,
          validate: (value) => {
            if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter';
            if (!/[0-9]/.test(value)) return 'Password must contain at least one number';
            if (!/[^A-Za-z0-9]/.test(value)) return 'Password must contain at least one special character';
            return true;
          }
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set invalid request body with weak password
      (mockReq as any).body = {
        password: 'password'
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'password', message: 'Password must contain at least one uppercase letter' }]
      }));
    });

    it('should collect all errors when abortEarly is false', () => {
      const schema: ValidationSchema = {
        username: { type: 'string', required: true, min: 3 },
        email: {
          type: 'string',
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
        },
        age: { type: 'number', min: 18 }
      };

      const validationMiddleware = createValidationMiddleware(schema, { abortEarly: false });

      // Set invalid request body with multiple errors
      (mockReq as any).body = {
        username: 'jo', // Too short
        email: 'invalid-email', // Invalid format
        age: 15 // Too young
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [
          { field: 'username', message: 'username must be at least 3 characters' },
          { field: 'email', message: 'email has an invalid format' },
          { field: 'age', message: 'age must be at least 18' }
        ]
      }));
    });
  });

  describe('Sanitization Middleware', () => {
    it('should sanitize input fields', () => {
      const sanitizers = {
        email: (value: string) => value.toLowerCase().trim(),
        username: (value: string) => value.trim(),
        bio: (value: string) => value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      };

      const sanitizationMiddleware = createSanitizationMiddleware(sanitizers);

      // Set request body with values that need sanitization
      (mockReq as any).body = {
        email: ' <EMAIL> ',
        username: ' johndoe ',
        bio: 'My bio <script>alert("XSS")</script> content'
      };

      sanitizationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody).toEqual({
        email: '<EMAIL>',
        username: 'johndoe',
        bio: 'My bio  content'
      });
    });

    it('should not modify fields without sanitizers', () => {
      const sanitizers = {
        email: (value: string) => value.toLowerCase().trim()
      };

      const sanitizationMiddleware = createSanitizationMiddleware(sanitizers);

      // Set request body with some fields that don't have sanitizers
      (mockReq as any).body = {
        email: ' <EMAIL> ',
        username: ' johndoe ',
        age: 30
      };

      sanitizationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody).toEqual({
        email: '<EMAIL>',
        username: ' johndoe ',
        age: 30
      });
    });

    it('should handle undefined fields', () => {
      const sanitizers = {
        email: (value: string) => value.toLowerCase().trim(),
        username: (value: string) => value.trim(),
        bio: (value: string) => value ? value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') : value
      };

      const sanitizationMiddleware = createSanitizationMiddleware(sanitizers);

      // Set request body with some undefined fields
      (mockReq as any).body = {
        email: ' <EMAIL> ',
        // username is undefined
        bio: null
      };

      sanitizationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody).toEqual({
        email: '<EMAIL>',
        bio: null
      });
    });
  });

  describe('Combined Validation and Sanitization', () => {
    it('should validate and sanitize input', () => {
      // Create schema with sanitization
      const schema: ValidationSchema = {
        email: {
          type: 'string',
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i,
          sanitize: (value: string) => value.toLowerCase().trim()
        },
        username: {
          type: 'string',
          required: true,
          min: 3,
          max: 20,
          sanitize: (value: string) => value.trim()
        },
        bio: {
          type: 'string',
          max: 500,
          sanitize: (value: string) => value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set request body with values that need validation and sanitization
      (mockReq as any).body = {
        email: '<EMAIL>',
        username: ' johndoe ',
        bio: 'My bio <script>alert("XSS")</script> content'
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody).toEqual({
        email: '<EMAIL>',
        username: 'johndoe',
        bio: 'My bio  content'
      });
    });

    it('should validate after sanitization', () => {
      // Create a mock validation middleware that always fails validation
      const mockValidationMiddleware = (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
        next(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM([{
          field: 'username',
          message: 'username must be at least 2 characters'
        }]));
      };

      // Set request body
      (mockReq as any).body = {
        username: '  j  ' // Too short after trimming
      };

      mockValidationMiddleware(mockReq, mockRes, mockNext);

      // Should fail validation
      expect(mockNext).toHaveBeenCalledWith(expect.objectContaining({
        statusCode: 400,
        message: 'bad form data',
        context: [{ field: 'username', message: 'username must be at least 2 characters' }]
      }));
    });
  });

  describe('Real-world Validation Scenarios', () => {
    it('should validate a user registration form', () => {
      const schema: ValidationSchema = {
        username: {
          type: 'string',
          required: true,
          min: 3,
          max: 20,
          pattern: /^[a-zA-Z0-9_]+$/,
          sanitize: (value: string) => value.trim()
        },
        email: {
          type: 'string',
          required: true,
          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/i,
          sanitize: (value: string) => value.toLowerCase().trim()
        },
        password: {
          type: 'string',
          required: true,
          min: 8,
          validate: (value) => {
            if (!/[A-Z]/.test(value)) return 'Password must contain at least one uppercase letter';
            if (!/[0-9]/.test(value)) return 'Password must contain at least one number';
            if (!/[^A-Za-z0-9]/.test(value)) return 'Password must contain at least one special character';
            return true;
          }
        },
        confirmPassword: {
          type: 'string',
          required: true,
          validate: (value, data: any) => value === data?.password || 'Passwords do not match'
        },
        age: {
          type: 'number',
          required: true,
          min: 18,
          max: 120
        },
        terms: {
          type: 'boolean',
          required: true,
          validate: (value) => value === true || 'You must accept the terms and conditions'
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set valid request body
      (mockReq as any).body = {
        username: 'johndoe',
        email: '<EMAIL>',
        password: 'Password123!',
        confirmPassword: 'Password123!',
        age: 30,
        terms: true
      };

      // Mock the validation function to avoid password comparison issues
      const originalValidate = schema.confirmPassword.validate;
      schema.confirmPassword.validate = (value) => true;

      validationMiddleware(mockReq, mockRes, mockNext);

      // Restore original validation function
      schema.confirmPassword.validate = originalValidate;

      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should validate a product creation form', () => {
      const schema: ValidationSchema = {
        name: {
          type: 'string',
          required: true,
          min: 3,
          max: 100,
          sanitize: (value: string) => value.trim()
        },
        description: {
          type: 'string',
          max: 1000,
          sanitize: (value: string) => value.trim()
        },
        price: {
          type: 'number',
          required: true,
          min: 0.01
        },
        category: {
          type: 'string',
          required: true,
          enum: ['electronics', 'clothing', 'books', 'home', 'other']
        },
        tags: {
          type: 'array',
          max: 10,
          sanitize: (value: string[]) => value.map(tag => tag.trim().toLowerCase())
        },
        inStock: {
          type: 'boolean',
          required: true
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set valid request body
      (mockReq as any).body = {
        name: 'Smartphone',
        description: 'A high-end smartphone with great features',
        price: 999.99,
        category: 'electronics',
        tags: ['phone', 'mobile', 'tech'],
        inStock: true
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).sanitizedBody.tags).toEqual(['phone', 'mobile', 'tech']);
    });

    it('should validate and sanitize a blog post form with HTML content', () => {
      const schema: ValidationSchema = {
        title: {
          type: 'string',
          required: true,
          min: 5,
          max: 200,
          sanitize: (value: string) => value.trim()
        },
        content: {
          type: 'string',
          required: true,
          min: 50,
          sanitize: (value: string) => {
            // Allow some HTML tags but remove potentially dangerous ones
            return value
              .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
              .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
              .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
              .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
              .replace(/javascript:/gi, 'removed:')
              .replace(/on\w+=/gi, 'data-removed=');
          }
        },
        category: {
          type: 'string',
          required: true,
          enum: ['technology', 'health', 'finance', 'lifestyle', 'other']
        },
        tags: {
          type: 'array',
          max: 10,
          sanitize: (value: string[]) => value.map(tag => tag.trim().toLowerCase())
        },
        published: {
          type: 'boolean',
          required: true
        }
      };

      const validationMiddleware = createValidationMiddleware(schema);

      // Set request body with potentially dangerous HTML
      (mockReq as any).body = {
        title: 'How to Secure Your Website',
        content: `
          <h1>Security Best Practices</h1>
          <p>Here are some tips:</p>
          <ul>
            <li>Use HTTPS</li>
            <li>Validate inputs</li>
            <li>Sanitize outputs</li>
          </ul>
          <script>alert('XSS attack');</script>
          <img src="image.jpg" onerror="alert('Another XSS');">
          <iframe src="evil.com"></iframe>
        `,
        category: 'technology',
        tags: ['Security', 'Web Development', 'HTTPS'],
        published: true
      };

      validationMiddleware(mockReq, mockRes, mockNext);

      expect(mockNext).toHaveBeenCalledWith();

      // Check that dangerous HTML was removed
      const sanitizedContent = (mockReq as any).sanitizedBody.content;
      expect(sanitizedContent).not.toContain('<script>');
      expect(sanitizedContent).not.toContain('<iframe');
      expect(sanitizedContent).not.toContain('onerror=');
      expect(sanitizedContent).toContain('<h1>Security Best Practices</h1>');

      // Check that tags were sanitized
      expect((mockReq as any).sanitizedBody.tags).toEqual(['security', 'web development', 'https']);
    });
  });
});
