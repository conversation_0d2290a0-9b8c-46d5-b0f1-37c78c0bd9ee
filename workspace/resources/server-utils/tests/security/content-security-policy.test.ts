import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';

// Define CSP directive types
type CSPDirective = {
  [key: string]: string | string[];
};

// Define CSP options type
type CSPOptions = {
  directives?: CSPDirective;
  reportOnly?: boolean;
  reportUri?: string;
  useDefaults?: boolean;
};

// Create a Content Security Policy middleware
function createCSPMiddleware(options: CSPOptions = {}) {
  const {
    directives = {},
    reportOnly = false,
    reportUri,
    useDefaults = true
  } = options;

  // Default directives
  const defaultDirectives: CSPDirective = {
    'default-src': ["'self'"],
    'base-uri': ["'self'"],
    'font-src': ["'self'", 'https:', 'data:'],
    'form-action': ["'self'"],
    'frame-ancestors': ["'self'"],
    'img-src': ["'self'", 'data:'],
    'object-src': ["'none'"],
    'script-src': ["'self'"],
    'script-src-attr': ["'none'"],
    'style-src': ["'self'", 'https:', "'unsafe-inline'"],
    'upgrade-insecure-requests': []
  };

  // Merge default and custom directives
  const finalDirectives: CSPDirective = useDefaults
    ? { ...defaultDirectives, ...directives }
    : { ...directives };

  // Add report-uri directive if provided
  if (reportUri) {
    finalDirectives['report-uri'] = [reportUri];
  }

  // Build CSP header value
  const headerValue = Object.entries(finalDirectives)
    .map(([key, value]) => {
      if (Array.isArray(value) && value.length === 0) {
        return key;
      }
      const values = Array.isArray(value) ? value : [value];
      return `${key} ${values.join(' ')}`;
    })
    .join('; ');

  // Determine header name based on reportOnly flag
  const headerName = reportOnly
    ? 'Content-Security-Policy-Report-Only'
    : 'Content-Security-Policy';

  // Return middleware function
  return (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
    res.setHeader(headerName, headerValue);
    next();
  };
}

describe('Content Security Policy Middleware', () => {
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockNext: (err?: any) => void;
  
  beforeEach(() => {
    // Create mock request
    mockReq = {} as IncomingMessage;
    
    // Create mock response
    mockRes = {
      setHeader: vi.fn(),
      getHeader: vi.fn(),
      removeHeader: vi.fn()
    } as unknown as ServerResponse;
    
    // Create mock next function
    mockNext = vi.fn();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  describe('Basic CSP Configuration', () => {
    it('should set default CSP header', () => {
      const cspMiddleware = createCSPMiddleware();
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Security-Policy',
        expect.stringContaining("default-src 'self'")
      );
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should set report-only CSP header when reportOnly is true', () => {
      const cspMiddleware = createCSPMiddleware({
        reportOnly: true
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Security-Policy-Report-Only',
        expect.any(String)
      );
    });
    
    it('should include report-uri directive when reportUri is provided', () => {
      const cspMiddleware = createCSPMiddleware({
        reportUri: 'https://example.com/report-csp-violations'
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith(
        'Content-Security-Policy',
        expect.stringContaining('report-uri https://example.com/report-csp-violations')
      );
    });
    
    it('should not include default directives when useDefaults is false', () => {
      const cspMiddleware = createCSPMiddleware({
        useDefaults: false,
        directives: {
          'script-src': ["'self'", 'https://trusted-cdn.com']
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toBe("script-src 'self' https://trusted-cdn.com");
      expect(headerValue).not.toContain("default-src");
    });
  });
  
  describe('Custom Directives', () => {
    it('should merge custom directives with default directives', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'script-src': ["'self'", 'https://trusted-cdn.com'],
          'connect-src': ["'self'", 'https://api.example.com']
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("script-src 'self' https://trusted-cdn.com");
      expect(headerValue).toContain("connect-src 'self' https://api.example.com");
      expect(headerValue).toContain("default-src 'self'");
    });
    
    it('should override default directives with custom directives', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'default-src': ["'self'", 'https://example.com'],
          'script-src': ["'self'", 'https://trusted-cdn.com', "'unsafe-inline'"]
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("default-src 'self' https://example.com");
      expect(headerValue).toContain("script-src 'self' https://trusted-cdn.com 'unsafe-inline'");
    });
    
    it('should handle empty directive arrays', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'upgrade-insecure-requests': []
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain('upgrade-insecure-requests');
      expect(headerValue).not.toContain('upgrade-insecure-requests ');
    });
    
    it('should handle string directive values', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'script-src': "'self' https://trusted-cdn.com"
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("script-src 'self' https://trusted-cdn.com");
    });
  });
  
  describe('Security Configurations', () => {
    it('should set a strict CSP for high security', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'default-src': ["'none'"],
          'script-src': ["'self'"],
          'connect-src': ["'self'"],
          'img-src': ["'self'"],
          'style-src': ["'self'"],
          'font-src': ["'self'"],
          'base-uri': ["'none'"],
          'form-action': ["'self'"],
          'frame-ancestors': ["'none'"],
          'object-src': ["'none'"],
          'require-trusted-types-for': ["'script'"]
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("default-src 'none'");
      expect(headerValue).toContain("script-src 'self'");
      expect(headerValue).toContain("frame-ancestors 'none'");
      expect(headerValue).toContain("require-trusted-types-for 'script'");
    });
    
    it('should set a CSP that allows inline scripts for development', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          'style-src': ["'self'", "'unsafe-inline'"]
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("script-src 'self' 'unsafe-inline' 'unsafe-eval'");
      expect(headerValue).toContain("style-src 'self' 'unsafe-inline'");
    });
    
    it('should set a CSP that allows specific nonces', () => {
      const nonce = 'random123';
      
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'script-src': ["'self'", `'nonce-${nonce}'`],
          'style-src': ["'self'", `'nonce-${nonce}'`]
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain(`script-src 'self' 'nonce-${nonce}'`);
      expect(headerValue).toContain(`style-src 'self' 'nonce-${nonce}'`);
    });
  });
  
  describe('Real-world Scenarios', () => {
    it('should set a CSP for a typical web application', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", 'https://cdn.jsdelivr.net', 'https://ajax.googleapis.com'],
          'style-src': ["'self'", 'https://fonts.googleapis.com', "'unsafe-inline'"],
          'font-src': ["'self'", 'https://fonts.gstatic.com'],
          'img-src': ["'self'", 'data:', 'https://img.example.com'],
          'connect-src': ["'self'", 'https://api.example.com']
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("default-src 'self'");
      expect(headerValue).toContain("script-src 'self' https://cdn.jsdelivr.net https://ajax.googleapis.com");
      expect(headerValue).toContain("style-src 'self' https://fonts.googleapis.com 'unsafe-inline'");
      expect(headerValue).toContain("font-src 'self' https://fonts.gstatic.com");
      expect(headerValue).toContain("img-src 'self' data: https://img.example.com");
      expect(headerValue).toContain("connect-src 'self' https://api.example.com");
    });
    
    it('should set a CSP for a single-page application', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'"],
          'style-src': ["'self'", "'unsafe-inline'"],
          'connect-src': ["'self'", 'https://api.example.com', 'wss://ws.example.com'],
          'worker-src': ["'self'", 'blob:'],
          'manifest-src': ["'self'"]
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("default-src 'self'");
      expect(headerValue).toContain("script-src 'self'");
      expect(headerValue).toContain("style-src 'self' 'unsafe-inline'");
      expect(headerValue).toContain("connect-src 'self' https://api.example.com wss://ws.example.com");
      expect(headerValue).toContain("worker-src 'self' blob:");
      expect(headerValue).toContain("manifest-src 'self'");
    });
    
    it('should set a CSP for a content site that embeds third-party content', () => {
      const cspMiddleware = createCSPMiddleware({
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", 'https://www.youtube.com', 'https://platform.twitter.com'],
          'frame-src': ["'self'", 'https://www.youtube.com', 'https://platform.twitter.com'],
          'img-src': ["'self'", 'https://img.youtube.com', 'https://pbs.twimg.com', 'data:'],
          'media-src': ["'self'", 'https://www.youtube.com']
        }
      });
      
      cspMiddleware(mockReq, mockRes, mockNext);
      
      const headerValue = (mockRes.setHeader as any).mock.calls[0][1];
      
      expect(headerValue).toContain("default-src 'self'");
      expect(headerValue).toContain("script-src 'self' https://www.youtube.com https://platform.twitter.com");
      expect(headerValue).toContain("frame-src 'self' https://www.youtube.com https://platform.twitter.com");
      expect(headerValue).toContain("img-src 'self' https://img.youtube.com https://pbs.twimg.com data:");
      expect(headerValue).toContain("media-src 'self' https://www.youtube.com");
    });
  });
});
