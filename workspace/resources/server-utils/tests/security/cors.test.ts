import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';

// Define CORS options type
type CorsOptions = {
  origin?: string | string[] | boolean | ((origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => void);
  methods?: string | string[];
  allowedHeaders?: string | string[];
  exposedHeaders?: string | string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
  optionsSuccessStatus?: number;
};

// Create a CORS middleware
function createCorsMiddleware(options: CorsOptions = {}) {
  const {
    origin = '*',
    methods = 'GET,HEAD,PUT,PATCH,POST,DELETE',
    allowedHeaders = '*',
    exposedHeaders = '',
    credentials = false,
    maxAge = 86400, // 24 hours
    preflightContinue = false,
    optionsSuccessStatus = 204
  } = options;

  // Convert methods and headers to arrays if they are strings
  const methodsArray = Array.isArray(methods) ? methods : methods.split(',');
  const allowedHeadersArray = Array.isArray(allowedHeaders) ? allowedHeaders : 
    allowedHeaders === '*' ? ['*'] : allowedHeaders.split(',');
  const exposedHeadersArray = Array.isArray(exposedHeaders) ? exposedHeaders : 
    exposedHeaders === '' ? [] : exposedHeaders.split(',');

  // Return middleware function
  return (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
    // Get request origin
    const requestOrigin = req.headers.origin;

    // Handle origin
    if (origin === '*') {
      res.setHeader('Access-Control-Allow-Origin', '*');
    } else if (typeof origin === 'string') {
      res.setHeader('Access-Control-Allow-Origin', origin);
      res.setHeader('Vary', 'Origin');
    } else if (Array.isArray(origin)) {
      if (requestOrigin && origin.includes(requestOrigin)) {
        res.setHeader('Access-Control-Allow-Origin', requestOrigin);
      }
      res.setHeader('Vary', 'Origin');
    } else if (typeof origin === 'function') {
      origin(requestOrigin, (err, allow) => {
        if (err || !allow) {
          return next(new Error('CORS origin not allowed'));
        }
        res.setHeader('Access-Control-Allow-Origin', requestOrigin || '*');
        res.setHeader('Vary', 'Origin');
        continueWithCors();
      });
      return;
    }

    continueWithCors();

    function continueWithCors() {
      // Handle credentials
      if (credentials) {
        res.setHeader('Access-Control-Allow-Credentials', 'true');
      }

      // Handle preflight requests
      if (req.method === 'OPTIONS') {
        // Handle methods
        res.setHeader('Access-Control-Allow-Methods', methodsArray.join(','));

        // Handle headers
        if (allowedHeaders === '*') {
          const requestHeaders = req.headers['access-control-request-headers'];
          if (requestHeaders) {
            res.setHeader('Access-Control-Allow-Headers', requestHeaders);
            res.setHeader('Vary', 'Access-Control-Request-Headers');
          } else {
            res.setHeader('Access-Control-Allow-Headers', '*');
          }
        } else {
          res.setHeader('Access-Control-Allow-Headers', allowedHeadersArray.join(','));
        }

        // Handle max age
        if (maxAge) {
          res.setHeader('Access-Control-Max-Age', maxAge.toString());
        }

        // Handle preflight response
        if (!preflightContinue) {
          res.statusCode = optionsSuccessStatus;
          res.setHeader('Content-Length', '0');
          res.end();
          return;
        }
      } else {
        // Handle exposed headers for non-preflight requests
        if (exposedHeadersArray.length > 0) {
          res.setHeader('Access-Control-Expose-Headers', exposedHeadersArray.join(','));
        }
      }

      next();
    }
  };
}

describe('CORS Middleware', () => {
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockNext: (err?: any) => void;
  
  beforeEach(() => {
    // Create mock request
    mockReq = {
      method: 'GET',
      headers: {
        origin: 'https://example.com'
      }
    } as unknown as IncomingMessage;
    
    // Create mock response
    mockRes = {
      setHeader: vi.fn(),
      statusCode: 200,
      end: vi.fn()
    } as unknown as ServerResponse;
    
    // Create mock next function
    mockNext = vi.fn();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  describe('Basic CORS Configuration', () => {
    it('should set default CORS headers with wildcard origin', () => {
      const corsMiddleware = createCorsMiddleware();
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', '*');
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should set specific origin when provided', () => {
      const corsMiddleware = createCorsMiddleware({
        origin: 'https://allowed-origin.com'
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://allowed-origin.com');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Vary', 'Origin');
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should handle array of allowed origins', () => {
      const corsMiddleware = createCorsMiddleware({
        origin: ['https://example.com', 'https://another-origin.com']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://example.com');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Vary', 'Origin');
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should not set origin header if origin is not in allowed list', () => {
      const corsMiddleware = createCorsMiddleware({
        origin: ['https://allowed-origin.com']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).not.toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://example.com');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Vary', 'Origin');
      expect(mockNext).toHaveBeenCalled();
    });
  });
  
  describe('Preflight Requests', () => {
    beforeEach(() => {
      mockReq.method = 'OPTIONS';
      mockReq.headers['access-control-request-method'] = 'POST';
    });
    
    it('should handle preflight requests with default options', () => {
      const corsMiddleware = createCorsMiddleware();
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', '*');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Headers', '*');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Max-Age', '86400');
      expect(mockRes.statusCode).toBe(204);
      expect(mockRes.end).toHaveBeenCalled();
      expect(mockNext).not.toHaveBeenCalled();
    });
    
    it('should respect custom methods', () => {
      const corsMiddleware = createCorsMiddleware({
        methods: ['GET', 'POST']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'GET,POST');
    });
    
    it('should respect custom headers', () => {
      const corsMiddleware = createCorsMiddleware({
        allowedHeaders: ['Content-Type', 'Authorization']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Headers', 'Content-Type,Authorization');
    });
    
    it('should respect custom max age', () => {
      const corsMiddleware = createCorsMiddleware({
        maxAge: 3600
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Max-Age', '3600');
    });
    
    it('should continue to next middleware if preflightContinue is true', () => {
      const corsMiddleware = createCorsMiddleware({
        preflightContinue: true
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.end).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should use custom options success status', () => {
      const corsMiddleware = createCorsMiddleware({
        optionsSuccessStatus: 200
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.statusCode).toBe(200);
    });
  });
  
  describe('Credentials and Exposed Headers', () => {
    it('should set credentials header when enabled', () => {
      const corsMiddleware = createCorsMiddleware({
        credentials: true
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Credentials', 'true');
    });
    
    it('should not set credentials header when disabled', () => {
      const corsMiddleware = createCorsMiddleware({
        credentials: false
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).not.toHaveBeenCalledWith('Access-Control-Allow-Credentials', 'true');
    });
    
    it('should set exposed headers for non-preflight requests', () => {
      const corsMiddleware = createCorsMiddleware({
        exposedHeaders: ['Content-Length', 'X-Custom-Header']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Expose-Headers', 'Content-Length,X-Custom-Header');
    });
    
    it('should not set exposed headers for preflight requests', () => {
      mockReq.method = 'OPTIONS';
      
      const corsMiddleware = createCorsMiddleware({
        exposedHeaders: ['Content-Length', 'X-Custom-Header']
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).not.toHaveBeenCalledWith('Access-Control-Expose-Headers', expect.any(String));
    });
  });
  
  describe('Dynamic Origin Function', () => {
    it('should call origin function with request origin', () => {
      const originFn = vi.fn((origin, callback) => {
        callback(null, true);
      });
      
      const corsMiddleware = createCorsMiddleware({
        origin: originFn
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(originFn).toHaveBeenCalledWith('https://example.com', expect.any(Function));
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://example.com');
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should handle origin function that rejects the origin', () => {
      const originFn = vi.fn((origin, callback) => {
        callback(new Error('Origin not allowed'));
      });
      
      const corsMiddleware = createCorsMiddleware({
        origin: originFn
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(originFn).toHaveBeenCalledWith('https://example.com', expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
    
    it('should handle origin function that allows the origin but returns false', () => {
      const originFn = vi.fn((origin, callback) => {
        callback(null, false);
      });
      
      const corsMiddleware = createCorsMiddleware({
        origin: originFn
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(originFn).toHaveBeenCalledWith('https://example.com', expect.any(Function));
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
  });
  
  describe('Real-world Scenarios', () => {
    it('should handle requests with no origin header', () => {
      delete mockReq.headers.origin;
      
      const corsMiddleware = createCorsMiddleware();
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', '*');
      expect(mockNext).toHaveBeenCalled();
    });
    
    it('should handle regex pattern for allowed origins', () => {
      // Create a function that checks origin against a regex pattern
      const originFn = (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
        const allowedPattern = /^https:\/\/(.*\.)?example\.com$/;
        const allowed = origin ? allowedPattern.test(origin) : false;
        callback(null, allowed);
      };
      
      const corsMiddleware = createCorsMiddleware({
        origin: originFn
      });
      
      // Test with allowed origin
      mockReq.headers.origin = 'https://subdomain.example.com';
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Origin', 'https://subdomain.example.com');
      expect(mockNext).toHaveBeenCalled();
      
      // Reset mocks
      vi.clearAllMocks();
      
      // Test with disallowed origin
      mockReq.headers.origin = 'https://evil-site.com';
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    });
    
    it('should handle requests with custom request headers', () => {
      mockReq.method = 'OPTIONS';
      mockReq.headers['access-control-request-headers'] = 'content-type,authorization,x-custom-header';
      
      const corsMiddleware = createCorsMiddleware();
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockRes.setHeader).toHaveBeenCalledWith('Access-Control-Allow-Headers', 'content-type,authorization,x-custom-header');
      expect(mockRes.setHeader).toHaveBeenCalledWith('Vary', 'Access-Control-Request-Headers');
    });
    
    it('should handle multiple Vary headers correctly', () => {
      mockReq.method = 'OPTIONS';
      mockReq.headers['access-control-request-headers'] = 'content-type,authorization';
      
      const corsMiddleware = createCorsMiddleware({
        origin: 'https://example.com'
      });
      
      corsMiddleware(mockReq, mockRes, mockNext);
      
      // Both Origin and Access-Control-Request-Headers should be in Vary
      const varyHeaderCalls = (mockRes.setHeader as any).mock.calls.filter(
        (call: any[]) => call[0] === 'Vary'
      );
      
      expect(varyHeaderCalls.length).toBe(2);
      expect(varyHeaderCalls).toContainEqual(['Vary', 'Origin']);
      expect(varyHeaderCalls).toContainEqual(['Vary', 'Access-Control-Request-Headers']);
    });
  });
});
