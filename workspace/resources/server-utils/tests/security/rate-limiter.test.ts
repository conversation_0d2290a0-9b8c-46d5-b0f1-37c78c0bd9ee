import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IncomingMessage, ServerResponse } from 'http';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

// Define a simple in-memory rate limiter
type RateLimiterOptions = {
  windowMs: number;
  maxRequests: number;
  message?: string;
  statusCode?: number;
  keyGenerator?: (req: IncomingMessage) => string;
  skip?: (req: IncomingMessage) => boolean;
  headers?: boolean;
};

type RateLimiterStore = {
  [key: string]: {
    count: number;
    resetTime: number;
  };
};

// Create a rate limiter middleware
function createRateLimiter(options: RateLimiterOptions) {
  const {
    windowMs = 60 * 1000, // 1 minute by default
    maxRequests = 100,
    message = 'Too many requests, please try again later.',
    statusCode = 429,
    keyGenerator = (req) => req.socket.remoteAddress || 'unknown',
    skip = () => false,
    headers = true
  } = options;

  // In-memory store for rate limiting
  const store: RateLimiterStore = {};

  // Clean up expired entries periodically
  const cleanup = setInterval(() => {
    const now = Date.now();
    Object.keys(store).forEach(key => {
      if (store[key].resetTime <= now) {
        delete store[key];
      }
    });
  }, windowMs);

  // Return middleware function
  return (req: IncomingMessage, res: ServerResponse, next: (err?: any) => void) => {
    if (skip(req)) {
      return next();
    }

    const key = keyGenerator(req);
    const now = Date.now();

    // Initialize or reset if window has passed
    if (!store[key] || store[key].resetTime <= now) {
      store[key] = {
        count: 0,
        resetTime: now + windowMs
      };
    }

    // Increment request count
    store[key].count += 1;

    // Calculate remaining requests and reset time
    const remaining = Math.max(0, maxRequests - store[key].count);
    const resetTime = store[key].resetTime;

    // Set rate limit headers if enabled
    if (headers) {
      res.setHeader('X-RateLimit-Limit', maxRequests.toString());
      res.setHeader('X-RateLimit-Remaining', remaining.toString());
      res.setHeader('X-RateLimit-Reset', Math.ceil(resetTime / 1000).toString());
    }

    // If rate limit is exceeded, return error
    if (store[key].count > maxRequests) {
      if (headers) {
        res.setHeader('Retry-After', Math.ceil((resetTime - now) / 1000).toString());
      }

      const rateLimitError = HTTP_ERRORS_WITH_CONTEXT.TOO_MANY_REQUESTS(message);
      return next(rateLimitError);
    }

    next();
  };
}

describe('Rate Limiter Middleware', () => {
  let mockReq: IncomingMessage;
  let mockRes: ServerResponse;
  let mockNext: (err?: any) => void;
  let rateLimiter: ReturnType<typeof createRateLimiter>;
  let store: RateLimiterStore;

  beforeEach(() => {
    // Create mock request
    mockReq = {
      socket: {
        remoteAddress: '127.0.0.1'
      },
      method: 'GET',
      url: '/api/test',
      headers: {}
    } as unknown as IncomingMessage;

    // Create mock response
    mockRes = {
      setHeader: vi.fn(),
      statusCode: 200
    } as unknown as ServerResponse;

    // Create mock next function
    mockNext = vi.fn();

    // Create rate limiter with test settings
    rateLimiter = createRateLimiter({
      windowMs: 1000, // 1 second window for testing
      maxRequests: 3, // Allow 3 requests per window
      headers: true
    });

    // Access the store directly for testing
    store = (rateLimiter as any).store;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rate Limiting', () => {
    it('should allow requests within the rate limit', () => {
      // Make 3 requests (within limit)
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);

      // All requests should pass
      expect(mockNext).toHaveBeenCalledTimes(3);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should block requests that exceed the rate limit', () => {
      // Make 4 requests (exceeding limit of 3)
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);

      // First 3 should pass, 4th should be blocked
      expect(mockNext).toHaveBeenCalledTimes(4);
      expect(mockNext.mock.calls[0][0]).toBeUndefined();
      expect(mockNext.mock.calls[1][0]).toBeUndefined();
      expect(mockNext.mock.calls[2][0]).toBeUndefined();

      // Check that the 4th call has an error
      const error = mockNext.mock.calls[3][0];
      expect(error).toBeDefined();
      expect(error.statusCode).toBe(429);
      expect(error.message).toBe('too many requests, please try again later');
    });

    it('should reset the rate limit after the window expires', async () => {
      // Make 3 requests (within limit)
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);

      // All requests should pass
      expect(mockNext).toHaveBeenCalledTimes(3);
      expect(mockNext).toHaveBeenCalledWith();

      // Wait for the window to expire
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Make another request
      rateLimiter(mockReq, mockRes, mockNext);

      // Should pass because the window has reset
      expect(mockNext).toHaveBeenCalledTimes(4);
      expect(mockNext.mock.calls[3][0]).toBeUndefined();
    });
  });

  describe('Rate Limit Headers', () => {
    it('should set rate limit headers on responses', () => {
      rateLimiter(mockReq, mockRes, mockNext);

      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Limit', '3');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', '2');
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Reset', expect.any(String));
    });

    it('should update remaining requests in headers', () => {
      rateLimiter(mockReq, mockRes, mockNext);
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', '2');

      rateLimiter(mockReq, mockRes, mockNext);
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', '1');

      rateLimiter(mockReq, mockRes, mockNext);
      expect(mockRes.setHeader).toHaveBeenCalledWith('X-RateLimit-Remaining', '0');
    });

    it('should set Retry-After header when rate limit is exceeded', () => {
      // Make 4 requests (exceeding limit of 3)
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);
      rateLimiter(mockReq, mockRes, mockNext);

      // Check that Retry-After header is set on the 4th request
      expect(mockRes.setHeader).toHaveBeenCalledWith('Retry-After', expect.any(String));
    });

    it('should not set headers when headers option is disabled', () => {
      // Create rate limiter with headers disabled
      const noHeadersRateLimiter = createRateLimiter({
        windowMs: 1000,
        maxRequests: 3,
        headers: false
      });

      // Make a request
      noHeadersRateLimiter(mockReq, mockRes, mockNext);

      // Headers should not be set
      expect(mockRes.setHeader).not.toHaveBeenCalled();
    });
  });

  describe('Custom Options', () => {
    it('should use custom key generator', () => {
      // Create rate limiter with custom key generator
      const customKeyRateLimiter = createRateLimiter({
        windowMs: 1000,
        maxRequests: 3,
        keyGenerator: (req) => req.headers['x-forwarded-for'] as string || 'default'
      });

      // Set custom header
      mockReq.headers['x-forwarded-for'] = '192.168.1.1';

      // Make a request
      customKeyRateLimiter(mockReq, mockRes, mockNext);

      // Check that the custom key was used
      expect(mockNext).toHaveBeenCalledWith();

      // Change the key
      mockReq.headers['x-forwarded-for'] = '***********';

      // Make more requests than the limit
      customKeyRateLimiter(mockReq, mockRes, mockNext);
      customKeyRateLimiter(mockReq, mockRes, mockNext);
      customKeyRateLimiter(mockReq, mockRes, mockNext);
      customKeyRateLimiter(mockReq, mockRes, mockNext);

      // The 4th request with the new key should be blocked
      const error = mockNext.mock.calls[4][0];
      expect(error).toBeDefined();
      expect(error.statusCode).toBe(429);
    });

    it('should skip rate limiting for certain requests', () => {
      // Create rate limiter that skips OPTIONS requests
      const skipOptionsRateLimiter = createRateLimiter({
        windowMs: 1000,
        maxRequests: 3,
        skip: (req) => req.method === 'OPTIONS'
      });

      // Make OPTIONS request
      mockReq.method = 'OPTIONS';

      // Make more requests than the limit
      skipOptionsRateLimiter(mockReq, mockRes, mockNext);
      skipOptionsRateLimiter(mockReq, mockRes, mockNext);
      skipOptionsRateLimiter(mockReq, mockRes, mockNext);
      skipOptionsRateLimiter(mockReq, mockRes, mockNext);

      // All requests should pass because they're skipped
      expect(mockNext).toHaveBeenCalledTimes(4);
      expect(mockNext).toHaveBeenCalledWith();
    });

    it('should use custom message and status code', () => {
      // Create rate limiter with custom message and status code
      const customMessageRateLimiter = createRateLimiter({
        windowMs: 1000,
        maxRequests: 1,
        message: 'Custom rate limit message',
        statusCode: 403
      });

      // Make 2 requests (exceeding limit of 1)
      customMessageRateLimiter(mockReq, mockRes, mockNext);
      customMessageRateLimiter(mockReq, mockRes, mockNext);

      // Check that the custom message and status code are used
      const error = mockNext.mock.calls[1][0];
      expect(error).toBeDefined();
      expect(error.statusCode).toBe(429);
      expect(error.context).toBe('Custom rate limit message');
    });
  });

  describe('Different Rate Limits for Different Routes', () => {
    it('should apply different rate limits based on the request URL', () => {
      // Create rate limiter factory function
      const createRouteSpecificRateLimiter = (route: string, limit: number) => {
        return createRateLimiter({
          windowMs: 1000,
          maxRequests: limit,
          skip: (req) => !req.url?.startsWith(route)
        });
      };

      // Create rate limiters for different routes
      const apiRateLimiter = createRouteSpecificRateLimiter('/api', 2);
      const adminRateLimiter = createRouteSpecificRateLimiter('/admin', 1);

      // Test API rate limiter
      mockReq.url = '/api/users';
      apiRateLimiter(mockReq, mockRes, mockNext);
      apiRateLimiter(mockReq, mockRes, mockNext);
      apiRateLimiter(mockReq, mockRes, mockNext);

      // First 2 should pass, 3rd should be blocked
      expect(mockNext).toHaveBeenCalledTimes(3);
      expect(mockNext.mock.calls[0][0]).toBeUndefined();
      expect(mockNext.mock.calls[1][0]).toBeUndefined();
      expect(mockNext.mock.calls[2][0]).toBeDefined();

      // Reset mocks
      vi.clearAllMocks();

      // Test admin rate limiter
      mockReq.url = '/admin/dashboard';
      adminRateLimiter(mockReq, mockRes, mockNext);
      adminRateLimiter(mockReq, mockRes, mockNext);

      // First should pass, 2nd should be blocked
      expect(mockNext).toHaveBeenCalledTimes(2);
      expect(mockNext.mock.calls[0][0]).toBeUndefined();
      expect(mockNext.mock.calls[1][0]).toBeDefined();
    });
  });

  describe('Rate Limiting by User ID', () => {
    it('should rate limit based on user ID', () => {
      // Create rate limiter that uses user ID from request
      const userRateLimiter = createRateLimiter({
        windowMs: 1000,
        maxRequests: 2,
        keyGenerator: (req) => (req as any).user?.id || 'anonymous'
      });

      // Set user ID
      (mockReq as any).user = { id: 'user-123' };

      // Make requests for user-123
      userRateLimiter(mockReq, mockRes, mockNext);
      userRateLimiter(mockReq, mockRes, mockNext);
      userRateLimiter(mockReq, mockRes, mockNext);

      // First 2 should pass, 3rd should be blocked
      expect(mockNext).toHaveBeenCalledTimes(3);
      expect(mockNext.mock.calls[0][0]).toBeUndefined();
      expect(mockNext.mock.calls[1][0]).toBeUndefined();
      expect(mockNext.mock.calls[2][0]).toBeDefined();

      // Reset mocks
      vi.clearAllMocks();

      // Change user ID
      (mockReq as any).user = { id: 'user-456' };

      // Make requests for user-456
      userRateLimiter(mockReq, mockRes, mockNext);
      userRateLimiter(mockReq, mockRes, mockNext);

      // Both should pass because it's a different user
      expect(mockNext).toHaveBeenCalledTimes(2);
      expect(mockNext.mock.calls[0][0]).toBeUndefined();
      expect(mockNext.mock.calls[1][0]).toBeUndefined();
    });
  });
});
