import { describe, it, expect, vi, beforeEach } from "vitest";
import { getS3Readable } from "../../src/s3/s3-readable";
import { Readable } from "node:stream";

// Mock AbortController
vi.mock("abort-controller", ()=>{
  return {
    AbortController: function(){
      return {
        signal: "mock-signal",
        abort: vi.fn()
      };
    }
  };
});

describe("getS3Readable", ()=>{
  let mockS3;
  let mockReadable;
  beforeEach(()=>{
    // Reset mocks
    vi.resetAllMocks();

    // Create a mock readable stream
    mockReadable = new Readable({
      read(){
        this.push("test data");
        this.push(null);
      }
    });

    // Create a mock S3 client
    mockS3 = {
      getObject: vi.fn().mockResolvedValue({
        Body: mockReadable
      })
    };
  });

  it("should get a readable stream from S3", async ()=>{
    const result = await getS3Readable(mockS3, { bucket: "test-bucket", objectKey: "test-key" });

    // Check that S3 getObject was called with the correct parameters
    expect(mockS3.getObject).toHaveBeenCalledWith(
      {
        Bucket: "test-bucket",
        Key: "test-key"
      },
      { abortSignal: "mock-signal" }
    );

    // Check that the result contains the stream and a cancel function
    expect(result).toHaveProperty("stream");
    expect(result).toHaveProperty("cancel");
    expect(result.stream).toBe(mockReadable);
    expect(typeof result.cancel).toBe("function");
  });

  it("should throw an error if the response has no body", async ()=>{
    // Mock S3 getObject to return a response with no Body
    mockS3.getObject.mockResolvedValue({});

    // Expect getS3Readable to throw an error
    await expect(getS3Readable(mockS3, { bucket: "test-bucket", objectKey: "test-key" }))
      .rejects.toThrow("❌ No body in get checks response.");
  });

  it("should provide a cancel function that aborts the request", async ()=>{
    const result = await getS3Readable(mockS3, { bucket: "test-bucket", objectKey: "test-key" });

    // Call the cancel function
    result.cancel();

    // We can't directly check if abort was called because of how the mock is structured
    // But we can verify that the cancel function exists and is callable
    expect(typeof result.cancel).toBe("function");
  });
});
