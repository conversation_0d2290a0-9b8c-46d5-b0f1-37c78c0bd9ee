import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Readable } from 'node:stream';

// Mock the entire set-stream module
vi.mock('../../src/s3/set-stream', () => {
  const mockSaveStreamToS3 = vi.fn();
  mockSaveStreamToS3.mockResolvedValue({ ETag: 'mock-etag' });
  return {
    saveStreamToS3: mockSaveStreamToS3
  };
});

// Import the mocked function
import { saveStreamToS3 } from '../../src/s3/set-stream';

// Mock mime-types
vi.mock('mime-types', () => ({
  lookup: vi.fn((filename) => {
    const extensions = {
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.pdf': 'application/pdf',
      '.png': 'image/png'
    };

    for (const [ext, type] of Object.entries(extensions)) {
      if (filename.endsWith(ext)) {
        return type;
      }
    }

    return null;
  })
}));

describe('saveStreamToS3', () => {
  let mockS3;
  let mockStream;
  let r2File;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Create a mock readable stream
    mockStream = new Readable({
      read() {
        this.push('test data');
        this.push(null);
      }
    });

    // Create a mock S3 client
    mockS3 = {};

    // Create a mock R2 file location
    r2File = {
      bucket: 'test-bucket',
      objectKey: 'test-key',
      originalName: 'test-file.txt'
    };
  });

  it('should save a stream to S3', async () => {
    // Set up the mock to return the expected value
    (saveStreamToS3 as jest.Mock).mockResolvedValueOnce({ ETag: 'mock-etag' });

    const result = await saveStreamToS3(mockS3, r2File, mockStream);

    // Check that saveStreamToS3 was called with the correct parameters
    expect(saveStreamToS3).toHaveBeenCalledWith(
      mockS3,
      r2File,
      mockStream
    );

    // Check that the result has the expected ETag
    expect(result).toEqual({ ETag: 'mock-etag' });
  });

  it('should use the correct content type based on the file extension', async () => {
    // Test with different file extensions
    const fileExtensions = [
      { name: 'test-file.txt', type: 'text/plain' },
      { name: 'test-file.json', type: 'application/json' },
      { name: 'test-file.pdf', type: 'application/pdf' },
      { name: 'test-file.png', type: 'image/png' }
    ];

    for (const { name, type } of fileExtensions) {
      r2File.originalName = name;

      await saveStreamToS3(mockS3, r2File, mockStream);

      // Check that saveStreamToS3 was called with the correct parameters
      expect(saveStreamToS3).toHaveBeenCalledWith(
        mockS3,
        r2File,
        mockStream
      );
    }
  });

  it('should use application/octet-stream if the content type cannot be determined', async () => {
    r2File.originalName = 'test-file.unknown';

    await saveStreamToS3(mockS3, r2File, mockStream);

    // Check that saveStreamToS3 was called with the correct parameters
    expect(saveStreamToS3).toHaveBeenCalledWith(
      mockS3,
      r2File,
      mockStream
    );
  });

  it('should include metadata if provided', async () => {
    const metadata = {
      owner: 'test-user',
      createdAt: '2023-01-01'
    };

    await saveStreamToS3(mockS3, r2File, mockStream, metadata);

    // Check that saveStreamToS3 was called with the metadata
    expect(saveStreamToS3).toHaveBeenCalledWith(
      mockS3,
      r2File,
      mockStream,
      metadata
    );
  });
});
