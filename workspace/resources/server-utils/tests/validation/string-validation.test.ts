import { describe, it, expect, vi } from 'vitest';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

// Import string validation utilities from @divinci-ai/utils
// Since we can't directly import from @divinci-ai/utils in the test,
// we'll mock the utilities and test the integration with HTTP_ERRORS_WITH_CONTEXT

// Mock the EMAIL_REGEXP from @divinci-ai/utils
const EMAIL_REGEXP = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

// Mock the password validation regexes
const lowerCaseTest = /[a-z]/;
const upperCaseTest = /[A-Z]/;
const numberTest = /[0-9]/;
const symbolTest = /[ ~`!@#$%^&*()_\-+={}\[\]|\:;"'<,>.\?\/]/;
const minLength = 8;

// Create validation functions that use HTTP_ERRORS_WITH_CONTEXT
function validateEmail(email: string): boolean {
  if (!EMAIL_REGEXP.test(email)) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(`Invalid email format: ${email}`);
  }
  return true;
}

function validateUsername(username: string): boolean {
  if (username.length < 5) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Username must be at least 5 characters long');
  }
  return true;
}

function validatePassword(password: string): boolean {
  if (password.length < minLength) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(`Password must be at least ${minLength} characters long`);
  }
  if (!lowerCaseTest.test(password)) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Password must contain at least one lowercase letter');
  }
  if (!upperCaseTest.test(password)) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Password must contain at least one uppercase letter');
  }
  if (!numberTest.test(password)) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Password must contain at least one number');
  }
  if (!symbolTest.test(password)) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Password must contain at least one symbol');
  }
  return true;
}

function validateURL(url: string): boolean {
  try {
    // Add protocol if missing
    if (!url.match(/^[a-zA-Z]+:\/\//)) {
      url = 'http://' + url;
    }
    new URL(url);

    // Additional validation for malformed URLs that the URL constructor accepts
    if (url.includes(' ') || url.includes('<') || url.includes('>')) {
      throw new Error('URL contains invalid characters');
    }

    return true;
  } catch (e) {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(`Invalid URL: ${url}`);
  }
}

describe('String Validation with HTTP_ERRORS_WITH_CONTEXT', () => {
  describe('Email Validation', () => {
    it('should validate correct email formats', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should throw BAD_FORM error for invalid email formats', () => {
      expect(() => validateEmail('test')).toThrow();
      expect(() => validateEmail('test@')).toThrow();
      expect(() => validateEmail('test@example')).toThrow();
      expect(() => validateEmail('@example.com')).toThrow();
      expect(() => validateEmail('test@.com')).toThrow();
    });

    it('should include the invalid email in the error context', () => {
      try {
        validateEmail('invalid-email');
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('invalid-email');
      }
    });
  });

  describe('Username Validation', () => {
    it('should validate usernames with 5 or more characters', () => {
      expect(validateUsername('user1')).toBe(true);
      expect(validateUsername('username')).toBe(true);
      expect(validateUsername('user-name')).toBe(true);
    });

    it('should throw BAD_FORM error for usernames with less than 5 characters', () => {
      expect(() => validateUsername('user')).toThrow();
      expect(() => validateUsername('usr')).toThrow();
      expect(() => validateUsername('u')).toThrow();
    });

    it('should include the minimum length requirement in the error context', () => {
      try {
        validateUsername('usr');
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('5 characters');
      }
    });
  });

  describe('Password Validation', () => {
    it('should validate passwords that meet all requirements', () => {
      expect(validatePassword('Password1!')).toBe(true);
      expect(validatePassword('Complex123$')).toBe(true);
      expect(validatePassword('Very-Strong-Password-123')).toBe(true);
    });

    it('should throw BAD_FORM error for passwords that are too short', () => {
      expect(() => validatePassword('Pass1!')).toThrow();
    });

    it('should throw BAD_FORM error for passwords without lowercase letters', () => {
      expect(() => validatePassword('PASSWORD123!')).toThrow();
    });

    it('should throw BAD_FORM error for passwords without uppercase letters', () => {
      expect(() => validatePassword('password123!')).toThrow();
    });

    it('should throw BAD_FORM error for passwords without numbers', () => {
      expect(() => validatePassword('Password!')).toThrow();
    });

    it('should throw BAD_FORM error for passwords without symbols', () => {
      expect(() => validatePassword('Password123')).toThrow();
    });

    it('should include specific requirement details in the error context', () => {
      try {
        validatePassword('password');
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('uppercase');
      }
    });
  });

  describe('URL Validation', () => {
    it('should validate correct URLs', () => {
      expect(validateURL('https://example.com')).toBe(true);
      expect(validateURL('http://example.com/path')).toBe(true);
      expect(validateURL('example.com')).toBe(true); // Will be prefixed with http://
    });

    it('should throw BAD_FORM error for invalid URLs', () => {
      // These URLs are invalid
      expect(() => validateURL('http://no spaces allowed.com')).toThrow();
      expect(() => validateURL('http://invalid-chars-<>-in-url')).toThrow();
    });

    it('should include the invalid URL in the error context', () => {
      try {
        validateURL('invalid-url');
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('invalid-url');
      }
    });
  });
});
