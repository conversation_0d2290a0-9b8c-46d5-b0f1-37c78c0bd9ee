import { describe, it, expect, vi } from 'vitest';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

// Mock the schema validation utilities from @divinci-ai/utils
// Since we can't directly import from @divinci-ai/utils in the test,
// we'll create mock implementations based on the code we've seen

// Mock isArrayRegExp and isOptionalRegExp
const isArrayRegExp = /^([a-z]+)\[\]$/;
const isOptionalRegExp = /^([a-z]+)\?$/;

// Mock castToBaseValidType
function castToBaseValidType(str: string): 'boolean' | 'number' | 'string' {
  switch(str) {
    case 'boolean': return str;
    case 'number': return str;
    case 'string': return str;
    default: {
      throw new Error(`Invalid string: ${str}`);
    }
  }
}

// Mock castToObject
function castToObject(un: any, e?: any): Record<string, any> {
  if (typeof un !== 'object') {
    throw (e || new Error('Json is not an object'));
  }
  if (un === null) {
    throw (e || new Error('Json is null'));
  }
  if (Array.isArray(un)) {
    throw (e || new Error('Json is an array'));
  }
  return un;
}

// Mock castBase
function castBase(value: any, key: string, schemaType: 'boolean' | 'number' | 'string', e?: any) {
  const valueType = typeof value;
  if (valueType === 'undefined') {
    throw (
      typeof e === 'undefined' ? new Error(`Json at "${key}" does not exist`) :
      typeof e === 'function' ? e(`Json at "${key}" does not exist`) :
      e
    );
  }
  if (valueType !== schemaType) {
    throw (
      typeof e === 'undefined' ? new Error(`Json at "${key}" is not of type "${schemaType}"`) :
      typeof e === 'function' ? e(`Json at "${key}" is not of type "${schemaType}"`) :
      e
    );
  }
  return value;
}

// Mock castArray
function castArray(value: any, key: string, schemaType: 'boolean' | 'number' | 'string', e?: any) {
  const valueType = typeof value;
  if (valueType !== 'object') {
    throw (
      typeof e === 'undefined' ? new Error(`Json at "${key}" is not an array`) :
      typeof e === 'function' ? e(`Json at "${key}" is not an array`) :
      e
    );
  }
  if (!Array.isArray(value)) {
    throw (
      typeof e === 'undefined' ? new Error(`Json at "${key}" is not an array`) :
      typeof e === 'function' ? e(`Json at "${key}" is not an array`) :
      e
    );
  }
  return value.map((arrayValue) => {
    if (typeof arrayValue !== schemaType) {
      throw (
        typeof e === 'undefined' ? new Error(`Array item in "${key}" is not of type "${schemaType}"`) :
        typeof e === 'function' ? e(`Array item in "${key}" is not of type "${schemaType}"`) :
        e
      );
    }
    return arrayValue;
  });
}

// Mock castShallowObject
function castShallowObject(unknown: any, schema: Record<string, string>, e?: any): any {
  const value = castToObject(unknown, e);

  return Object.keys(schema).reduce((obj: Record<string, any>, key) => {
    const rawType = schema[key];
    const array = isArrayRegExp.exec(rawType);
    const optional = isOptionalRegExp.exec(rawType);

    if (array) {
      const [, schemaType] = array;
      obj[key] = castArray(value[key], key, castToBaseValidType(schemaType), e);
    } else if (optional) {
      const [, schemaType] = optional;
      if (typeof value[key] !== 'undefined') {
        obj[key] = castBase(value[key], key, castToBaseValidType(schemaType), e);
      }
    } else {
      obj[key] = castBase(value[key], key, castToBaseValidType(rawType), e);
    }

    return obj;
  }, {});
}

// Create a validation function that uses HTTP_ERRORS_WITH_CONTEXT
function validateWithSchema(data: any, schema: Record<string, string>): any {
  try {
    return castShallowObject(data, schema, (message: string) => {
      return HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(message);
    });
  } catch (error) {
    if (error instanceof Error) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(error.message);
    }
    throw error;
  }
}

describe('Schema Validation with HTTP_ERRORS_WITH_CONTEXT', () => {
  describe('Basic Schema Validation', () => {
    it('should validate objects against a schema', () => {
      const schema = {
        name: 'string',
        age: 'number',
        isActive: 'boolean'
      };

      const data = {
        name: 'John Doe',
        age: 30,
        isActive: true
      };

      const result = validateWithSchema(data, schema);

      expect(result).toEqual(data);
    });

    it('should throw BAD_FORM error if data is not an object', () => {
      const schema = {
        name: 'string'
      };

      expect(() => validateWithSchema('not an object', schema)).toThrow(expect.anything());
      expect(() => validateWithSchema(null, schema)).toThrow(expect.anything());
      expect(() => validateWithSchema([1, 2, 3], schema)).toThrow(expect.anything());
    });

    it('should throw BAD_FORM error if a required field is missing', () => {
      const schema = {
        name: 'string',
        age: 'number'
      };

      const data = {
        name: 'John Doe'
        // missing age
      };

      expect(() => validateWithSchema(data, schema)).toThrow();
    });

    it('should throw BAD_FORM error if a field has the wrong type', () => {
      const schema = {
        name: 'string',
        age: 'number'
      };

      const data = {
        name: 'John Doe',
        age: '30' // string instead of number
      };

      expect(() => validateWithSchema(data, schema)).toThrow();
    });

    it('should include the field name and expected type in the error context', () => {
      const schema = {
        name: 'string',
        age: 'number'
      };

      const data = {
        name: 'John Doe',
        age: '30' // string instead of number
      };

      try {
        validateWithSchema(data, schema);
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('age');
        expect(error.context).toContain('number');
      }
    });
  });

  describe('Optional Fields', () => {
    it('should allow optional fields to be omitted', () => {
      const schema = {
        name: 'string',
        age: 'number?'
      };

      const data = {
        name: 'John Doe'
        // age is optional
      };

      const result = validateWithSchema(data, schema);

      expect(result).toEqual(data);
    });

    it('should validate optional fields if they are present', () => {
      const schema = {
        name: 'string',
        age: 'number?'
      };

      const data = {
        name: 'John Doe',
        age: 30
      };

      const result = validateWithSchema(data, schema);

      expect(result).toEqual(data);
    });

    it('should throw BAD_FORM error if an optional field has the wrong type', () => {
      const schema = {
        name: 'string',
        age: 'number?'
      };

      const data = {
        name: 'John Doe',
        age: '30' // string instead of number
      };

      expect(() => validateWithSchema(data, schema)).toThrow();
    });
  });

  describe('Array Fields', () => {
    it('should validate array fields', () => {
      const schema = {
        name: 'string',
        tags: 'string[]'
      };

      const data = {
        name: 'John Doe',
        tags: ['tag1', 'tag2', 'tag3']
      };

      const result = validateWithSchema(data, schema);

      expect(result).toEqual(data);
    });

    it('should throw BAD_FORM error if an array field is not an array', () => {
      const schema = {
        name: 'string',
        tags: 'string[]'
      };

      const data = {
        name: 'John Doe',
        tags: 'tag1, tag2, tag3' // string instead of array
      };

      expect(() => validateWithSchema(data, schema)).toThrow();
    });

    it('should throw BAD_FORM error if an array item has the wrong type', () => {
      const schema = {
        name: 'string',
        scores: 'number[]'
      };

      const data = {
        name: 'John Doe',
        scores: [90, '95', 85] // mixed types
      };

      expect(() => validateWithSchema(data, schema)).toThrow();
    });

    it('should include the array field name in the error context', () => {
      const schema = {
        name: 'string',
        scores: 'number[]'
      };

      const data = {
        name: 'John Doe',
        scores: [90, '95', 85] // mixed types
      };

      try {
        validateWithSchema(data, schema);
      } catch (error: any) {
        expect(error.statusCode).toBe(400);
        expect(error.message).toBe('bad form data');
        expect(error.context).toContain('scores');
      }
    });
  });

  describe('Complex Schemas', () => {
    it('should validate objects with mixed field types', () => {
      const schema = {
        name: 'string',
        age: 'number?',
        isActive: 'boolean',
        tags: 'string[]',
        scores: 'number[]'
      };

      const data = {
        name: 'John Doe',
        age: 30,
        isActive: true,
        tags: ['tag1', 'tag2', 'tag3'],
        scores: [90, 95, 85]
      };

      const result = validateWithSchema(data, schema);

      expect(result).toEqual(data);
    });

    it('should allow extra fields not in the schema', () => {
      const schema = {
        name: 'string',
        age: 'number'
      };

      const data = {
        name: 'John Doe',
        age: 30,
        extraField: 'extra value'
      };

      const result = validateWithSchema(data, schema);

      // Extra fields are not included in the result
      expect(result).toEqual({
        name: 'John Doe',
        age: 30
      });
    });
  });
});
