import { describe, it, expect, vi, beforeEach } from 'vitest';
import { jsonExtraResponse } from '../../src/http-response/json-extra-response';
import { JSON_EXTRA_stringify } from '@divinci-ai/utils';

// Mock JSON_EXTRA_stringify
vi.mock('@divinci-ai/utils', () => ({
  JSON_EXTRA_stringify: vi.fn(data => JSON.stringify(data))
}));

describe('jsonExtraResponse', () => {
  let mockRes;

  beforeEach(() => {
    // Create a mock response object
    mockRes = {
      setHeader: vi.fn(),
      end: vi.fn()
    };

    // Reset mocks
    vi.clearAllMocks();
  });

  it('should set the content-type header to application/json', () => {
    const data = { key: 'value' };
    jsonExtraResponse(mockRes, data);

    expect(mockRes.setHeader).toHaveBeenCalledWith('content-type', 'application/json');
  });

  it('should stringify the data using JSON_EXTRA_stringify', () => {
    const data = { key: 'value' };
    jsonExtraResponse(mockRes, data);

    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
  });

  it('should end the response with the stringified data', () => {
    const data = { key: 'value' };
    const stringified = JSON.stringify(data);

    // Mock JSON_EXTRA_stringify to return our stringified data
    (JSON_EXTRA_stringify as any).mockReturnValueOnce(stringified);

    jsonExtraResponse(mockRes, data);

    expect(mockRes.end).toHaveBeenCalledWith(stringified);
  });

  it('should handle null or undefined data', () => {
    jsonExtraResponse(mockRes, null);
    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(null);

    jsonExtraResponse(mockRes, undefined);
    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(undefined);
  });

  it('should handle array data', () => {
    const data = [1, 2, 3];
    jsonExtraResponse(mockRes, data);

    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(data);
  });

  it('should handle primitive data types', () => {
    // Test with string
    jsonExtraResponse(mockRes, 'test string');
    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith('test string');

    // Test with number
    jsonExtraResponse(mockRes, 42);
    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(42);

    // Test with boolean
    jsonExtraResponse(mockRes, true);
    expect(JSON_EXTRA_stringify).toHaveBeenCalledWith(true);
  });
});
