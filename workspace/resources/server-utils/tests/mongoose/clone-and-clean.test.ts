import { describe, it, expect, vi, beforeEach } from 'vitest';
import { cloneAndCleanDoc } from '../../src/mongoose/clone-and-clean';
import { Model } from 'mongoose';

describe('cloneAndCleanDoc', () => {
  // Mock document with _id and __v fields
  let mockDoc: any;
  
  beforeEach(() => {
    // Create a mock document with _id and __v fields
    mockDoc = {
      _id: '507f1f77bcf86cd799439011',
      __v: 0,
      name: 'Test Document',
      description: 'This is a test document',
      metadata: {
        _id: '507f1f77bcf86cd799439012',
        __v: 1,
        createdAt: '2023-01-01',
        tags: ['test', 'document']
      },
      nestedArray: [
        {
          _id: '507f1f77bcf86cd799439013',
          __v: 2,
          value: 'item 1'
        },
        {
          _id: '507f1f77bcf86cd799439014',
          __v: 3,
          value: 'item 2'
        }
      ]
    };
  });

  it('should clone the document and remove _id and __v fields', () => {
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    // Check that the result is a clone (not the same object reference)
    expect(result).not.toBe(mockDoc);
    
    // Check that _id and __v are removed from the top level
    expect(result._id).toBeUndefined();
    expect(result.__v).toBeUndefined();
    
    // Check that other top-level properties are preserved
    expect(result.name).toBe('Test Document');
    expect(result.description).toBe('This is a test document');
    
    // Check that _id and __v are removed from nested objects
    expect(result.metadata._id).toBeUndefined();
    expect(result.metadata.__v).toBeUndefined();
    expect(result.metadata.createdAt).toBe('2023-01-01');
    expect(result.metadata.tags).toEqual(['test', 'document']);
    
    // Check that _id and __v are removed from objects in arrays
    expect(result.nestedArray[0]._id).toBeUndefined();
    expect(result.nestedArray[0].__v).toBeUndefined();
    expect(result.nestedArray[0].value).toBe('item 1');
    
    expect(result.nestedArray[1]._id).toBeUndefined();
    expect(result.nestedArray[1].__v).toBeUndefined();
    expect(result.nestedArray[1].value).toBe('item 2');
  });

  it('should handle null values', () => {
    mockDoc.nullField = null;
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.nullField).toBeNull();
  });

  it('should handle undefined values', () => {
    mockDoc.undefinedField = undefined;
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.undefinedField).toBeUndefined();
  });

  it('should handle primitive values', () => {
    mockDoc.stringField = 'string';
    mockDoc.numberField = 123;
    mockDoc.booleanField = true;
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.stringField).toBe('string');
    expect(result.numberField).toBe(123);
    expect(result.booleanField).toBe(true);
  });

  it('should handle deeply nested objects', () => {
    mockDoc.deeplyNested = {
      level1: {
        _id: '507f1f77bcf86cd799439015',
        __v: 4,
        level2: {
          _id: '507f1f77bcf86cd799439016',
          __v: 5,
          level3: {
            _id: '507f1f77bcf86cd799439017',
            __v: 6,
            value: 'deep value'
          }
        }
      }
    };
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.deeplyNested.level1._id).toBeUndefined();
    expect(result.deeplyNested.level1.__v).toBeUndefined();
    expect(result.deeplyNested.level1.level2._id).toBeUndefined();
    expect(result.deeplyNested.level1.level2.__v).toBeUndefined();
    expect(result.deeplyNested.level1.level2.level3._id).toBeUndefined();
    expect(result.deeplyNested.level1.level2.level3.__v).toBeUndefined();
    expect(result.deeplyNested.level1.level2.level3.value).toBe('deep value');
  });

  it('should handle Map objects', () => {
    const map = new Map();
    map.set('key1', { _id: '507f1f77bcf86cd799439018', __v: 7, value: 'map value 1' });
    map.set('key2', { _id: '507f1f77bcf86cd799439019', __v: 8, value: 'map value 2' });
    mockDoc.mapField = map;
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    // Maps are converted to objects during JSON.stringify/parse
    expect(result.mapField).toEqual({});
  });

  it('should handle empty objects', () => {
    mockDoc.emptyObject = {};
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.emptyObject).toEqual({});
  });

  it('should handle empty arrays', () => {
    mockDoc.emptyArray = [];
    
    const result = cloneAndCleanDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result.emptyArray).toEqual([]);
  });
});
