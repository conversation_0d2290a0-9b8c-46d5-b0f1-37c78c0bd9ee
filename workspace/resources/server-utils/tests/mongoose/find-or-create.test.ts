import { describe, it, expect, vi, beforeEach } from 'vitest';
import { findOrCreate } from '../../src/mongoose/find-or-create';

describe('findOrCreate', () => {
  let mockModel;
  let mockDoc;

  beforeEach(() => {
    // Create a mock document
    mockDoc = {
      _id: 'mock-id',
      name: 'Test Document'
    };

    // Create a mock model
    mockModel = {
      findOneAndUpdate: vi.fn().mockResolvedValue(mockDoc)
    };
  });

  it('should call findOneAndUpdate with the correct parameters', async () => {
    const query = { name: 'Test Document' };
    const result = await findOrCreate(mockModel, query);

    // Check that findOneAndUpdate was called with the correct parameters
    expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
      query,
      { $setOnInsert: query },
      {
        new: true,
        upsert: true
      }
    );

    // Check that the result is the mock document
    expect(result).toBe(mockDoc);
  });

  it('should handle errors from findOneAndUpdate', async () => {
    // Mock findOneAndUpdate to throw an error
    const error = new Error('Database error');
    mockModel.findOneAndUpdate.mockRejectedValueOnce(error);

    const query = { name: 'Test Document' };

    // Expect findOrCreate to throw the same error
    await expect(findOrCreate(mockModel, query)).rejects.toThrow('Database error');

    // Check that findOneAndUpdate was called with the correct parameters
    expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
      query,
      { $setOnInsert: query },
      {
        new: true,
        upsert: true
      }
    );
  });
});
