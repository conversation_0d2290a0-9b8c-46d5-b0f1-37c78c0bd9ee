import { describe, it, expect, vi, beforeEach } from 'vitest';
import { findOrCreate } from '../../src/mongoose/find-or-create';
import { refreshDoc } from '../../src/mongoose/refrsh-doc';
import { cloneAndCleanDoc } from '../../src/mongoose/clone-and-clean';
import { Model } from 'mongoose';

describe('Database Error Handling', () => {
  describe('findOrCreate error handling', () => {
    let mockModel: any;

    beforeEach(() => {
      // Create a mock model with findOneAndUpdate method
      mockModel = {
        findOneAndUpdate: vi.fn()
      };
    });

    it('should handle network errors', async () => {
      // Mock findOneAndUpdate to throw a network error
      const error = new Error('Network error');
      error.name = 'MongoNetworkError';
      mockModel.findOneAndUpdate.mockRejectedValueOnce(error);

      const query = { name: 'Test Document' };

      // Expect findOrCreate to throw the same error
      await expect(findOrCreate(mockModel, query)).rejects.toThrow('Network error');
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledTimes(1);
    });

    it('should handle timeout errors', async () => {
      // Mock findOneAndUpdate to throw a timeout error
      const error = new Error('Timeout error');
      error.name = 'MongoTimeoutError';
      mockModel.findOneAndUpdate.mockRejectedValueOnce(error);

      const query = { name: 'Test Document' };

      // Expect findOrCreate to throw the same error
      await expect(findOrCreate(mockModel, query)).rejects.toThrow('Timeout error');
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledTimes(1);
    });

    it('should handle validation errors', async () => {
      // Mock findOneAndUpdate to throw a validation error
      const error = new Error('Validation error');
      error.name = 'ValidationError';
      mockModel.findOneAndUpdate.mockRejectedValueOnce(error);

      const query = { name: 'Test Document' };

      // Expect findOrCreate to throw the same error
      await expect(findOrCreate(mockModel, query)).rejects.toThrow('Validation error');
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledTimes(1);
    });
  });

  describe('refreshDoc error handling', () => {
    let mockModel: any;
    let mockDoc: any;

    beforeEach(() => {
      // Create a mock document with _id field and constructor property
      mockDoc = {
        _id: '507f1f77bcf86cd799439011',
        name: 'Test Document',
        constructor: {} // Will be set to mockModel
      };

      // Create a mock model with findById method
      mockModel = {
        findById: vi.fn()
      };

      // Set the constructor property to the mock model
      mockDoc.constructor = mockModel;
    });

    it('should handle network errors', async () => {
      // Mock findById to throw a network error
      const error = new Error('Network error');
      error.name = 'MongoNetworkError';
      mockModel.findById.mockRejectedValueOnce(error);

      // Expect refreshDoc to throw the same error
      await expect(refreshDoc(mockDoc as InstanceType<Model<any>>)).rejects.toThrow('Network error');
      expect(mockModel.findById).toHaveBeenCalledTimes(1);
    });

    it('should handle timeout errors', async () => {
      // Mock findById to throw a timeout error
      const error = new Error('Timeout error');
      error.name = 'MongoTimeoutError';
      mockModel.findById.mockRejectedValueOnce(error);

      // Expect refreshDoc to throw the same error
      await expect(refreshDoc(mockDoc as InstanceType<Model<any>>)).rejects.toThrow('Timeout error');
      expect(mockModel.findById).toHaveBeenCalledTimes(1);
    });

    it('should handle document not found', async () => {
      // Mock findById to return null (document not found)
      mockModel.findById.mockResolvedValueOnce(null);

      // Expect refreshDoc to return null
      const result = await refreshDoc(mockDoc as InstanceType<Model<any>>);
      expect(result).toBeNull();
      expect(mockModel.findById).toHaveBeenCalledTimes(1);
    });
  });

  describe('cloneAndCleanDoc error handling', () => {
    it('should throw error for circular references', () => {
      // Create an object with a circular reference
      const circularObj: any = {
        _id: '507f1f77bcf86cd799439011',
        __v: 0,
        name: 'Circular Object'
      };
      circularObj.self = circularObj; // Create circular reference

      // Expect cloneAndCleanDoc to throw an error
      expect(() => cloneAndCleanDoc(circularObj as InstanceType<Model<any>>)).toThrow();
    });

    it('should throw error for undefined', () => {
      // Test with undefined
      expect(() => cloneAndCleanDoc(undefined as unknown as InstanceType<Model<any>>)).toThrow('"undefined" is not valid JSON');
    });

    it('should handle null correctly', () => {
      // Test with null
      const nullResult = cloneAndCleanDoc(null as unknown as InstanceType<Model<any>>);
      expect(nullResult).toBeNull();
    });

    it('should handle primitive values correctly', () => {
      // Test with primitive values
      const stringResult = cloneAndCleanDoc('string' as unknown as InstanceType<Model<any>>);
      expect(stringResult).toBe('string');

      const numberResult = cloneAndCleanDoc(123 as unknown as InstanceType<Model<any>>);
      expect(numberResult).toBe(123);

      const booleanResult = cloneAndCleanDoc(true as unknown as InstanceType<Model<any>>);
      expect(booleanResult).toBe(true);
    });
  });
});
