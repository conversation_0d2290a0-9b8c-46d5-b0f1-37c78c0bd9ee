import { describe, it, expect, vi, beforeEach } from 'vitest';
import { refreshDoc } from '../../src/mongoose/refrsh-doc';
import { Model } from 'mongoose';

describe('refreshDoc', () => {
  let mockModel: any;
  let mockDoc: any;
  
  beforeEach(() => {
    // Create a mock document with _id field and constructor property
    mockDoc = {
      _id: '507f1f77bcf86cd799439011',
      name: 'Test Document',
      description: 'This is a test document',
      constructor: {} // Will be set to mockModel
    };
    
    // Create a mock model with findById method
    mockModel = {
      findById: vi.fn().mockResolvedValue({
        _id: '507f1f77bcf86cd799439011',
        name: 'Updated Document',
        description: 'This is an updated document'
      })
    };
    
    // Set the constructor property to the mock model
    mockDoc.constructor = mockModel;
  });

  it('should call findById with the document _id', async () => {
    await refreshDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(mockModel.findById).toHaveBeenCalledWith('507f1f77bcf86cd799439011');
  });

  it('should return the result of findById', async () => {
    const result = await refreshDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result).toEqual({
      _id: '507f1f77bcf86cd799439011',
      name: 'Updated Document',
      description: 'This is an updated document'
    });
  });

  it('should handle errors from findById', async () => {
    const error = new Error('Database error');
    mockModel.findById.mockRejectedValueOnce(error);
    
    await expect(refreshDoc(mockDoc as InstanceType<Model<any>>)).rejects.toThrow('Database error');
  });

  it('should handle null result from findById', async () => {
    mockModel.findById.mockResolvedValueOnce(null);
    
    const result = await refreshDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(result).toBeNull();
  });

  it('should handle undefined _id', async () => {
    delete mockDoc._id;
    
    await refreshDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(mockModel.findById).toHaveBeenCalledWith(undefined);
  });

  it('should handle non-string _id (ObjectId)', async () => {
    const objectId = { toString: () => '507f1f77bcf86cd799439011' };
    mockDoc._id = objectId;
    
    await refreshDoc(mockDoc as InstanceType<Model<any>>);
    
    expect(mockModel.findById).toHaveBeenCalledWith(objectId);
  });
});
