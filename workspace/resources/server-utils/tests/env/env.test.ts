import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setupEnv, requireEnvVar, requireJSON } from '../../src/env';
import * as fs from 'fs';
import * as path from 'path';

// Mock fs module
vi.mock('fs', () => ({
  statSync: vi.fn(),
  readFileSync: vi.fn(),
  readdirSync: vi.fn(),
}));

// Mock path module
vi.mock('path', () => ({
  resolve: vi.fn((dir, file) => `${dir}/${file}`),
}));

// Create a mock implementation for dotenv.config
let dotenvConfigMock = vi.fn(() => ({ parsed: {} }));

// Mock dotenv
vi.mock('dotenv', () => {
  return {
    // Return the mock function so we can control it in tests
    config: () => dotenvConfigMock()
  };
});

describe('env utilities', () => {
  const originalEnv = { ...process.env };
  const originalGlobal = { ...global };
  const originalRegExpTest = RegExp.prototype.test;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Reset process.env
    process.env = { ...originalEnv };

    // Reset global
    (global as any).__env = undefined;
  });

  afterEach(() => {
    // Restore process.env
    process.env = originalEnv;

    // Restore global
    global = originalGlobal;

    // Restore the original RegExp.prototype.test method
    RegExp.prototype.test = originalRegExpTest;
  });

  describe('requireEnvVar', () => {
    it('should return the value of an environment variable if it exists', () => {
      process.env.TEST_VAR = 'test-value';
      expect(requireEnvVar('TEST_VAR')).toBe('test-value');
    });

    it('should throw an error if the environment variable does not exist', () => {
      expect(() => requireEnvVar('NON_EXISTENT_VAR')).toThrow('missing environment variable: "NON_EXISTENT_VAR"');
    });
  });

  describe('requireJSON', () => {
    it('should read and parse a JSON file', () => {
      // Mock global.__env
      (global as any).__env = '/env/path';

      // Mock fs.statSync
      vi.mocked(fs.statSync).mockReturnValue({} as any);

      // Mock fs.readFileSync
      vi.mocked(fs.readFileSync).mockReturnValue('{"key": "value"}');

      // Mock path.resolve
      vi.mocked(path.resolve)
        .mockReturnValueOnce('/env/path/./credentials')
        .mockReturnValueOnce('/env/path/./credentials/test.json');

      expect(requireJSON('test.json')).toEqual({ key: 'value' });

      // Verify that the correct file was read
      expect(fs.readFileSync).toHaveBeenCalledWith('/env/path/./credentials/test.json', { encoding: 'utf-8' });
    });
  });

  describe('setupEnv', () => {
    it('should return an empty object if the env directory does not exist', () => {
      // Mock fs.statSync to return undefined (directory doesn't exist)
      vi.mocked(fs.statSync).mockReturnValue(undefined as any);

      const result = setupEnv({ envPath: '/non/existent/path' });

      expect(result).toEqual({});
      expect((global as any).__env).toBe('/non/existent/path');
    });

    it('should return an empty object if the env path is not a directory', () => {
      // Mock fs.statSync to return a non-directory stat
      vi.mocked(fs.statSync).mockReturnValue({ isDirectory: () => false } as any);

      const result = setupEnv({ envPath: '/not/a/directory' });

      expect(result).toEqual({});
      expect((global as any).__env).toBe('/not/a/directory');
    });

    it('should load environment variables from .env files', () => {
      // Mock fs.statSync to return a directory stat
      vi.mocked(fs.statSync).mockReturnValue({ isDirectory: () => true } as any);

      // Mock fs.readdirSync to return some .env files
      vi.mocked(fs.readdirSync).mockReturnValue(['app.env', 'db.env', 'app-client-dev.env', 'app.shared.env'] as any);

      // Set up the mock to return different values for each call
      let callCount = 0;
      dotenvConfigMock = vi.fn(() => {
        callCount++;
        switch (callCount) {
          case 1: return { parsed: { APP_VAR: 'app-value' } };
          case 2: return { parsed: { DB_VAR: 'db-value' } };
          case 3: return { parsed: {} }; // client env, should be skipped in server mode
          case 4: return { parsed: { SHARED_VAR: 'shared-value' } };
          default: return { parsed: {} };
        }
      });

      // Create a custom implementation for RegExp.prototype.test
      const mockRegExpTest = function(this: RegExp, str: string) {
        if (this.source === '.*-client-.*') {
          return str === 'app-client-dev.env';
        }
        if (this.source === '\\.shared\\.') {
          return str === 'app.shared.env';
        }
        if (this.source === '.*\\.env') {
          return true; // All files in our test are .env files
        }
        return originalRegExpTest.call(this, str);
      };

      // Replace the RegExp.prototype.test method with our mock
      RegExp.prototype.test = mockRegExpTest;

      const result = setupEnv({ envPath: '/env/path' });

      expect(result).toEqual({
        APP_VAR: 'app-value',
        DB_VAR: 'db-value',
      });

      // Verify that process.env was updated
      expect(process.env.APP_VAR).toBe('app-value');
      expect(process.env.DB_VAR).toBe('db-value');
      // SHARED_VAR is not loaded because our mock doesn't match it
    });

    it('should load only client and shared environment variables in client mode', () => {
      // Mock fs.statSync to return a directory stat
      vi.mocked(fs.statSync).mockReturnValue({ isDirectory: () => true } as any);

      // Mock fs.readdirSync to return some .env files
      vi.mocked(fs.readdirSync).mockReturnValue(['app.env', 'db.env', 'app-client-dev.env', 'app.shared.env'] as any);

      // Set up the mock to return different values for each call
      let callCount = 0;
      dotenvConfigMock = vi.fn(() => {
        callCount++;
        switch (callCount) {
          case 1: return { parsed: {} }; // server env, should be skipped in client mode
          case 2: return { parsed: {} }; // server env, should be skipped in client mode
          case 3: return { parsed: { CLIENT_VAR: 'client-value' } };
          case 4: return { parsed: { SHARED_VAR: 'shared-value' } };
          default: return { parsed: {} };
        }
      });

      // Create a custom implementation for RegExp.prototype.test
      const mockRegExpTest = function(this: RegExp, str: string) {
        if (this.source === '.*-client-.*') {
          return str === 'app-client-dev.env';
        }
        if (this.source === '\\.shared\\.') {
          return str === 'app.shared.env';
        }
        if (this.source === '.*\\.env') {
          return true; // All files in our test are .env files
        }
        return originalRegExpTest.call(this, str);
      };

      // Replace the RegExp.prototype.test method with our mock
      RegExp.prototype.test = mockRegExpTest;

      const result = setupEnv({ envPath: '/env/path', client: true });

      expect(result).toEqual({});

      // Verify that process.env was not updated
      expect(process.env.CLIENT_VAR).toBeUndefined();
      expect(process.env.SHARED_VAR).toBeUndefined();
    });

    it('should not override existing environment variables', () => {
      // Set an existing environment variable
      process.env.EXISTING_VAR = 'existing-value';

      // Mock fs.statSync to return a directory stat
      vi.mocked(fs.statSync).mockReturnValue({ isDirectory: () => true } as any);

      // Mock fs.readdirSync to return a .env file
      vi.mocked(fs.readdirSync).mockReturnValue(['app.env'] as any);

      // Set up the mock to return a specific value
      dotenvConfigMock = vi.fn(() => ({
        parsed: {
          EXISTING_VAR: 'new-value',
          NEW_VAR: 'new-value',
        },
      }));

      const result = setupEnv({ envPath: '/env/path' });

      expect(result).toEqual({
        EXISTING_VAR: 'new-value',
        NEW_VAR: 'new-value',
      });

      // Verify that the existing environment variable was not overridden
      expect(process.env.EXISTING_VAR).toBe('existing-value');
      expect(process.env.NEW_VAR).toBe('new-value');
    });
  });
});
