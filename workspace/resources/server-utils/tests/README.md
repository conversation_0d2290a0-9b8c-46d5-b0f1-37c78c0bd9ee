# Server Utils Tests

This directory contains tests for the Server Utils package.

## Test Structure

- `unit/`: Unit tests for individual utility functions
- `integration/`: Integration tests for utility modules

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/errors/http` resolves to `src/errors/http`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { HTTP_ERRORS } from '@/errors/http';

// Import from tests directory
import { testData } from '~/helpers/test-data';
```

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## Writing Tests

When writing tests, follow these guidelines:

1. Use Vitest's testing functions (`describe`, `it`, `expect`, etc.)
2. Use path aliases for imports when possible
3. Test both success and error cases
4. Keep tests focused and small

### Example Test

```typescript
import { describe, it, expect } from 'vitest';
import { HTTP_ERRORS } from '@/errors/http';

describe('HTTP_ERRORS', () => {
  it('should have the correct status code for NOT_FOUND', () => {
    expect(HTTP_ERRORS.NOT_FOUND.statusCode).toBe(404);
  });

  it('should have the correct message for NOT_FOUND', () => {
    expect(HTTP_ERRORS.NOT_FOUND.message).toBe('Not Found');
  });
});
```
