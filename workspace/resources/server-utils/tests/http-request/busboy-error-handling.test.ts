import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';
import { IncomingMessage } from 'http';
import { Readable } from 'stream';
import { FileInfo } from 'busboy';

// Mock the busboy module
vi.mock('busboy', () => {
  return {
    default: vi.fn().mockImplementation((options) => {
      const eventHandlers: Record<string, Function> = {};
      const mockBusboy = {
        on: (event: string, handler: Function) => {
          eventHandlers[event] = handler;
          return mockBusboy;
        },
        emit: (event: string, ...args: any[]) => {
          if (eventHandlers[event]) {
            eventHandlers[event](...args);
          }
          return true;
        },
        _eventHandlers: eventHandlers
      };
      return mockBusboy;
    })
  };
});

// Import the module under test
import { CastedBusBoy } from '../../src/http-request/busboy/typeless-casting';

// Create mock file handler
const createMockFileHandler = () => {
  return {
    addStream: vi.fn().mockImplementation((stream) => {
      return {
        pointer: { id: 'file-123' },
        uploadPromise: Promise.resolve()
      };
    }),
    getStream: vi.fn().mockResolvedValue({ stream: new Readable({
      read() {
        this.push('test data');
        this.push(null);
      }
    }), info: {} as FileInfo }),
    deleteStream: vi.fn().mockResolvedValue(undefined)
  };
};

describe('Busboy Error Handling', () => {
  let mockReq: IncomingMessage;
  let mockFileHandler: ReturnType<typeof createMockFileHandler>;
  let parser: CastedBusBoy<any, any>;
  
  beforeEach(() => {
    // Create mock request
    mockReq = new Readable({
      read() {
        this.push('test data');
        this.push(null);
      }
    }) as IncomingMessage;
    mockReq.headers = {
      'content-type': 'multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW'
    };
    
    // Create mock file handler
    mockFileHandler = createMockFileHandler();
    
    // Create parser
    parser = new CastedBusBoy({
      name: 'string',
      email: 'string',
      file: 'file'
    }, mockFileHandler);
    
    // Mock pipe method
    mockReq.pipe = vi.fn().mockReturnValue(mockReq);
  });
  
  describe('Error handling in file uploads', () => {
    it('should reject with BAD_FORM error when a required field is missing', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit some fields but not all required ones
      busboy.emit('field', 'name', 'John Doe');
      // Missing 'email' field
      busboy.emit('file', 'file', new Readable({
        read() {
          this.push('test data');
          this.push(null);
        }
      }), { filename: 'test.txt' } as FileInfo);
      
      // Emit close event
      busboy.emit('close');
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should reject with BAD_FORM error when a field has the wrong type', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit a field with the wrong type
      busboy.emit('field', 'name', 'John Doe');
      busboy.emit('field', 'email', 'not-an-email'); // This would pass in the real implementation
      busboy.emit('field', 'file', 'This should be a file, not a string');
      
      // Emit close event
      busboy.emit('close');
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should reject with BAD_FORM error when a file field is sent as a string', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit fields
      busboy.emit('field', 'name', 'John Doe');
      busboy.emit('field', 'email', '<EMAIL>');
      busboy.emit('field', 'file', 'This should be a file, not a string');
      
      // Emit close event
      busboy.emit('close');
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should reject with BAD_FORM error when a string field is sent as a file', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit a file for a string field
      busboy.emit('file', 'name', new Readable({
        read() {
          this.push('test data');
          this.push(null);
        }
      }), { filename: 'test.txt' } as FileInfo);
      busboy.emit('field', 'email', '<EMAIL>');
      busboy.emit('file', 'file', new Readable({
        read() {
          this.push('test data');
          this.push(null);
        }
      }), { filename: 'test.txt' } as FileInfo);
      
      // Emit close event
      busboy.emit('close');
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should reject with BAD_FORM error when busboy emits an error', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit an error
      busboy.emit('error', new Error('Busboy error'));
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should reject with BAD_FORM error when file upload fails', async () => {
      // Mock the file handler to reject the upload promise
      mockFileHandler.addStream.mockImplementation(() => {
        return {
          pointer: { id: 'file-123' },
          uploadPromise: Promise.reject(new Error('Upload failed'))
        };
      });
      
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit fields and file
      busboy.emit('field', 'name', 'John Doe');
      busboy.emit('field', 'email', '<EMAIL>');
      busboy.emit('file', 'file', new Readable({
        read() {
          this.push('test data');
          this.push(null);
        }
      }), { filename: 'test.txt' } as FileInfo);
      
      // Emit close event
      busboy.emit('close');
      
      // The promise should reject with a BAD_FORM error
      await expect(parsePromise).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: 'bad form data'
        })
      );
    });
    
    it('should clean up files when an error occurs', async () => {
      // Create a promise for the parse operation
      const parsePromise = parser.consumeRequest(mockReq, { fileHandler: mockFileHandler });
      
      // Get the busboy instance
      const busboy = (mockReq.pipe as any).mock.calls[0][0];
      
      // Emit a file
      busboy.emit('file', 'file', new Readable({
        read() {
          this.push('test data');
          this.push(null);
        }
      }), { filename: 'test.txt' } as FileInfo);
      
      // Emit an error
      busboy.emit('error', new Error('Busboy error'));
      
      // The promise should reject
      await expect(parsePromise).rejects.toBeDefined();
      
      // The cleanup method should have been called
      expect(mockFileHandler.deleteStream).toHaveBeenCalled();
    });
  });
});
