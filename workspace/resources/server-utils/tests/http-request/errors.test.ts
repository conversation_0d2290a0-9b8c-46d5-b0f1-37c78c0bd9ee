import { describe, it, expect } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from '../../src/http-request/errors';

describe('HTTP_ERRORS', () => {
  it('should define common HTTP error types with status codes and messages', () => {
    expect(HTTP_ERRORS.BAD_FORM).toEqual({ statusCode: 400, message: 'bad form data' });
    expect(HTTP_ERRORS.UNAUTHORIZED).toEqual({ statusCode: 401, message: 'unauthorized' });
    expect(HTTP_ERRORS.FORBIDDEN).toEqual({ statusCode: 403, message: 'forbidden' });
    expect(HTTP_ERRORS.NOT_FOUND).toEqual({ statusCode: 404, message: 'not found' });
    expect(HTTP_ERRORS.LOCKED).toEqual({ statusCode: 423, message: 'document is locked' });
    expect(HTTP_ERRORS.ALREADY_EXISTS).toEqual({ statusCode: 409, message: 'the item you want to add already exists' });
    expect(HTTP_ERRORS.SERVER_ERROR).toEqual({
      statusCode: 500,
      message: 'something went wrong internally, someone should have been notified'
    });
    expect(HTTP_ERRORS.TIMEOUT).toEqual({
      statusCode: 500,
      message: 'something went wrong internally, someone should have been notified'
    });
    expect(HTTP_ERRORS.UNAVAILABLE).toEqual({ statusCode: 503, message: 'service unavailable, try again' });
  });
});

describe('HTTP_ERRORS_WITH_CONTEXT', () => {
  it('should create an error object with the given context', () => {
    const errorFn = HTTP_ERRORS_WITH_CONTEXT.BAD_FORM;
    const error = errorFn({ field: 'username' });

    expect(error).toEqual({
      statusCode: 400,
      message: 'bad form data',
      context: { field: 'username' }
    });
  });

  it('should create an error object with null context', () => {
    const errorFn = HTTP_ERRORS_WITH_CONTEXT.BAD_FORM;
    const error = errorFn(null);

    expect(error).toEqual({
      statusCode: 400,
      message: 'bad form data',
      context: null
    });
  });

  it('should have functions for all HTTP error types', () => {
    // Check that all error types have corresponding functions
    for (const key in HTTP_ERRORS) {
      expect(HTTP_ERRORS_WITH_CONTEXT[key]).toBeDefined();
      expect(typeof HTTP_ERRORS_WITH_CONTEXT[key]).toBe('function');
    }
  });
});
