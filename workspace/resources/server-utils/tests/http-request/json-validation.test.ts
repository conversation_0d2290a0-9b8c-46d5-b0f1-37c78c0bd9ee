import { describe, it, expect, vi, beforeEach } from "vitest";
import { HTTP_ERRORS_WITH_CONTEXT } from "../../src/http-request/errors";
import { jsonBody, formBody } from "../../src/http-request/middleware";
import { IncomingMessage } from "http";
import { Readable } from "stream";

// Mock the body/json and body/form modules
vi.mock("body/json", ()=>{
  return {
    default: vi.fn((req, callback)=>{
      // Simulate parsing JSON from the request
      if(req.simulateError) {
        callback(new Error("JSON parse error"), null);
      } else {
        callback(null, req.mockJson || {});
      }
    })
  };
});

vi.mock("body/form", ()=>{
  return {
    default: vi.fn((req, callback)=>{
      // Simulate parsing form data from the request
      if(req.simulateError) {
        callback(new Error("Form parse error"), null);
      } else {
        callback(null, req.mockForm || {});
      }
    })
  };
});

describe("JSON Request Body Validation", ()=>{
  let mockReq: IncomingMessage & { mockJson?: any, mockForm?: any, simulateError?: boolean };

  beforeEach(()=>{
    // Create a mock request
    mockReq = new Readable() as any;
    mockReq._read = ()=>{};
    mockReq.headers = {
      "content-type": "application/json"
    };
    mockReq.mockJson = { key: "value" };
    mockReq.mockForm = { field: "value" };
    mockReq.simulateError = false;
  });

  describe("jsonBody", ()=>{
    it("should parse JSON from the request body", async ()=>{
      const result = await jsonBody(mockReq);

      expect(result).toEqual({ key: "value" });
    });

    it("should reject with an error if JSON parsing fails", async ()=>{
      mockReq.simulateError = true;

      await expect(jsonBody(mockReq)).rejects.toThrow("JSON parse error");
    });

    it("should handle empty JSON objects", async ()=>{
      mockReq.mockJson = {};

      const result = await jsonBody(mockReq);

      expect(result).toEqual({});
    });

    it("should handle JSON arrays", async ()=>{
      mockReq.mockJson = [1, 2, 3];

      const result = await jsonBody(mockReq);

      expect(result).toEqual([1, 2, 3]);
    });

    it("should handle null values", async ()=>{
      mockReq.mockJson = null;

      const result = await jsonBody(mockReq);

      expect(result).toEqual({});
    });
  });

  describe("formBody", ()=>{
    it("should parse form data from the request body", async ()=>{
      const result = await formBody(mockReq);

      expect(result).toEqual({ field: "value" });
    });

    it("should reject with an error if form parsing fails", async ()=>{
      mockReq.simulateError = true;

      await expect(formBody(mockReq)).rejects.toThrow("Form parse error");
    });

    it("should handle empty form data", async ()=>{
      mockReq.mockForm = {};

      const result = await formBody(mockReq);

      expect(result).toEqual({});
    });
  });

  describe("Integration with HTTP_ERRORS", ()=>{
    it("should be usable with HTTP_ERRORS_WITH_CONTEXT for validation errors", async ()=>{
      // Create a function that uses jsonBody and HTTP_ERRORS_WITH_CONTEXT
      const validateJsonBody = async (req: IncomingMessage)=>{
        try {
          const body = await jsonBody(req);

          // Validate that body has required fields
          if(!body || typeof body !== "object" || body === null) {
            throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must be an object");
          }

          if(!("name" in body)) {
            throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Body must contain a name field");
          }

          return body;
        }catch(error) {
          if(error instanceof Error) {
            throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(error.message);
          }
          throw error;
        }
      };

      // Test with valid body
      mockReq.mockJson = { name: "Test" };
      const result = await validateJsonBody(mockReq);
      expect(result).toEqual({ name: "Test" });

      // Test with invalid body (missing name)
      mockReq.mockJson = { key: "value" };
      await expect(validateJsonBody(mockReq)).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: "bad form data",
          context: "Body must contain a name field"
        })
      );

      // Test with null body
      mockReq.mockJson = null;
      await expect(validateJsonBody(mockReq)).rejects.toEqual(
        expect.objectContaining({
          statusCode: 400,
          message: "bad form data"
        })
      );
    });
  });
});
