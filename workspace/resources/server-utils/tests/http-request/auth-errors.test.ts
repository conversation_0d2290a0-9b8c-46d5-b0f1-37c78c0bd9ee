import { describe, it, expect } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, HTTP_ERRORS_WITH_STACK } from '../../src/http-request/errors';

describe('Authentication and Authorization HTTP Errors', () => {
  describe('HTTP_ERRORS', () => {
    it('should define UNAUTHORIZED error with correct status code and message', () => {
      expect(HTTP_ERRORS.UNAUTHORIZED).toBeDefined();
      expect(HTTP_ERRORS.UNAUTHORIZED.statusCode).toBe(401);
      expect(HTTP_ERRORS.UNAUTHORIZED.message).toBe('unauthorized');
    });

    it('should define FORBIDDEN error with correct status code and message', () => {
      expect(HTTP_ERRORS.FORBIDDEN).toBeDefined();
      expect(HTTP_ERRORS.FORBIDDEN.statusCode).toBe(403);
      expect(HTTP_ERRORS.FORBIDDEN.message).toBe('forbidden');
    });
  });

  describe('HTTP_ERRORS_WITH_CONTEXT', () => {
    it('should create UNAUTHORIZED error with context', () => {
      const context = { userId: '123', action: 'read' };
      const error = HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED(context);

      expect(error).toBeDefined();
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('unauthorized');
      expect(error.context).toEqual(context);
    });

    it('should create FORBIDDEN error with context', () => {
      const context = { userId: '123', resource: 'document', action: 'edit' };
      const error = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN(context);

      expect(error).toBeDefined();
      expect(error.statusCode).toBe(403);
      expect(error.message).toBe('forbidden');
      expect(error.context).toEqual(context);
    });

    it('should use the default message for UNAUTHORIZED error', () => {
      const context = { userId: '123' };
      const error = HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED(context);

      expect(error).toBeDefined();
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('unauthorized');
      expect(error.context).toEqual(context);
    });

    it('should use the default message for FORBIDDEN error', () => {
      const context = { userId: '123', resource: 'document' };
      const error = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN(context);

      expect(error).toBeDefined();
      expect(error.statusCode).toBe(403);
      expect(error.message).toBe('forbidden');
      expect(error.context).toEqual(context);
    });
  });

  describe('HTTP_ERRORS_WITH_STACK', () => {
    it('should create UNAUTHORIZED error class with stack trace', () => {
      const UnauthorizedError = HTTP_ERRORS_WITH_STACK.UNAUTHORIZED;
      const error = new UnauthorizedError();

      expect(error).toBeDefined();
      expect(error).toBeInstanceOf(Error);
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('unauthorized');
      expect(error.stack).toBeDefined();
    });

    it('should create FORBIDDEN error class with stack trace', () => {
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      const error = new ForbiddenError();

      expect(error).toBeDefined();
      expect(error).toBeInstanceOf(Error);
      expect(error.statusCode).toBe(403);
      expect(error.message).toBe('forbidden');
      expect(error.stack).toBeDefined();
    });

    it('should support adding context to UNAUTHORIZED error', () => {
      const UnauthorizedError = HTTP_ERRORS_WITH_STACK.UNAUTHORIZED;
      const context = { userId: '123', action: 'read' };
      const error = new UnauthorizedError(context);

      expect(error.context).toEqual(context);
    });

    it('should support adding context to FORBIDDEN error', () => {
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      const context = { userId: '123', resource: 'document', action: 'edit' };
      const error = new ForbiddenError(context);

      expect(error.context).toEqual(context);
    });
  });

  describe('Error handling in authentication flows', () => {
    it('should be throwable and catchable', () => {
      expect(() => {
        throw HTTP_ERRORS.UNAUTHORIZED;
      }).toThrow();

      try {
        throw HTTP_ERRORS.UNAUTHORIZED;
      } catch (error) {
        expect(error).toBe(HTTP_ERRORS.UNAUTHORIZED);
        expect(error.statusCode).toBe(401);
      }
    });

    it('should be throwable and catchable with context', () => {
      const context = { userId: '123', action: 'read' };

      expect(() => {
        throw HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED(context);
      }).toThrow();

      try {
        throw HTTP_ERRORS_WITH_CONTEXT.UNAUTHORIZED(context);
      } catch (error: any) {
        expect(error.statusCode).toBe(401);
        expect(error.context).toEqual(context);
      }
    });

    it('should be throwable and catchable with stack', () => {
      const UnauthorizedError = HTTP_ERRORS_WITH_STACK.UNAUTHORIZED;

      expect(() => {
        throw new UnauthorizedError();
      }).toThrow();

      try {
        throw new UnauthorizedError();
      } catch (error: any) {
        expect(error.statusCode).toBe(401);
        expect(error.message).toBe('unauthorized');
        expect(error.stack).toBeDefined();
      }
    });
  });
});
