import { describe, it, expect, vi, beforeEach } from 'vitest';
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, HTTP_ERRORS_WITH_STACK } from '../../src/http-request/errors';
import { IncomingMessage } from 'http';
import { Readable } from 'stream';
import { Socket } from 'net';

// Mock Express types
type Request = IncomingMessage;
type Response = {
  statusCode: number;
  setHeader: (name: string, value: string) => void;
  end: (body?: any) => void;
  json: (body: any) => void;
};
type NextFunction = (err?: any) => void;

// Create mock middleware functions
const createAuthMiddleware = (requireAuth: boolean = true) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check for Authorization header
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      if (requireAuth) {
        return next(HTTP_ERRORS.UNAUTHORIZED);
      } else {
        return next();
      }
    }
    
    // Simulate token validation
    if (authHeader === 'Bearer valid-token') {
      // Add user info to request
      (req as any).user = { id: '123', role: 'user' };
      next();
    } else {
      next(HTTP_ERRORS.UNAUTHORIZED);
    }
  };
};

const createRoleCheckMiddleware = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = (req as any).user;
    
    if (!user) {
      return next(HTTP_ERRORS.UNAUTHORIZED);
    }
    
    if (user.role !== requiredRole) {
      return next(HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({ 
        requiredRole, 
        userRole: user.role 
      }));
    }
    
    next();
  };
};

const createResourceAccessMiddleware = (resourceId: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const user = (req as any).user;
    
    if (!user) {
      return next(HTTP_ERRORS.UNAUTHORIZED);
    }
    
    // Simulate resource access check
    const hasAccess = resourceId === 'public' || user.id === '123';
    
    if (!hasAccess) {
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      return next(new ForbiddenError({ 
        resourceId, 
        userId: user.id 
      }));
    }
    
    next();
  };
};

// Create error handling middleware
const errorHandler = (err: any, req: Request, res: Response, next: NextFunction) => {
  if (typeof err !== 'object') {
    err = {
      statusCode: 500,
      message: 'Unknown error'
    };
  }
  
  const statusCode = err.statusCode || 500;
  const message = err.message || 'Internal Server Error';
  const context = err.context || undefined;
  
  res.statusCode = statusCode;
  res.json({
    status: 'error',
    message,
    context
  });
};

describe('Middleware Error Handling', () => {
  let mockReq: Request;
  let mockRes: Response;
  let mockNext: NextFunction;
  
  beforeEach(() => {
    // Create mock request
    mockReq = new Readable() as Request;
    mockReq.headers = {};
    mockReq.socket = new Socket();
    
    // Create mock response
    mockRes = {
      statusCode: 200,
      setHeader: vi.fn(),
      end: vi.fn(),
      json: vi.fn()
    };
    
    // Create mock next function
    mockNext = vi.fn();
  });
  
  describe('Authentication Middleware', () => {
    it('should pass request when valid token is provided', () => {
      mockReq.headers.authorization = 'Bearer valid-token';
      
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).user).toBeDefined();
      expect((mockReq as any).user.id).toBe('123');
    });
    
    it('should return UNAUTHORIZED error when no token is provided', () => {
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
    });
    
    it('should return UNAUTHORIZED error when invalid token is provided', () => {
      mockReq.headers.authorization = 'Bearer invalid-token';
      
      const authMiddleware = createAuthMiddleware();
      authMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
    });
    
    it('should pass request when auth is optional and no token is provided', () => {
      const optionalAuthMiddleware = createAuthMiddleware(false);
      optionalAuthMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
      expect((mockReq as any).user).toBeUndefined();
    });
  });
  
  describe('Role Check Middleware', () => {
    it('should pass request when user has required role', () => {
      (mockReq as any).user = { id: '123', role: 'admin' };
      
      const roleCheckMiddleware = createRoleCheckMiddleware('admin');
      roleCheckMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
    });
    
    it('should return FORBIDDEN error when user has wrong role', () => {
      (mockReq as any).user = { id: '123', role: 'user' };
      
      const roleCheckMiddleware = createRoleCheckMiddleware('admin');
      roleCheckMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      const error = mockNext.mock.calls[0][0];
      expect(error.statusCode).toBe(403);
      expect(error.context).toEqual({ requiredRole: 'admin', userRole: 'user' });
    });
    
    it('should return UNAUTHORIZED error when user is not authenticated', () => {
      const roleCheckMiddleware = createRoleCheckMiddleware('admin');
      roleCheckMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.UNAUTHORIZED);
    });
  });
  
  describe('Resource Access Middleware', () => {
    it('should pass request when user has access to resource', () => {
      (mockReq as any).user = { id: '123', role: 'user' };
      
      const resourceAccessMiddleware = createResourceAccessMiddleware('public');
      resourceAccessMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalledWith();
    });
    
    it('should return FORBIDDEN error when user does not have access', () => {
      (mockReq as any).user = { id: '456', role: 'user' };
      
      const resourceAccessMiddleware = createResourceAccessMiddleware('private');
      resourceAccessMiddleware(mockReq, mockRes, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      const error = mockNext.mock.calls[0][0];
      expect(error.statusCode).toBe(403);
      expect(error.context).toEqual({ resourceId: 'private', userId: '456' });
    });
  });
  
  describe('Error Handler Middleware', () => {
    it('should format UNAUTHORIZED errors correctly', () => {
      errorHandler(HTTP_ERRORS.UNAUTHORIZED, mockReq, mockRes, mockNext);
      
      expect(mockRes.statusCode).toBe(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'unauthorized',
        context: undefined
      });
    });
    
    it('should format FORBIDDEN errors with context correctly', () => {
      const error = HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN({ 
        requiredRole: 'admin', 
        userRole: 'user' 
      });
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.statusCode).toBe(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'forbidden',
        context: { requiredRole: 'admin', userRole: 'user' }
      });
    });
    
    it('should format errors with stack correctly', () => {
      const ForbiddenError = HTTP_ERRORS_WITH_STACK.FORBIDDEN;
      const error = new ForbiddenError({ resourceId: 'private', userId: '456' });
      
      errorHandler(error, mockReq, mockRes, mockNext);
      
      expect(mockRes.statusCode).toBe(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'forbidden',
        context: { resourceId: 'private', userId: '456' }
      });
    });
    
    it('should handle non-object errors', () => {
      errorHandler('string error', mockReq, mockRes, mockNext);
      
      expect(mockRes.statusCode).toBe(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Unknown error',
        context: undefined
      });
    });
  });
  
  describe('Middleware Chains', () => {
    it('should pass through multiple middleware when all conditions are met', () => {
      mockReq.headers.authorization = 'Bearer valid-token';
      
      const authMiddleware = createAuthMiddleware();
      const roleCheckMiddleware = createRoleCheckMiddleware('user');
      const resourceAccessMiddleware = createResourceAccessMiddleware('public');
      
      // Simulate middleware chain
      authMiddleware(mockReq, mockRes, (err) => {
        if (err) return mockNext(err);
        roleCheckMiddleware(mockReq, mockRes, (err) => {
          if (err) return mockNext(err);
          resourceAccessMiddleware(mockReq, mockRes, mockNext);
        });
      });
      
      expect(mockNext).toHaveBeenCalledWith();
    });
    
    it('should stop at first failing middleware and pass error to next', () => {
      mockReq.headers.authorization = 'Bearer valid-token';
      
      const authMiddleware = createAuthMiddleware();
      const roleCheckMiddleware = createRoleCheckMiddleware('admin'); // This will fail
      const resourceAccessMiddleware = createResourceAccessMiddleware('public');
      
      // Simulate middleware chain
      authMiddleware(mockReq, mockRes, (err) => {
        if (err) return mockNext(err);
        roleCheckMiddleware(mockReq, mockRes, (err) => {
          if (err) return mockNext(err);
          resourceAccessMiddleware(mockReq, mockRes, mockNext);
        });
      });
      
      expect(mockNext).toHaveBeenCalled();
      const error = mockNext.mock.calls[0][0];
      expect(error.statusCode).toBe(403);
      expect(error.context).toEqual({ requiredRole: 'admin', userRole: 'user' });
    });
    
    it('should handle errors in the middleware chain and pass to error handler', () => {
      mockReq.headers.authorization = 'Bearer invalid-token';
      
      const authMiddleware = createAuthMiddleware();
      const roleCheckMiddleware = createRoleCheckMiddleware('admin');
      
      // Simulate middleware chain with error handler
      authMiddleware(mockReq, mockRes, (err) => {
        if (err) {
          return errorHandler(err, mockReq, mockRes, mockNext);
        }
        roleCheckMiddleware(mockReq, mockRes, (err) => {
          if (err) {
            return errorHandler(err, mockReq, mockRes, mockNext);
          }
          mockNext();
        });
      });
      
      expect(mockRes.statusCode).toBe(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'unauthorized',
        context: undefined
      });
    });
  });
});
