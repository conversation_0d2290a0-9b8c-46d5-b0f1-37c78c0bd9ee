import { describe, it, expect, vi } from "vitest";
import { getParam, getProtocol } from "../../src/http-request/req-getters";
import { HTTP_ERRORS } from "../../src/http-request/errors";

// Mock HTTP_ERRORS
vi.mock("../../src/http-request/errors", ()=>({
  HTTP_ERRORS: {
    NOT_FOUND: { statusCode: 404, message: "not found" }
  }
}));

describe("req-getters", ()=>{
  describe("getParam", ()=>{
    it("should return the parameter value if it exists", ()=>{
      const req = {
        params: {
          id: "123",
          name: "test"
        }
      };

      expect(getParam(req, "id")).toBe("123");
      expect(getParam(req, "name")).toBe("test");
    });

    it("should throw a NOT_FOUND error if params is undefined", ()=>{
      const req = {};

      expect(()=>getParam(req, "id")).toThrow();
    });

    it("should throw a NOT_FOUND error if the parameter does not exist", ()=>{
      const req = {
        params: {
          id: "123"
        }
      };

      expect(()=>getParam(req, "name")).toThrow();
    });
  });

  describe("getProtocol", ()=>{
    it("should return https for encrypted sockets", ()=>{
      const req = {
        socket: {
          encrypted: true
        },
        headers: {}
      };

      expect(getProtocol(req)).toBe("https");
    });

    it("should return http for non-encrypted sockets", ()=>{
      const req = {
        socket: {},
        headers: {}
      };

      expect(getProtocol(req)).toBe("http");
    });

    it("should use x-forwarded-proto header if available", ()=>{
      const req = {
        socket: {},
        headers: {
          "x-forwarded-proto": "https"
        }
      };

      expect(getProtocol(req)).toBe("https");
    });

    it("should use the first protocol if x-forwarded-proto has multiple values", ()=>{
      const req = {
        socket: {},
        headers: {
          "x-forwarded-proto": "https,http"
        }
      };

      expect(getProtocol(req)).toBe("https");
    });
  });
});
