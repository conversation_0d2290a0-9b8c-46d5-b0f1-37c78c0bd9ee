import { IncomingMessage, ServerResponse } from "http";
import {
  ShallowObject,
  castString,
  SingleType
} from "./casting";

import {
  ShallowObjectSchema, BaseValidTypes,
  isArrayRegExp, isOptionalRegExp,
  Castable
} from "./json-schema";

import Busboy, { FileInfo } from "busboy";
import { HTTP_ERRORS_WITH_CONTEXT } from "../errors";
import { JSON_Unknown } from "@divinci-ai/utils";
import { Readable } from "node:stream";

export type FileType = { stream: Readable, info: FileInfo };

const IS_CLEAN = Symbol("alreeady clean");

export type FileHandler<FilePointer extends JSON_Unknown > = {
  addStream: (stream: FileType) => { pointer: FilePointer, uploadPromise: Promise<any> },
  getStream: (pointer: FilePointer)=>Promise<FileType>,
  deleteStream: (pointer: FilePointer)=>Promise<any>,
};

type OptionalArgs<
  FilePointer extends JSON_Unknown,
  CastedResult extends ShallowObject<FilePointer & { info: FileInfo }> & { [IS_CLEAN]?: boolean }
> = {
  fileHandler?: FileHandler<FilePointer>,
  returnObject?: Partial<CastedResult>,
};

export class CastedBusBoy<
  CastedResult extends ShallowObject<FilePointer & { info: FileInfo }> & { [IS_CLEAN]?: boolean },
  FilePointer extends JSON_Unknown
> {
  keys: { [key: string]: Castable } = {};
  mandatoryKeys = new Set<string>();
  fileKeys: Array<string> = [];

  constructor(
    schema: ShallowObjectSchema<FilePointer, CastedResult>,
    private defaultHandler: FileHandler<FilePointer>
  ){
    Object.keys(schema).map((key)=>{
      const arrayConsumed = isArrayRegExp.exec(schema[key]);
      if(arrayConsumed){
        this.keys[key] = { type: arrayConsumed[1] as BaseValidTypes, array: true, optional: false };
        return key;
      }
      const optionalConsumed = isOptionalRegExp.exec(schema[key]);
      if(optionalConsumed){
        this.keys[key] = { type: optionalConsumed[1] as BaseValidTypes, array: false, optional: true };
        return key;
      }
      this.keys[key] = { type: schema[key] as BaseValidTypes, array: false, optional: false };
      this.mandatoryKeys.add(key);
      return key;
    }).map((key)=>{
      if(this.keys[key].type === "file"){
        this.fileKeys.push(key);
      }
    });
  }

  private handleKey(key: string, returnObject: Partial<CastedResult>, expectingKeys: Set<string>){
    const keyType = this.keys[key];
    if(typeof keyType === "undefined"){
      throw new Error("Extra unexpected key provided");
    }
    if(!keyType.array && key in returnObject){
      throw new Error(`key ${key} already in object but not expecting an array`);
    }
    expectingKeys.delete(key);
    return keyType;
  }

  private addValueToObject(key: string, value: SingleType<FilePointer & { info: FileInfo }>, returnObject: Partial<CastedResult>, keyType: Castable){
    if(!keyType.array){
      (returnObject[key] as typeof value) = value;
      return;
    }
    let prev = returnObject[key] as Array<typeof value>;
    if(!Array.isArray(prev)) prev = [];
    (returnObject[key] as Array<typeof value>) = prev.concat([value]);
  }
  consumeRequestResponse(req: IncomingMessage, res: ServerResponse, optional?: OptionalArgs<FilePointer, CastedResult>){
    const returnObject = optional?.returnObject || {};
    const fileHandler = optional?.fileHandler || this.defaultHandler;

    res.once("close", ()=>{
      this.cleanupBody({ returnObject, fileHandler });
    });
    return this.consumeRequest(req, { returnObject, fileHandler });
  }

  consumeRequest(req: IncomingMessage, optional?: OptionalArgs<FilePointer, CastedResult>): Promise<CastedResult>{
    const returnObject = optional?.returnObject || {};
    const fileHandler = optional?.fileHandler || this.defaultHandler;

    const expectedKeys = new Set(this.mandatoryKeys);
    return new Promise((resolve, reject)=>{
      const pendingFileUploads: Array<Promise<any>> = [];
      let hasError = false;

      const throwError = (e: Error)=>{
        console.error("busboy error");
        if(hasError){
          return console.error(`Already thrown attempting to consume body, new error: ${e.message}`);
        }
        hasError = true;
        req.unpipe(busboy);
        reject(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(e.message));
        this.cleanupBody({ fileHandler, returnObject });
      };

      const busboy = Busboy({ headers: req.headers });
      busboy.on("file", (key, stream, info)=>{
        if(hasError){
          stream.resume();
          return;
        }
        try {
          const keyType = this.handleKey(key, returnObject, expectedKeys);
          if(keyType.type !== "file"){
            throw new Error(`${key} is not expected to be a file but sent as one`);
          }
          const { pointer, uploadPromise } = fileHandler.addStream({ stream, info });

          const pointerWithInfo = pointer as FilePointer & { info: FileInfo };
          pointerWithInfo.info = info;
          this.addValueToObject(key, pointerWithInfo, returnObject, keyType);
          pendingFileUploads.push(uploadPromise);
        }catch(e: any){
          throwError(e);
        }
      });
      busboy.on("field", (key, val)=>{
        if(hasError) return;
        try {
          const keyType = this.handleKey(key, returnObject, expectedKeys);
          if(keyType.type === "file"){
            throw new Error(`${key} is expected to be a file but not sent as one`);
          }
          const value = castString(key, val, keyType.type);
          this.addValueToObject(key, value, returnObject, keyType);
        }catch(e: any){
          throwError(e.message);
        }
      });
      busboy.on("error", (e: Error)=>{
        throwError(e);
      });
      busboy.on("close", async ()=>{
        if(expectedKeys.size > 0){
          throwError(
            new Error(
              `Busboy is finished but not all mandatory keys have been provided: ${JSON.stringify(Array.from(expectedKeys.keys()))}`
            )
          );
        }
        if(hasError){
          console.error("Busboy closed but error already thrown");
          return;
        }
        try {
          await Promise.all(pendingFileUploads);
          resolve(returnObject as CastedResult);
        }catch(e: any){
          throwError(e);
        }
      });
      req.pipe(busboy);
    });
  }
  cleanupBody({ fileHandler, returnObject }: { fileHandler: FileHandler<FilePointer>, returnObject: Partial<CastedResult> }){
    if(returnObject[IS_CLEAN]) return Promise.resolve([]);

    returnObject[IS_CLEAN] = true;

    const promises: Array<Promise<any>> = [];
    type DeleteResult = { key: string, pointer: FilePointer, index: number, success: boolean, error?: any };
    const deletes: Array<DeleteResult> = [];
    for(const key of this.fileKeys){
      const keyType = this.keys[key];
      if(keyType.type !== "file"){
        // Should always be false
        continue;
      }
      let value = returnObject[key] as (undefined | FilePointer | Array<FilePointer>);
      if(typeof value === "undefined"){
        continue;
      }
      if(!Array.isArray(value)){
        value = [value];
      }
      (value as Array<FilePointer>).forEach((pointer, index)=>{
        const deleteObj: DeleteResult = { key, pointer: pointer, index: index, success: true };
        deletes.push(deleteObj);
        promises.push(
          fileHandler.deleteStream(pointer)
          .catch((error)=>{ deleteObj.success = false; deleteObj.error = error; })
        );
      });
    }
    return Promise.all(promises).then(()=>(deletes));
  }
}
