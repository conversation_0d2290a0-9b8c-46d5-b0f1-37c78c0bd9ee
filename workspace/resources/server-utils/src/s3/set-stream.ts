
import { S3 } from "@aws-sdk/client-s3";
import { S3FileLocation } from "./types";
import { Readable } from "stream";
import { Upload } from "@aws-sdk/lib-storage";
import mimetype from "mime-types";

export async function saveStreamToS3(
  s3: S3, r2File: S3FileLocation, stream: Readable,
  metadata: Record<string, string> = {}
) {
  const contentType = mimetype.lookup(r2File.originalName) || "application/octet-stream";
  const parallelUploads3 = new Upload({
    client: s3,
    leavePartsOnError: false, // optional manually handle dropped parts
    params: {
      Bucket: r2File.bucket,
      Key: r2File.objectKey,
      ContentType: contentType,
      Body: stream,
      Metadata: metadata,
    },
  });

  return await parallelUploads3.done();
}