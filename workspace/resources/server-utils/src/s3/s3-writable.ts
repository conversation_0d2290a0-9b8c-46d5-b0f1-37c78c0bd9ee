import { PassThrough, Writable } from "node:stream";
import { Upload } from "@aws-sdk/lib-storage";
import { S3 } from "@aws-sdk/client-s3";

const UPLOAD_FINISHED = Symbol("Upload Finished");
export class S3Writable extends Writable {
  public upload: Upload;
  private bodyStream = new PassThrough();
  private [UPLOAD_FINISHED] = false;
  private uploadPromise: Promise<any>;
  constructor(
    private r2: S3,
    public config: { Bucket: string, Key: string, ContentType: string, Metadata: Record<string, string> }
  ){
    super();
    this.upload = new Upload({
      client: this.r2,
      params: {
        ...config,
        Body: this.bodyStream,
      }
    });
    this.uploadPromise = this.upload.done().catch((e)=>{
      console.error("S3 Writable Upload Error:", e);
    });
  }

  _write(chunk: any, encoding: BufferEncoding, callback: (error?: Error | null) => void){
    this.bodyStream.write(chunk, encoding, callback);
  }

  async _final(callback: (error?: any) => void){
    try {
      this.bodyStream.end();
      this[UPLOAD_FINISHED] = true;
      await this.uploadPromise;
      callback();
    }catch(e){
      callback(e);
    }
  }

  async _destroy(error: Error | null, callback: (error?: Error | null) => void){
    if(this[UPLOAD_FINISHED]) return callback(error);
    try { await this.upload.abort(); }catch(e){ console.error("Ignore abort error"); }
    callback(error);
  }
}


