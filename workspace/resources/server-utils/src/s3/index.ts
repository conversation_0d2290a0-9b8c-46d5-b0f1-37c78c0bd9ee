
export * from "./types";
export * from "./set-stream";
export * from "./s3-readable";
export * from "./s3-extract-zip";
export * from "./s3-writable";

import { S3 } from "@aws-sdk/client-s3";

export async function existsInS3(
  r2: S3, { bucket, objectKey }: { bucket: string, objectKey: string }
){
  try {
    await r2.headObject({
      Bucket: bucket,
      Key: objectKey,
    });
    return true;
  }catch(e){
    return false;
  }
}




export function deleteItemFromS3(r2: S3, {
  bucket,
  objectKey,
}: {
  bucket: string,
  objectKey: string,
}){
  return r2.deleteObject({
    Bucket: bucket,
    Key: objectKey,
  });
}
