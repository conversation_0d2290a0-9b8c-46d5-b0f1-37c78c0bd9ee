
import { HTTP_ERRORS_WITH_CONTEXT } from "../http-request";
import <PERSON><PERSON><PERSON><PERSON> from "jszip";
import { S3 } from "@aws-sdk/client-s3";
import { lookup as mimetypeLookup } from "mime-types";

// Helper function to extract videos from zip file
export async function extractFilesFromZip(
  r2: S3,
  zipFileS3Location: {
    Bucket: string,
    Key: string,
  },
  validFileExtensions: Array<string>
){
  const zipObj = await r2.getObject(zipFileS3Location);
  if(!zipObj.Body){
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Invalid Input");
  }
  const zipBuffer = await zipObj.Body.transformToByteArray();
  const zip = new JSZip();

  // Load the zip content
  const contents = await zip.loadAsync(zipBuffer);
  const extractedVideos: Array<{
    Bucket: string, Key: string,
    filename: string,
    contentType: string,
    size: number,
  }> = [];

  await Promise.all(Object.entries(contents.files).map(async ([path, file])=>{
    // Skip directories
    if(file.dir) return;

    // Check if file is a video based on extension
    const isValidFilename = validFileExtensions.some(ext=>path.toLowerCase().endsWith(ext));

    if(!isValidFilename) return;

    const contentType = mimetypeLookup(path);
    if(!contentType){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid content type");
    }

    // Get the file content
    const fileData = await file.async("nodebuffer");

    // Upload extracted video to R2
    const objectKey = `${zipFileS3Location.Key.split("/").slice(0, -1).join("/")}/${path}`;

    await r2.putObject({
      Bucket: zipFileS3Location.Bucket,
      Key: objectKey,
      Body: fileData,
      ContentType: contentType
    });

    // Add to extracted videos list
    extractedVideos.push({
      Bucket: zipFileS3Location.Bucket,
      Key: objectKey,
      filename: path,
      contentType: contentType,
      size: fileData.length
    });
  }));

  return extractedVideos;
}

