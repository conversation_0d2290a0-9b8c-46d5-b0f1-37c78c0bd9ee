

import { Abort<PERSON>ontroller } from "abort-controller";
import { S3 } from "@aws-sdk/client-s3";
import { Readable } from "node:stream";

export async function getS3Readable(
  s3: S3, { bucket, objectKey }: { bucket: string, objectKey: string }
): Promise<{ stream: Readable, cancel: () => any }> {
  const abortController = new AbortController();
  const response = await s3.getObject(
    {
      Bucket: bucket,
      Key: objectKey,
    },
    { abortSignal: abortController.signal }
  );
  if(!response.Body) {
    throw new Error("❌ No body in get checks response. ");
  }
  return {
    stream: response.Body as Readable,
    cancel: () => abortController.abort(),
  };
}
