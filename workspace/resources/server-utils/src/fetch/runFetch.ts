import { Readable } from "stream";

import fetch from "node-fetch-commonjs";
import { JSON_Unknown } from "@divinci-ai/utils";

export async function runFetch(outStream: Readable, url: string, fetchConfig: {
  signal: undefined | AbortSignal,
  method: string,
  body: Readable,
  headers?: Record<string, string>
}){
  try {

    const response = await fetch(url, fetchConfig);

    if(!response.ok){
      throw new ErrorWithContext(
        `Fetch error: ${response.status} ${response.statusText}`,
        {
          url, method: fetchConfig.method,
          status: response.status, statusText: response.statusText,
          content: await response.text()
        }
      );
    }

    const responseBody = response.body;

    if(!responseBody){
      return outStream.push(null);
    }

    responseBody
    .on("data", (data)=>{
      outStream.push(data);
    }).on("end", ()=>{
      outStream.push(null);
    }).on("error", (e)=>{
      outStream.emit("error", e);
    });

  }catch(e){
    outStream.emit("error", e);
  }
}

class ErrorWithContext extends Error {
  constructor(text: string, public context: JSON_Unknown){
    super(text);
  }
}
