import { Duplex, PassThrough } from "stream";

import FormData from "form-data";
import { lookup } from "mime-types";

import { runFetch } from "./runFetch";

type FetchOptions = {
  method: string,
  file: { key: string, filename: string },
  body?: Record<string, boolean|number|string|Array<boolean|number|string>>,
  headers?: Record<string, string>,
};

export class FetchFormDataTransform extends Duplex {
  private reqStream = new PassThrough();
  private controller = new AbortController();
  public fetchPromise: Promise<any>;
  constructor(url: string, options: FetchOptions){
    super();
    const contentType = lookup(options.file.filename);
    if(!contentType){
      throw new Error("Couldn't find the mimetype of the file");
    }
    const formData = new FormData();
    formData.append(
      options.file.key, this.reqStream,
      { filename: options.file.filename, contentType: contentType }
    );
    if(options.body){
      for(const [k, v] of Object.entries(options.body)){
        if(k === options.file.key){
          throw new Error(`Key (${k}) on the body is the same as the file's key`);
        }
        if(Array.isArray(v)){
          for(const item of v) formData.append(k, item);
        } else {
          formData.append(k, v);
        }
      }
    }

    const headers = {
      ...(options.headers || {}),
      ...formData.getHeaders(),
    };
    this.fetchPromise = runFetch(this, url, {
      signal: this.controller.signal,
      method: options.method,
      body: formData,
      headers,
    });
  }

  _write(chunk: string, encoding: BufferEncoding | undefined, callback: (e?: any)=>any){
    this.reqStream.write(chunk, encoding as BufferEncoding, callback);
  }

  _final(callback: (e?: any)=>any){
    this.reqStream.end();
    callback();
  }

  // _read is not used directly because we push data in _startFetch,
  // but it must be defined.
  // No-op; data is pushed asynchronously as it comes from the fetch response.
  _read(){}

  // Provide a method to cancel the fetch.
  cancel(){
    this.controller.abort();
  }
}
