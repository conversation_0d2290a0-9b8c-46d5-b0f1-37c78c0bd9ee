import { Duplex, PassThrough } from "stream";

import { runFetch } from "./runFetch";

type FetchOptions = {
  method: string,
  headers?: Record<string, string>
};

export class FetchRawBodyTransform extends Duplex {
  private reqStream = new PassThrough();
  private controller = new AbortController();
  public fetchPromise: Promise<any>;
  constructor(url: string, options: FetchOptions) {
    super();
    const headers = {
      ...(options.headers || {}),
    };

    this.fetchPromise = runFetch(this, url, {
      signal: this.controller.signal,
      method: options.method,
      body: this.reqStream,
      headers,
    });
  }

  _write(chunk: string, encoding: BufferEncoding | undefined, callback: (e?: any)=>any) {
    this.reqStream.write(chunk, encoding as BufferEncoding, callback);
  }

  _final(callback: (e?: any)=>any) {
    this.reqStream.end();
    callback();
  }

  // _read is not used directly because we push data in _startFetch,
  // but it must be defined.
  // No-op; data is pushed asynchronously as it comes from the fetch response.
  _read() {}

  // Provide a method to cancel the fetch.
  cancel() {
    this.controller.abort();
  }
}

