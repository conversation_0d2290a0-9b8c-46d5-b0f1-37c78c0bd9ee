import { describe, it, expect, vi } from "vitest";
import { countTokens } from "../../../../src/rag/raw-to-chunks/open-parse/count-tokens";

// Mock the GPTTokens module
vi.mock("gpt-tokens", ()=>{
  return {
    GPTTokens: vi.fn().mockImplementation(({ messages })=>{
      // Simple mock implementation that counts words as a proxy for tokens
      // In reality, tokenization is more complex, but this is sufficient for testing
      const content = messages[0].content;
      const wordCount = content.split(/\s+/).filter(word=>word.length > 0).length;
      return {
        usedTokens: wordCount * 1.3 // Rough approximation
      };
    })
  };
});

describe("countTokens", ()=>{
  it("should count tokens in a string", ()=>{
    const content = "This is a test string with some words.";
    const tokenCount = countTokens(content);

    // Our mock implementation will count 8 words * 1.3 = ~10.4 tokens
    expect(tokenCount).toBeCloseTo(10.4);
  });

  it("should handle empty strings", ()=>{
    const content = "";
    const tokenCount = countTokens(content);

    expect(tokenCount).toBe(0);
  });

  it("should handle strings with special characters", ()=>{
    const content = "Special characters: !@#$%^&*()_+";
    const tokenCount = countTokens(content);

    // Our mock implementation will count 3 words * 1.3 = ~3.9 tokens
    expect(tokenCount).toBeCloseTo(3.9);
  });

  it("should handle strings with multiple spaces", ()=>{
    const content = "Multiple   spaces   between   words";
    const tokenCount = countTokens(content);

    // Our mock implementation will count 4 words * 1.3 = ~5.2 tokens
    expect(tokenCount).toBeCloseTo(5.2);
  });
});
