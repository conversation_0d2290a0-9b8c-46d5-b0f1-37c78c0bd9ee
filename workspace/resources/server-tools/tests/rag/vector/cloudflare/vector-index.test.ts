import { describe, it, expect, vi, beforeEach } from "vitest";

// Mock all external dependencies before importing the modules that use them

// Mock the server-utils module
vi.mock("@divinci-ai/server-utils", ()=>({
  HTTP_ERRORS: {
    SERVER_ERROR: new Error("Server Error")
  }
}));

// Mock the server-globals module
vi.mock("@divinci-ai/server-globals", ()=>({
  CLOUDFLARE_AI_API_URL: "https://api.cloudflare.com/client/v4",
  CLOUDFLARE_ACCOUNT_ID: "mock-account-id",
  fetchCloudflare: vi.fn()
}));

// Mock the utils module
vi.mock("@divinci-ai/utils", ()=>({}));

// Mock the models module
vi.mock("@divinci-ai/models", ()=>({}));

// Now import the modules that use the mocked dependencies
import {
  createVectorizeIndex,
  getVectorizeIndex,
  deleteVectorizeIndex,
  findOrCreateVectorizeIndex,
  listVectorizeIndexes
} from "../../../../src/rag/vector/cloudflare/vector-index";
import { VectorDescription } from "../../../../src/rag/vector/cloudflare/types";

// Import the mocked fetchCloudflare
import { fetchCloudflare } from "@divinci-ai/server-globals";

describe("vector-index", ()=>{
  const mockVectorIndex = "test-vector-index";
  const mockDescription = "Test vector index description";
  const mockVectorDescription = {
    id: "test-id",
    name: mockVectorIndex,
    description: mockDescription,
    created_on: "2023-01-01T00:00:00Z",
    modified_on: "2023-01-01T00:00:00Z",
    config: {
      preset: "@cf/baai/bge-base-en-v1.5"
    }
  };

  beforeEach(()=>{
    vi.resetAllMocks();
    vi.spyOn(console, "error").mockImplementation(()=>{});
    vi.spyOn(console, "log").mockImplementation(()=>{});
  });

  describe("createVectorizeIndex", ()=>{
    it("should create a vector index", async ()=>{
      vi.mocked(fetchCloudflare).mockResolvedValueOnce({
        result: mockVectorDescription,
        success: true
      });

      const result = await createVectorizeIndex(mockVectorIndex, mockDescription);

      expect(fetchCloudflare).toHaveBeenCalledWith(
        "/vectorize/v2/indexes",
        "POST",
        {
          name: mockVectorIndex,
          description: mockDescription,
          config: { preset: "@cf/baai/bge-base-en-v1.5" }
        }
      );
      expect(result).toEqual(mockVectorDescription);
    });

    it("should throw an error if creation fails", async ()=>{
      const mockError = {
        json: {
          errors: [{ code: 1000, message: "Error creating index" }]
        }
      };
      vi.mocked(fetchCloudflare).mockRejectedValueOnce(mockError);

      await expect(createVectorizeIndex(mockVectorIndex, mockDescription))
        .rejects.toEqual(mockError);

      expect(console.error).toHaveBeenCalled();
    });
  });

  describe("getVectorizeIndex", ()=>{
    it("should get a vector index", async ()=>{
      vi.mocked(fetchCloudflare).mockResolvedValueOnce({
        result: mockVectorDescription,
        success: true
      });

      const result = await getVectorizeIndex(mockVectorIndex);

      expect(fetchCloudflare).toHaveBeenCalledWith(
        `/vectorize/v2/indexes/${mockVectorIndex}`,
        "GET"
      );
      expect(result).toEqual(mockVectorDescription);
    });
  });

  describe("deleteVectorizeIndex", ()=>{
    it("should delete a vector index", async ()=>{
      vi.mocked(fetchCloudflare).mockResolvedValueOnce({
        result: mockVectorDescription,
        success: true
      });

      const result = await deleteVectorizeIndex(mockVectorIndex);

      expect(fetchCloudflare).toHaveBeenCalledWith(
        `/vectorize/v2/indexes/${mockVectorIndex}`,
        "DELETE"
      );
      expect(result).toEqual(mockVectorDescription);
    });
  });

  describe("findOrCreateVectorizeIndex", ()=>{
    it("should return existing vector index if found", async ()=>{
      vi.mocked(fetchCloudflare).mockResolvedValueOnce({
        result: mockVectorDescription,
        success: true
      });

      const result = await findOrCreateVectorizeIndex(mockVectorIndex, mockDescription);

      expect(fetchCloudflare).toHaveBeenCalledWith(
        `/vectorize/v2/indexes/${mockVectorIndex}`,
        "GET"
      );
      expect(fetchCloudflare).toHaveBeenCalledTimes(1);
      expect(result).toEqual(mockVectorDescription);
    });

    it("should create a new vector index if not found (404)", async ()=>{
      const notFoundError = {
        json: {
          result: null,
          success: false,
          errors: [{ code: 404, message: "Not found" }]
        },
        ok: false,
        status: 404
      };

      vi.mocked(fetchCloudflare)
        .mockRejectedValueOnce(notFoundError)
        .mockResolvedValueOnce({
          result: mockVectorDescription,
          success: true
        });

      const result = await findOrCreateVectorizeIndex(mockVectorIndex, mockDescription);

      expect(fetchCloudflare).toHaveBeenCalledTimes(2);
      expect(fetchCloudflare).toHaveBeenNthCalledWith(
        1,
        `/vectorize/v2/indexes/${mockVectorIndex}`,
        "GET"
      );
      expect(fetchCloudflare).toHaveBeenNthCalledWith(
        2,
        "/vectorize/v2/indexes",
        "POST",
        {
          name: mockVectorIndex,
          description: mockDescription,
          config: { preset: "@cf/baai/bge-base-en-v1.5" }
        }
      );
      expect(result).toEqual(mockVectorDescription);
    });

    it("should handle 400 error with code 3005 (recreate deleted vector)", async ()=>{
      const badRequestError = {
        json: {
          result: null,
          success: false,
          errors: [{ code: 3005, message: "Index was deleted" }]
        },
        ok: false,
        status: 400
      };

      vi.mocked(fetchCloudflare)
        .mockRejectedValueOnce(badRequestError)
        .mockResolvedValueOnce({
          result: mockVectorDescription,
          success: true
        });

      const result = await findOrCreateVectorizeIndex(mockVectorIndex, mockDescription);

      expect(fetchCloudflare).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockVectorDescription);
    });

    it("should throw SERVER_ERROR for other error types", async ()=>{
      const unknownError = {
        message: "Unknown error"
      };

      vi.mocked(fetchCloudflare).mockRejectedValueOnce(unknownError);

      await expect(findOrCreateVectorizeIndex(mockVectorIndex, mockDescription))
        .rejects.toThrow("Server Error");
    });
  });

  describe("listVectorizeIndexes", ()=>{
    it("should list all vector indexes", async ()=>{
      const mockIndexes = [mockVectorDescription];
      vi.mocked(fetchCloudflare).mockResolvedValueOnce({
        result: mockIndexes,
        success: true
      });

      const result = await listVectorizeIndexes();

      expect(fetchCloudflare).toHaveBeenCalledWith(
        "/vectorize/v2/indexes",
        "GET"
      );
      expect(result).toEqual(mockIndexes);
    });
  });
});
