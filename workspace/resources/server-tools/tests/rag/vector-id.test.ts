import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  extractIndexName,
  fileToVectorIds,
  createVectorId,
  extractFromVectorId
} from "../../src/rag/vector-id";

describe("vector-id", ()=>{
  beforeEach(()=>{
    // Mock console.log to avoid cluttering test output
    vi.spyOn(console, "log").mockImplementation(()=>{});
  });

  describe("extractIndexName", ()=>{
    it("should extract index name, model, and id from a target string", ()=>{
      const target = "some/path/model/id";
      const result = extractIndexName(target);

      expect(result).toEqual({
        vectorIndex: "model-id",
        model: "model",
        id: "id"
      });
    });

    it("should throw if id is missing", ()=>{
      const target = "some/path/model/";

      expect(()=>{
        extractIndexName(target);
      }).toThrow("❌ Unable to extract whiteLabelId from condensedTarget.");
    });

    it("should throw if model is missing", ()=>{
      const target = "some/path//id";

      expect(()=>{
        extractIndexName(target);
      }).toThrow("❌ Unable to extract mode from condensedTarget.");
    });
  });

  describe("fileToVectorIds", ()=>{
    it("should generate vector IDs for a file", ()=>{
      const fileId = "file123";
      const numberOfItems = 3;

      const generator = fileToVectorIds(fileId, numberOfItems);

      expect(generator.next().value).toBe("file123-chunk_0");
      expect(generator.next().value).toBe("file123-chunk_1");
      expect(generator.next().value).toBe("file123-chunk_2");
      expect(generator.next().done).toBe(true);
    });

    it("should generate no vector IDs when numberOfItems is 0", ()=>{
      const fileId = "file123";
      const numberOfItems = 0;

      const generator = fileToVectorIds(fileId, numberOfItems);

      expect(generator.next().done).toBe(true);
    });
  });

  describe("createVectorId", ()=>{
    it("should create a vector ID from a file ID and chunk number", ()=>{
      const fileId = "file123";
      const chunkNumber = 5;

      const vectorId = createVectorId(fileId, chunkNumber);

      expect(vectorId).toBe("file123-chunk_5");
    });
  });

  describe("extractFromVectorId", ()=>{
    it("should extract file ID and chunk number from a vector ID", ()=>{
      const vectorId = "file123-chunk_5";

      const result = extractFromVectorId(vectorId);

      expect(result).toEqual({
        fileId: "file123",
        chunkNumber: "5"
      });
    });

    it("should throw for an invalid vector ID", ()=>{
      const invalidVectorId = "invalid-vector-id";

      expect(()=>{
        extractFromVectorId(invalidVectorId);
      }).toThrow("❌ Invalid Vector Id");
    });
  });
});
