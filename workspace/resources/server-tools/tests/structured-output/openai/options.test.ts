import { describe, it, expect } from "vitest";
import { OpenAI4oMini } from "../../../src/structured-output/openai/options";

describe("OpenAI options", ()=>{
  describe("OpenAI4oMini", ()=>{
    it("should have the correct API configuration", ()=>{
      expect(OpenAI4oMini.api).toHaveProperty("assistant");
      expect(OpenAI4oMini.api.assistant).toBe("gpt-4o-mini-2024-07-18");
    });

    it("should have the correct info configuration", ()=>{
      expect(OpenAI4oMini.info).toHaveProperty("id");
      expect(OpenAI4oMini.info).toHaveProperty("title");
      expect(OpenAI4oMini.info).toHaveProperty("description");
      expect(OpenAI4oMini.info).toHaveProperty("url");
      expect(OpenAI4oMini.info).toHaveProperty("orgUrl");
      expect(OpenAI4oMini.info).toHaveProperty("org");
      expect(OpenAI4oMini.info).toHaveProperty("deprecated");

      expect(OpenAI4oMini.info.id).toBe("gpt-4o-mini-2024-07-18");
      expect(OpenAI4oMini.info.title).toBe("GPT-4o Mini");
      expect(OpenAI4oMini.info.description).toBe("Uses GTP-4o to generate structured output");
      expect(OpenAI4oMini.info.url).toBe("https://platform.openai.com/docs/guides/structured-outputs?lang=javascript");
      expect(OpenAI4oMini.info.orgUrl).toBe("https://openai.com/");
      expect(OpenAI4oMini.info.org).toBe("openai");
      expect(OpenAI4oMini.info.deprecated).toBe(false);
    });
  });
});
