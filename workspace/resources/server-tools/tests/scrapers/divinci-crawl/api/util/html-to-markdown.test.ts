import { describe, it, expect, vi } from "vitest";
import { htmlToMarkdown } from "../../../../../src/scrapers/divinci-crawl/api/util/html-to-markdown";

// Mock the NodeHtmlMarkdown module
vi.mock("node-html-markdown", ()=>{
  return {
    NodeHtmlMarkdown: vi.fn().mockImplementation(()=>{
      return {
        translate: vi.fn((html)=>{
          // Simple mock implementation for testing
          if(html.includes("<h1>")) {
            return html.replace(/<h1>(.*?)<\/h1>/g, "# $1");
          }
          if(html.includes("<p>")) {
            return html.replace(/<p>(.*?)<\/p>/g, "$1");
          }
          if(html.includes("<a href=")) {
            return html.replace(/<a href="(.*?)">(.*?)<\/a>/g, "[$2]($1)");
          }
          if(html.includes("<ul>")) {
            return html
              .replace(/<ul>(.*?)<\/ul>/gs, "$1")
              .replace(/<li>(.*?)<\/li>/g, "- $1");
          }
          return html;
        })
      };
    })
  };
});

describe("htmlToMarkdown", ()=>{
  it("should convert h1 tags to markdown headings", ()=>{
    const html = "<h1>Test Heading</h1>";
    const markdown = htmlToMarkdown(html);

    expect(markdown).toBe("# Test Heading");
  });

  it("should convert paragraph tags to plain text", ()=>{
    const html = "<p>This is a paragraph.</p>";
    const markdown = htmlToMarkdown(html);

    expect(markdown).toBe("This is a paragraph.");
  });

  it("should convert anchor tags to markdown links", ()=>{
    const html = "<a href=\"https://example.com\">Example Link</a>";
    const markdown = htmlToMarkdown(html);

    expect(markdown).toBe("[Example Link](https://example.com)");
  });

  it("should convert unordered lists to markdown lists", ()=>{
    const html = "<ul><li>Item 1</li><li>Item 2</li></ul>";
    const markdown = htmlToMarkdown(html);

    expect(markdown).toBe("- Item 1- Item 2");
  });

  it("should handle empty HTML", ()=>{
    const html = "";
    const markdown = htmlToMarkdown(html);

    expect(markdown).toBe("");
  });
});
