# Server Tools Tests

This directory contains tests for the Server Tools package.

## Test Structure

- `unit/`: Unit tests for individual server tool functions
- `integration/`: Integration tests for server tool modules

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/tools/server` resolves to `src/tools/server`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { serverTool } from '@/tools/server';

// Import from tests directory
import { mockServerTool } from '~/helpers/mock-server-tool';
```

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## Writing Tests

When writing tests, follow these guidelines:

1. Use Vitest's testing functions (`describe`, `it`, `expect`, etc.)
2. Use path aliases for imports when possible
3. Test both success and error cases
4. Keep tests focused and small

### Example Test

```typescript
import { describe, it, expect } from 'vitest';
import { serverTool } from '@/tools/server';

describe('serverTool', () => {
  it('should process server requests correctly', () => {
    const request = {
      method: 'GET',
      path: '/api/test'
    };
    
    const result = serverTool(request);
    expect(result).toEqual({
      status: 200,
      body: { success: true }
    });
  });

  it('should handle invalid requests', () => {
    const request = {
      method: 'INVALID',
      path: '/api/test'
    };
    
    const result = serverTool(request);
    expect(result).toEqual({
      status: 400,
      body: { error: 'Invalid method' }
    });
  });
});
```
