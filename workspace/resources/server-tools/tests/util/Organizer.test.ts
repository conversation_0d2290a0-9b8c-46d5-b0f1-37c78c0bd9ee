import { describe, it, expect, beforeEach } from "vitest";
import { Organizer } from "../../src/util/Organizer";

// Mock the PublicToolInfo type
interface MockPublicToolInfo {
  id: string,
  name: string,
  description: string,
  deprecated?: boolean,
}

// Mock tool type
interface MockTool {
  process: () => string,
}

describe("Organizer", ()=>{
  let organizer: Organizer<MockTool>;
  let mockInfo: MockPublicToolInfo;
  let mockTool: MockTool;

  beforeEach(()=>{
    organizer = new Organizer<MockTool>("TestTool");
    mockInfo = {
      id: "test-tool",
      name: "Test Tool",
      description: "A test tool"
    };
    mockTool = {
      process: ()=>"processed"
    };
  });

  it("should add a tool", ()=>{
    organizer.add(mockInfo as any, mockTool);

    expect(organizer.getOptions()).toContain("test-tool");
    expect(organizer.getAllInfo()).toHaveProperty("test-tool");
    expect(organizer.getAllTools()).toHaveProperty("test-tool");
  });

  it("should throw when adding a duplicate tool", ()=>{
    organizer.add(mockInfo as any, mockTool);

    expect(()=>{
      organizer.add(mockInfo as any, mockTool);
    }).toThrow("Item with name test-tool already exists");
  });

  it("should get tool info", ()=>{
    organizer.add(mockInfo as any, mockTool);

    const info = organizer.getInfo("test-tool");
    expect(info).toEqual(mockInfo);
  });

  it("should throw when getting info for non-existent tool", ()=>{
    expect(()=>{
      organizer.getInfo("non-existent");
    }).toThrow("TestTool non-existent does not exist");
  });

  it("should get a tool", ()=>{
    organizer.add(mockInfo as any, mockTool);

    const tool = organizer.getTool("test-tool");
    expect(tool).toHaveProperty("process");
    expect(tool).toHaveProperty("id", "test-tool");
  });

  it("should throw when getting a non-existent tool", ()=>{
    expect(()=>{
      organizer.getTool("non-existent");
    }).toThrow("TestTool non-existent does not exist");
  });

  it("should validate a tool exists and is not deprecated", ()=>{
    organizer.add(mockInfo as any, mockTool);

    expect(organizer.validTool("test-tool")).toBe(true);
  });

  it("should invalidate a non-existent tool", ()=>{
    expect(organizer.validTool("non-existent")).toBe(false);
  });

  it("should invalidate a deprecated tool", ()=>{
    const deprecatedInfo = { ...mockInfo, deprecated: true };
    organizer.add(deprecatedInfo as any, mockTool);

    expect(organizer.validTool("test-tool")).toBe(false);
  });

  it("should try to get a tool", ()=>{
    organizer.add(mockInfo as any, mockTool);

    const tool = organizer.tryToGetTool("test-tool");
    expect(tool).toHaveProperty("process");
    expect(tool).toHaveProperty("id", "test-tool");
  });

  it("should throw when trying to get a non-existent tool", ()=>{
    expect(()=>{
      organizer.tryToGetTool("non-existent");
    }).toThrow("TestTool non-existent does not exist");
  });

  it("should throw when trying to get a deprecated tool", ()=>{
    const deprecatedInfo = { ...mockInfo, deprecated: true };
    organizer.add(deprecatedInfo as any, mockTool);

    expect(()=>{
      organizer.tryToGetTool("test-tool");
    }).toThrow("TestTool test-tool is deprecated");
  });

  it("should use custom error handler when provided", ()=>{
    const errorHandler = (message: string)=>new Error(`Custom: ${message}`);

    expect(()=>{
      organizer.tryToGetTool("non-existent", errorHandler);
    }).toThrow("Custom: TestTool non-existent does not exist");
  });

  it("should try to get tool and info", ()=>{
    organizer.add(mockInfo as any, mockTool);

    const result = organizer.tryToGet("test-tool");
    expect(result).toHaveProperty("info");
    expect(result).toHaveProperty("api");
    expect(result.info).toEqual(mockInfo);
    expect(result.api).toHaveProperty("process");
  });

  it("should throw when trying to get info and api for a non-existent tool", ()=>{
    expect(()=>{
      organizer.tryToGet("non-existent");
    }).toThrow("TestTool non-existent does not exist");
  });

  it("should throw when trying to get info and api for a deprecated tool", ()=>{
    const deprecatedInfo = { ...mockInfo, deprecated: true };
    organizer.add(deprecatedInfo as any, mockTool);

    expect(()=>{
      organizer.tryToGet("test-tool");
    }).toThrow("TestTool test-tool is deprecated");
  });
});
