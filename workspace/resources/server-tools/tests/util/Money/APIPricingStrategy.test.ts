import { describe, it, expect } from "vitest";
import { APIPricingStrategy } from "../../../src/util/Money/APIPricingStrategy";

// Define example types for testing
interface ExamplePreUsageParams {
  inputTokens: number,
  estimatedOutputTokens: number,
}

interface ExamplePostUsageParams {
  inputTokens: number,
  actualOutputTokens: number,
}

describe("APIPricingStrategy", ()=>{
  it("should define the correct interface structure", ()=>{
    // Create a mock implementation of the interface
    const mockPricingStrategy: APIPricingStrategy<ExamplePreUsageParams, ExamplePostUsageParams> = {
      getEstimatedCost: (params)=>({
        inputCost: BigInt(params.inputTokens * 1),
        escrowOutputCost: BigInt(params.estimatedOutputTokens * 2)
      }),
      getFinalCost: (params)=>BigInt(params.inputTokens * 1 + params.actualOutputTokens * 2)
    };

    // Test the implementation
    const preUsageParams: ExamplePreUsageParams = {
      inputTokens: 100,
      estimatedOutputTokens: 200
    };

    const estimatedCost = mockPricingStrategy.getEstimatedCost(preUsageParams);
    expect(estimatedCost.inputCost).toBe(BigInt(100));
    expect(estimatedCost.escrowOutputCost).toBe(BigInt(400));

    const postUsageParams: ExamplePostUsageParams = {
      inputTokens: 100,
      actualOutputTokens: 150
    };

    const finalCost = mockPricingStrategy.getFinalCost(postUsageParams);
    expect(finalCost).toBe(BigInt(400)); // 100 + (150 * 2)
  });

  it("should handle zero values correctly", ()=>{
    // Create a mock implementation of the interface
    const mockPricingStrategy: APIPricingStrategy<ExamplePreUsageParams, ExamplePostUsageParams> = {
      getEstimatedCost: (params)=>({
        inputCost: BigInt(params.inputTokens * 1),
        escrowOutputCost: BigInt(params.estimatedOutputTokens * 2)
      }),
      getFinalCost: (params)=>BigInt(params.inputTokens * 1 + params.actualOutputTokens * 2)
    };

    // Test with zero values
    const zeroParams: ExamplePreUsageParams = {
      inputTokens: 0,
      estimatedOutputTokens: 0
    };

    const estimatedCost = mockPricingStrategy.getEstimatedCost(zeroParams);
    expect(estimatedCost.inputCost).toBe(BigInt(0));
    expect(estimatedCost.escrowOutputCost).toBe(BigInt(0));

    const zeroPostParams: ExamplePostUsageParams = {
      inputTokens: 0,
      actualOutputTokens: 0
    };

    const finalCost = mockPricingStrategy.getFinalCost(zeroPostParams);
    expect(finalCost).toBe(BigInt(0));
  });

  it("should handle different pricing models", ()=>{
    // Create a different pricing model
    const premiumPricingStrategy: APIPricingStrategy<ExamplePreUsageParams, ExamplePostUsageParams> = {
      getEstimatedCost: (params)=>({
        inputCost: BigInt(params.inputTokens * 5),
        escrowOutputCost: BigInt(params.estimatedOutputTokens * 10)
      }),
      getFinalCost: (params)=>BigInt(params.inputTokens * 5 + params.actualOutputTokens * 10)
    };

    const params: ExamplePreUsageParams = {
      inputTokens: 50,
      estimatedOutputTokens: 100
    };

    const estimatedCost = premiumPricingStrategy.getEstimatedCost(params);
    expect(estimatedCost.inputCost).toBe(BigInt(250));
    expect(estimatedCost.escrowOutputCost).toBe(BigInt(1000));

    const postParams: ExamplePostUsageParams = {
      inputTokens: 50,
      actualOutputTokens: 80
    };

    const finalCost = premiumPricingStrategy.getFinalCost(postParams);
    expect(finalCost).toBe(BigInt(1050)); // 50*5 + 80*10
  });
});
