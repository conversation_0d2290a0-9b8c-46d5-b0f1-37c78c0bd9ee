
import { XMLParser } from "fast-xml-parser";
import { DivinciCrawlError } from "./error";

export async function resolveSitemap(url: string){
  const sitemaps = await getSitemaps(url);
  const urls = new Set<string>();
  await Promise.all(sitemaps.map(async (sitemap) => {
    const sitemapUrls = await getSitemapUrls(sitemap);
    console.log(sitemapUrls);
    for(const sitemapUrl of sitemapUrls){
      urls.add(sitemapUrl.loc);
    }
  }));
  return Array.from(urls);
}

async function getSitemaps(url: string){
  const sitemapUrl = new URL("/robots.txt", url);
  const response = await fetch(sitemapUrl.href);
  if(!response.ok){
    throw new DivinciCrawlError(url, `Failed to fetch sitemap`, { statusCode: response.status });
  }
  const textRaw = await response.text();
  const textParts = textRaw.split("\n");

  const sitemaps = new Set<string>();

  for(const partRaw of textParts){
    if(partRaw.indexOf("Sitemap:") !== 0) continue;
    const sitemap = partRaw.slice("Sitemap:".length).trim();
    try {
      sitemaps.add(new URL(sitemap).href);
    }catch(e){
      console.log("❌ Failed to parse sitemap", sitemap);
    }
  }

  return Array.from(sitemaps);
}

const xmlParser = new XMLParser({ ignoreAttributes: true });
async function getSitemapUrls(sitemapUrl: string){
  const response = await fetch(sitemapUrl);
  if(!response.ok){
    throw new DivinciCrawlError(sitemapUrl, `Failed to fetch sitemap`, { statusCode: response.status });
  }
  const text = await response.text();
  const json = xmlParser.parse(text);
  console.log(json);
  return json.urlset.url;
}