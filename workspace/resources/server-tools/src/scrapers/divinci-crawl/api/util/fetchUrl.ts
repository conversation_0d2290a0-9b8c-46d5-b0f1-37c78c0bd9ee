import { parseUrl, DivinciCrawlError } from "./error";
import { load as cheerioLoad } from "cheerio";
import { htmlToMarkdown } from "./html-to-markdown";

export async function fetchUrl(url: string){
  const initialUrl = parseUrl(url);

  const response = await fetch(url);
  if(!response.ok){
    throw new DivinciCrawlError(url, `Failed to fetch`, { statusCode: response.status });
  }
  if(response.redirected){
    initialUrl.href = response.url;
  }
  if(response.headers.get("content-type")?.includes("text/html") === false){
    throw new DivinciCrawlError(
      url, `Content not HTML`, { contentType: response.headers.get("content-type") || "Unknown" }
    );
  }

  const text = await response.text();

  const $ = cheerioLoad(text);

  const baseUrl = handleBase($, initialUrl);

  $("head").remove();
  $("script").remove();
  $("noscript").remove();
  $("link").remove();
  $("input").remove();

  const links = new Set<string>();

  for(const htmlTag of ELEMENTS_WITH_HREF){
    const elements = $(htmlTag);
    elements.each((_, el)=>{
      try {
        const href = $(el).attr("href");
        if(!href) return;
        const hrefUrl = new URL(href, baseUrl);
        $(el).attr("href", hrefUrl.href);
        hrefUrl.hash = "";
        links.add(hrefUrl.href);
      }catch(e){
        console.error("Could not parse href", e);
      }
    });
  }

  for(const htmlTag of ELEMENTS_WITH_SRC){
    const elements = $(htmlTag);
    elements.each((_, el)=>{
      try {
        const src = $(el).attr("src");
        if(!src) return;
        const srcUrl = new URL(src, baseUrl);
        $(el).attr("src", srcUrl.href);
      }catch(e){
        console.error("Could not parse src", e);
      }
    });
  }

  const html = $.html();

  return {
    url: initialUrl.href,
    html: html,
    links: Array.from(links),
    markdown: htmlToMarkdown(html),
  };
}

function handleBase($: ReturnType<typeof cheerioLoad>, baseUrl: URL){
  const baseElements = $("head > base");
  if(baseElements.length === 0) return baseUrl;
  const baseElement = baseElements.last();
  const href = baseElement.attr("href");
  if(!href) return baseUrl;
  return new URL(href, baseUrl);
}

const ELEMENTS_WITH_HREF = [
  "a", "area"
];

// https://www.w3schools.com/tags/att_src.asp
const ELEMENTS_WITH_SRC = [
  "audio", "embed", "iframe", "img", "input", "script", "source", "track", "video"
];
