import { FIRECRAWL_APP } from "@divinci-ai/server-globals";
import { HTTPScraper } from "../../types";

import { FireCrawlError } from "./error";


export const scrapeUrl: HTTPScraper["scrapeUrl"] = async function(url: string){
  const crawlResponse = await FIRECRAWL_APP.scrapeUrl(url, { formats: ["html", "markdown", "links"] });

  if(!crawlResponse.success){
    throw new FireCrawlError(crawlResponse, `Failed to scrape`);
  }

  if(!crawlResponse.url){
    throw new FireCrawlError(crawlResponse, `Missing URL`);
  }
  if(!crawlResponse.markdown){
    throw new FireCrawlError(crawlResponse, `Missing Markdown`);
  }
  if(!crawlResponse.html){
    throw new FireCrawlError(crawlResponse, `Missing HTML`);
  }
  if(!crawlResponse.links){
    throw new FireCrawlError(crawlResponse, `Missing Links`);
  }

  return {
    url: crawlResponse.url,
    markdown: crawlResponse.markdown,
    html: crawlResponse.html,
    links: crawlResponse.links
  };
};
