import { CrawlParams } from '@mendable/firecrawl-js';
import { FIRECRAWL_APP } from "@divinci-ai/server-globals";

import { FireCrawlError } from "./error";
import { RagCrawlerOptions } from "@divinci-ai/models";

const DEFAULT_OPTIONS: CrawlParams = {
  limit: 100,
  scrapeOptions: {
    formats: ["html", "markdown"],
  }
};


export const crawlUrl = async function(
  url: string, { limit, excludePaths, allowedPaths }: RagCrawlerOptions
) {
  const params: CrawlParams = {
    ...DEFAULT_OPTIONS,
    limit,
  };

  if(excludePaths && excludePaths.length > 0){
    params.excludePaths = excludePaths;
  }
  if(allowedPaths && allowedPaths.length > 0){
    params.includePaths = allowedPaths;
  }

  const crawlResponse = await FIRECRAWL_APP.crawlUrl(url, params);
  if(!crawlResponse.success){
    throw new FireCrawlError(crawlResponse, `Failed to crawl`);
  }

  const errors: Array<{ url: string, error: string }> = [];
  const pages: Array<{ url: string, html: string, markdown: string }> = [];
  for(const page of crawlResponse.data){
    if(!page.metadata){
      throw new FireCrawlError(crawlResponse, `Missing metadata`);
    }
    const url = page.metadata.sourceURL;
    if(!url){
      throw new FireCrawlError(crawlResponse, `Missing source url`);
    }
    if(page.metadata.error){
      errors.push({ url: url, error: page.metadata.error });
      continue;
    }
    if(page.metadata.statusCode !== 200){
      errors.push({ url: url, error: `Status code is ${page.metadata.statusCode}` });
      continue;
    }
    if(!page.markdown){
      throw new FireCrawlError(crawlResponse, `Missing Markdown`);
    }
    if(!page.html){
      throw new FireCrawlError(crawlResponse, `Missing HTML`);
    }
    pages.push({
      url: url,
      markdown: page.markdown,
      html: page.html,
    });
  }

  return { pages, errors };
};


