import { HTTPScraper } from "./types";
import { Organizer } from "../util/Organizer";
import { TOOL as firecrawlTool } from "./firecrawl";
import { TOOL as divinciCrawlTool } from "./divinci-crawl";

export * from "./types";
export const SCRAPER_ASSISTANTS = new Organizer<HTTPScraper>("Url Scraper");

SCRAPER_ASSISTANTS.add(divinciCrawlTool.info, divinciCrawlTool.api);
SCRAPER_ASSISTANTS.add(firecrawlTool.info, firecrawlTool.api);

