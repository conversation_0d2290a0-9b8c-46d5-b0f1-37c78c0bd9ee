import { Readable } from "stream";
import { pipeline } from "node:stream/promises";
import { fileBlobToUnstructuredElements } from "@divinci-ai/server-globals";

import { ElementsToTextChunks } from "./ElementsToTextChunks";

import { Parser } from "stream-json";
import StreamArray from "stream-json/streamers/StreamArray";

export async function rawStreamToChunkStream(fileName: string, inputStream: Readable){
  const { stream, cancel } = await fileBlobToUnstructuredElements(inputStream, { fileName }, {});
  const toTextChunks = new ElementsToTextChunks();

  pipeline(
    stream,
    new Parser(),
    new StreamArray(),
    toTextChunks
  ).catch((err)=>{
    console.error("Error in ElementsToTextChunks", err);
    cancel();
  });

  return { readable: toTextChunks, cancel };
}
