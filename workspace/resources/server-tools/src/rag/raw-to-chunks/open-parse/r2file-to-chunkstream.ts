
// import { ElementsToTextChunks } from "./ElementsToTextChunks";
// import { logDebug } from "@divinci-ai/utils";
import { Readable } from "stream";
// import { Parser } from "stream-json";
// import StreamArray from "stream-json/streamers/StreamArray";
import { R2FileLocation, ChunkingConfig } from "../../types";

import { getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";
import { convertWebReadableToReadable } from "@divinci-ai/utils";

// First, create a helper function to convert Readable to Buffer
async function streamToBuffer(stream: Readable): Promise<Buffer>{
  const chunks: Buffer[] = [];
  for await (const chunk of stream) {
    chunks.push(Buffer.from(chunk));
  }
  return Buffer.concat(chunks);
}

export async function r2FileToChunkStream(
  r2File: R2FileLocation,
  config?: ChunkingConfig
): Promise<{ readable: Readable & { count: number }, cancel: () => void }>{
  const r2Target = {
    Bucket: r2File.bucket,
    Key: r2File.objectKey
  };

  const r2 = getWhitelabelVectorR2Instance();
  const r2Obj = await r2.getObject(r2Target);

  if(!r2Obj.Body) {
    throw new Error("Could not retrieve file body from R2");
  }

  const bodyStream = convertWebReadableToReadable(r2Obj.Body.transformToWebStream());
  const buffer = await streamToBuffer(bodyStream);

  // Add configuration
  const openparseConfig = {
    minTokens: config?.openparse?.minTokens || 64,
    maxTokens: config?.openparse?.maxTokens || 1024,
    embeddings_provider: config?.openparse?.embeddings?.provider || "cloudflare"
  };

  const formData = new FormData();
  formData.append("file", new Blob([buffer]), r2File.originalName);
  formData.append("config", JSON.stringify(openparseConfig));

  const response = await fetch(process.env.OPENPARSE_API_URL || "http://localhost:8084", {
    method: "POST",
    body: formData
  });

  if(!response.ok) {
    throw new Error(`OpenParse server error: ${response.statusText}`);
  }

  const readable = convertWebReadableToReadable(response.body as ReadableStream);
  (readable as any).count = 0; // Initialize count

  return {
    readable: readable as Readable & { count: number },
    cancel: ()=>{
      // Implement cancellation logic here
      readable.destroy();
    }
  };
}
