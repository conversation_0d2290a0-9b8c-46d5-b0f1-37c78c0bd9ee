import { Transform } from "stream";

import { countTokens } from "./count-tokens";

export class ElementsToTextChunks extends Transform {
  get count(){ return this.index;}
  public index = 0;
  constructor(){
    super({ objectMode: true });
  }

  _transform({ value: text }: { key: number, value: string }, _encoding: string, callback: (error?: any)=>any){
    if(typeof text !== 'string' || text.trim().length === 0){
      return callback();
    }

    const tokenCount = countTokens(text);

    this.push({ index: this.index, tokenCount, text });
    this.index++;
    callback();
  }
}
