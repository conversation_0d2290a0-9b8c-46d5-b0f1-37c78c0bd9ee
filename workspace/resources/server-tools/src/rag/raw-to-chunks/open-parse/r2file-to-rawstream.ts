import {
  fileBlobToOpenParseElements,
} from "@divinci-ai/server-globals";

import { getWhitelabelVectorR2Instance } from "@divinci-ai/server-globals";

import { R2FileLocation } from "../../types";
import { convertWebReadableToReadable, logDebug } from "@divinci-ai/utils";


export async function r2FileToRawOpenParseStream(r2File: R2FileLocation){
  const r2Target = {
    Bucket: r2File.bucket,
    Key: r2File.objectKey
  };

  const info = { fileName: r2File.originalName };

  const r2 = getWhitelabelVectorR2Instance();
  const r2Obj = await r2.getObject(r2Target);

  const bodyStream = r2Obj.Body
  if(!bodyStream){
    throw new Error(`❌ Could not retrieve file.`);
  }

  const stream = convertWebReadableToReadable(bodyStream.transformToWebStream());

  logDebug("🌐 open-parse is about to request elements.");

  return await fileBlobToOpenParseElements(stream, { ...info, });
}
