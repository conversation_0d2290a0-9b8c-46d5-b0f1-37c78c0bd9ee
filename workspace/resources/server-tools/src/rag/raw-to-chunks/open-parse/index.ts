import { PublicToolInfo } from "@divinci-ai/models";
import { RawFileToChunks } from "../../types";
import { r2FileToChunkStream } from "./r2file-to-chunkstream";
import { ElementsToTextChunks } from "./ElementsToTextChunks";
import { Readable } from "stream";
import { pipeline } from "node:stream/promises";
import { Parser } from "stream-json";
import StreamArray from "stream-json/streamers/StreamArray";
import { fileBlobToOpenParseElements } from "@divinci-ai/server-globals";


export const OpenParseRawFileToChunks: { info: PublicToolInfo, api: RawFileToChunks } = {
  info: {
    id: "openparse",
    url: "https://github.com/Divinci-AI/open-parse",
    title: "Open-Parse",
    picture: "/img/open-parse_logo.webp",
    description: "Open-Parse semantically converts PDF files to text chunks.",
    org: "OpenParse",
    orgUrl: "https://github.com/Divinci-AI/open-parse",
  },
  api: {
    transformFile: r2FileToChunkStream,
    transformStream: async (fileName: string, inputStream: Readable)=>{
      const toTextChunks = new ElementsToTextChunks();

      const { stream, cancel } = await fileBlobToOpenParseElements(inputStream, { fileName });

      pipeline(
        stream,
        new Parser(),
        new StreamArray(),
        toTextChunks
      ).catch((err)=>{
        console.error("Error in ElementsToTextChunks", err);
        cancel();
      });

      return { readable: toTextChunks, cancel };
    }
  }
};
