import { PublicToolInfo } from "@divinci-ai/models";

export class Organizer<T> {
  info: Record<string, PublicToolInfo> = {};
  tools: Record<string, T & { id:  string }> = {};
  constructor(private toolType: string){}
  add(info: PublicToolInfo, util: T){
    const id = info.id;
    if(id in this.tools) throw new Error(`Item with name ${id} already exists`);
    this.tools[id] = { ...util, id };
    this.info[id] = info;
  }
  getOptions(){
    return Object.keys(this.tools);
  }
  getAllInfo(){
    return this.info;
  }
  getAllTools(){
    return this.tools;
  }
  getInfo(slug: string){
    if(!(slug in this.info)){
      throw new Error(`${this.toolType} ${slug} does not exist`);
    }
    return this.info[slug];
  }
  getTool(slug: string){
    if(!(slug in this.tools)){
      throw new Error(`${this.toolType} ${slug} does not exist`);
    }
    return this.tools[slug];
  }
  validTool(baseModel: string){
    if(!(baseModel in this.tools)) return false;
    if(this.info[baseModel].deprecated) return false;
    return true;
  }
  tryToGetTool(baseModel: string, e?: (message: string)=>any){
    if(!(baseModel in this.tools)){
      if(e) throw e(`${this.toolType} ${baseModel} does not exist`);
      throw new Error(`${this.toolType} ${baseModel} does not exist`);
    }
    if(this.info[baseModel].deprecated){
      if(e) throw e(`${this.toolType} ${baseModel} is deprecated`);
      throw new Error(`${this.toolType} ${baseModel} is deprecated`);
    }
    return this.tools[baseModel];
  }
  tryToGet(baseModel: string, e?: (message: string)=>any){
    if(!(baseModel in this.tools)){
      if(e) throw e(`${this.toolType} ${baseModel} does not exist`);
      throw new Error(`${this.toolType} ${baseModel} does not exist`);
    }
    if(this.info[baseModel].deprecated){
      if(e) throw e(`${this.toolType} ${baseModel} is deprecated`);
      throw new Error(`${this.toolType} ${baseModel} is deprecated`);
    }
    return { info: this.info[baseModel], api: this.tools[baseModel] };
  }
}

