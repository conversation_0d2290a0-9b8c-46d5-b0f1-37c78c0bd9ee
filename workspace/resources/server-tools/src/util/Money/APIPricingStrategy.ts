
export interface APIPricingStrategy<PreUsageParams, PostUsageParams> {
  /**
   * Calculate the necessary cost for input usage,
   * plus how much to escrow for the output usage if needed.
   *
   * @param params Data needed to estimate cost (file size, # of pages, worst-case token usage, etc.)
   * @return An object with:
   *   - inputCost: The definite cost (charged immediately)
   *   - escrowOutputCost: The maximum or estimated cost for the variable portion
   */
  getEstimatedCost(params: PreUsageParams): {
    inputCost: bigint,
    escrowOutputCost: bigint,
  },

  /**
   * Calculate the final cost after the API runs,
   * usually based on actual usage.
   * If the final cost differs from (inputCost + escrowOutputCost),
   * you'll either refund or charge the difference in your transaction manager.
   *
   * @param params Data from the actual usage (actual audio length, actual tokens used, etc.)
   * @return The final total cost for the usage (input + actual output)
   */
  getFinalCost(params: PostUsageParams): bigint,
}
