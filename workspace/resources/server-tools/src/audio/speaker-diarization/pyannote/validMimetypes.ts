// p<PERSON>otte uses torchaudio under the hood
// https://github.com/faroit/torchaudio/blob/master/README.md#torchaudio-an-audio-library-for-pytorch

// mp3	audio/mpeg
// wav	audio/wav (often seen as audio/x-wav)
// aac	audio/aac
// ogg	audio/ogg
// flac	audio/flac (alternatively audio/x-flac in some systems)
// avr	audio/x-avr (non-standard; used by some legacy tools)
// cdda	audio/cdda (or audio/x-cdda; CD audio files typically lack a single universally accepted type)
// aiff	audio/aiff (sometimes audio/x-aiff)
// au	audio/basic
// amr	audio/amr (narrowband; note that wideband may use audio/amr-wb)
// mp2	audio/mpeg (the same type is often used for both mp2 and mp3)
// mp4	video/mp4 (if used as an audio container, audio/mp4 is also possible)
// ac3	audio/ac3
// avi	video/x-msvideo
// wmv	video/x-ms-wmv
// mpeg	video/mpeg
// ircam	audio/ircam (alternatively audio/x-ircam)


const FILE_EXTENSIONS = [
  "mp3",
  "wav",
  "aac",
  "ogg",
  "flac",
  "avr",
  "cdda",
  "aiff",
  "au",
  "amr",
  "mp2",
  "mp4",
  "ac3",
  "avi",
  "wmv",
  "mpeg",
  "ircam",
];

import { lookup as mimetypeLookup } from "mime-types";
const ENCODINGS: Array<string> = [];

for(const extension of FILE_EXTENSIONS){
  const contentType = mimetypeLookup(extension);
  if(!contentType) continue;
  if(ENCODINGS.includes(contentType)) continue;
  ENCODINGS.push(contentType);
}

ENCODINGS.push("audio/wav");

export function validMimetypes(){
  return ENCODINGS;
}


