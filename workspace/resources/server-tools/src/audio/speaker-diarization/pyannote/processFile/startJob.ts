import { handleFetch } from "@divinci-ai/utils";

import { PYANNOTE_APIKEY, PyannoteJobStatus } from "./pyannote-constants";

export async function startJob({ publicAudioUrl }: { publicAudioUrl: string }){
  return await handleFetch(fetch(
    "https://api.pyannote.ai/v1/diarize",
    {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        url: publicAudioUrl
      })
    }
  )) as {
    "jobId": string,
    "status": PyannoteJobStatus,
  };
}
