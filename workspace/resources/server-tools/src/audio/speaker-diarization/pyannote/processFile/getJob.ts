import { handleFetch } from "@divinci-ai/utils";

import { PYANNOTE_APIKEY, PyannoteJobStatus } from "./pyannote-constants";

export async function getJob(jobId: string){
  return await handleFetch(fetch(
    `https://api.pyannote.ai/v1/jobs/${jobId}`,
    {
      headers: {
        "Authorization": `Bearer ${PYANNOTE_APIKEY}`,
      }
    }
  )) as {
    jobId: string,
    createdAt: string,
    updatedAt: string,
  } & JobValue;
}

type JobValue = (
  | { status: Exclude<PyannoteJobStatus, "succeeded"> }
  | { status: "succeeded", output: { diarization: Array<Diarization> } }
);

type Diarization = {
  speaker: string,
  start: number,
  end: number,
};

