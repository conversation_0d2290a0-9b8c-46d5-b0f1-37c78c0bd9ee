
import { CLOUDFLARE_AUDIO_PUBLIC_URL } from "../constants";
import { SpeakerDiarization } from "../../types";

import { startJob } from "./startJob";
import { getJob } from "./getJob";
import { delay } from "@divinci-ai/utils";


export const processFile: SpeakerDiarization["processFile"] = async function(
  r2Pointer,
){
  const { jobId, status } = await startJob({
    publicAudioUrl: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + r2Pointer.Key
  });

  switch(status){
    case "pending":
    case "created":
    case "running":
    case "succeeded":
        break;
    case "canceled":
      throw new Error("Job canceled");
    case "failed":
      throw new Error("Job failed");
  }

  let jobResult = await getJob(jobId);
  while(jobResult.status !== "succeeded"){
    if(jobResult.status === "canceled"){
      throw new Error("Job canceled");
    }
    if(jobResult.status === "failed"){
      throw new Error("Job failed");
    }
    await delay(15 * 1000);
    jobResult = await getJob(jobId);
  }
  return jobResult.output.diarization;
};

