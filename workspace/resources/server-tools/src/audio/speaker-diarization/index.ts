import { Organizer } from "../../util/Organizer";
import { SpeakerDiarization } from "./types";

export const SPEAKER_DIARIZERS = new Organizer<SpeakerDiarization>("Transcribe Speech");

import { PYANNOTE } from "./pyannote";
SPEAKER_DIARIZERS.add(PYANNOTE.info, PYANNOTE.api);

import { DIVINCI_PYANNOTE } from "./divinci-pyannote";
SPEAKER_DIARIZERS.add(DIVINCI_PYANNOTE.info, DIVINCI_PYANNOTE.api);

// import { GOOGLE } from "./google";
// SPEAKER_DIARIZERS.add(GOOGLE.info, GOOGLE.api);

