
import { SpeechToText } from "../../../types";
import mimetypes from "mime-types";

import { OPENAI_API_KEY } from "@divinci-ai/server-globals";
import { getS3Readable } from "@divinci-ai/server-utils";

import fetch from "node-fetch-commonjs";
import FormData from "form-data";

export const processFile: SpeechToText["processFile"] = async function(
  r2Pointer
){
  const audioType = mimetypes.lookup(r2Pointer.Key);
  if(!audioType) throw new Error("Invalid audio filetype");


  const { stream: s3Readable } = await getS3Readable(r2Pointer.s3, {
    bucket: r2Pointer.Bucket,
    objectKey: r2Pointer.Key
  });

  const formData = new FormData();
  formData.append("file", s3Readable, { filename: r2Pointer.originalName, contentType: audioType });
  formData.append("model", "whisper-1");

  formData.append("language", "en");

  const response = await fetch("https://api.openai.com/v1/audio/transcriptions", {
    method: "POST",
    headers: {
      ...formData.getHeaders(),
      "Authorization": `Bearer ${OPENAI_API_KEY}`
    },
    body: formData
  });

  if(!response.ok){
    console.error("Fetch Failed:", await response.text());
    throw new Error(`Fetch Failed: ${response.url}`);
  }

  const json = await response.json() as { text: string };

  return json.text;
};
