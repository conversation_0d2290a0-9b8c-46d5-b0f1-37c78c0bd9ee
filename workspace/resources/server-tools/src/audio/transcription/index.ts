import { Organizer } from "../../util/Organizer";
import { SpeechToText } from "./types";

export const SPEECH_TO_TEXT = new Organizer<SpeechToText>("Speech to Text");

import { CF_OPENAI_WHISPER } from "./cloudflare-whisper";
SPEECH_TO_TEXT.add(CF_OPENAI_WHISPER.info, CF_OPENAI_WHISPER.api);

// import { HF_OPENAI_WHISPER } from "./hugginface-whisper-large-v3";
// SPEECH_TO_TEXT.add(HF_OPENAI_WHISPER.info, HF_OPENAI_WHISPER.api);

import { OPENAI_WHISPER } from "./openai-whisper";
SPEECH_TO_TEXT.add(OPENAI_WHISPER.info, OPENAI_WHISPER.api);
