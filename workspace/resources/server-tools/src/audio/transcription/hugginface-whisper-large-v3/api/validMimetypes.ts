
// https://platform.openai.com/docs/guides/speech-to-text#overview
const fileExtensions = [
  // "flac",
  "mp3",
  "mp4",
  "mpeg",
  "mpga",
  "m4a",
  "ogg",
  "wav",
  "webm"
];

import { lookup as mimetypeLookup } from "mime-types";
const ENCODINGS: Array<string> = [];

for(const extension of fileExtensions){
  const contentType = mimetypeLookup(extension);
  if(!contentType) continue;
  if(ENCODINGS.includes(contentType)) continue;
  ENCODINGS.push(contentType);
}

ENCODINGS.push("audio/wav");

export function validMimetypes(){
  return ENCODINGS;
}


