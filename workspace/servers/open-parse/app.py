# Environment Variable Setup
try:
    from dotenv import load_dotenv
except ImportError:
    print("⚠️ python-dotenv not installed, falling back to basic env handling")
    def load_dotenv(dotenv_path=None):
        # Simple fallback that does nothing
        pass
from src.config import load_env_files_with_dotenv
load_env_files_with_dotenv()
print("✅ Loaded env files.")

import os
import json
import logging
import sys
import time
from pathlib import Path
from flask import Flask, Response, request, jsonify
from werkzeug.wrappers import Request, Response
from flask_cors import CORS
import uuid

# Configure logging to stdout for Cloud Run
logging.basicConfig(
    stream=sys.stdout,  # Ensure logs go to stdout for Cloud Run
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

from src.file_to_parsed import file_to_parsed, parsed_to_text_stream
from src.json_stream import generator_to_json_array_stream
from src.zip_to_parsed import process_zip_contents
from src.utils.allowed_files import allowed_file

# Create Flask app
app = Flask(__name__)

# Add a health check endpoint for Cloud Run
@app.route('/_ah/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for Cloud Run.
    Returns a simple JSON response indicating the service is healthy.
    """
    return jsonify({"status": "healthy"}), 200

# Add error handlers
@app.errorhandler(404)
def not_found(e):
    logger.error(f"404 error: {request.url}")
    return jsonify({"error": "Not found", "path": request.path}), 404

@app.errorhandler(500)
def server_error(e):
    logger.error(f"500 error: {str(e)}")
    return jsonify({"error": "Internal server error"}), 500

# CORS Configuration
cors_config = {
    "origins": [
        r"^https://(.*\.)?divinci\.app$",
        *(os.getenv("OPENPARSE_CORS_ORIGINS", "").split(",") if os.getenv("OPENPARSE_CORS_ORIGINS") else []),
    ],
    "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    "allow_headers": [
        "Content-Type", "Authorization", "Origin", "X-Access-Token",
        "X-File-Name", "X-File-Id", "X-Target", "X-Processor",
        "X-Vectorize-Config", "X-Processor-Config"
    ],
    "expose_headers": [
        "X-File-Name", "X-File-Id", "X-Target", "X-Processor",
        "X-Vectorize-Config", "X-Processor-Config"
    ],
    "supports_credentials": True,
    "max_age": 600
}

CORS(app, **cors_config)

FORM_BODY_NAME = 'file'

@app.before_request
def log_request_info():
    logger.info(f"📨 Incoming request from origin: {request.headers.get('Origin')}")
    logger.info(f"📨 Request method: {request.method}")
    logger.info(f"📨 Request headers: {dict(request.headers)}")

@app.route('/', methods=['GET'])
def index():
    """
    Root endpoint that returns basic service information.
    Used for simple connectivity testing and service identification.
    """
    logger.info(f"Handling GET request to /")
    my_data = {
      "hello": "world",
      "whoami": "a doc processor"
    }
    return jsonify(my_data)

@app.route('/', methods=['POST'])
def upload_file():
    """
    Main document processing endpoint.
    Accepts a file upload and processes it using OpenParse.
    
    Request:
    - Form data with 'file' field containing the document
    - Optional 'config' field with JSON configuration:
      - semantic_chunking: boolean
      - embeddings_provider: string (cloudflare, ollama, none)
      - minTokens: integer
      - maxTokens: integer
      - chunkOverlap: integer
      - useTokens: boolean
    
    Returns:
    - JSON array stream of processed document chunks
    """
    logger.info(f"Handling POST request to /")
    logger.info(f"Request headers: {dict(request.headers)}")
    logger.info(f"Request files: {request.files}")

    try:
        files = request.files.getlist(FORM_BODY_NAME)
        config = json.loads(request.form.get('config', '{}'))

        if len(files) == 0:
            logger.warning("❌ No file in request")
            return jsonify({"error": f"No {FORM_BODY_NAME} part in the request."}), 400

        if len(files) > 1:
            logger.warning(f"❌ Multiple files received: {len(files)}")
            return jsonify({"error": f"Only one {FORM_BODY_NAME} allowed."}), 400

        file = files[0]
        logger.info(f"📄 Processing file: {file.filename}")

        if not allowed_file(file.filename):
            logger.warning(f"❌ Invalid file type: {file.filename}")
            return jsonify({"error": "File extension not allowed."}), 400

        # Process file
        try:
            # Create a more structured config object
            processing_config = {
                'semantic_chunking': config.get('semantic_chunking', False),
                'embeddings_provider': config.get('embeddings_provider', 'cloudflare'),
                'minTokens': config.get('minTokens', 64),
                'maxTokens': config.get('maxTokens', 1024),
                'chunkOverlap': config.get('chunkOverlap', 200),
                'useTokens': config.get('useTokens', True),
            }
            
            logger.info(f"⚙️ Config: {processing_config}")
            
            result = file_to_parsed(
                file,
                config=processing_config
            )
            logger.info("✅ File processed successfully")
            
            # Log the result before streaming
            stream_result = list(parsed_to_text_stream(result))
            logger.info(f"📤 Streaming response with {len(stream_result)} chunks")
            logger.debug(f"First chunk sample: {stream_result[0] if stream_result else 'no chunks'}")
            
            return Response(
                generator_to_json_array_stream(iter(stream_result)),
                status=200,
                mimetype='application/json'
            )
        except Exception as e:
            logger.error(f"❌ Error processing file: {str(e)}", exc_info=True)
            return jsonify({"error": str(e)}), 500

    except Exception as e:
        logger.error(f"❌ Server error: {str(e)}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

@app.route('/batch', methods=['POST'])
def process_batch():
    """
    Batch processing endpoint for multiple files.
    Processes multiple files in a single request.
    
    Request:
    - Form data with 'files[]' field containing multiple documents
    
    Returns:
    - JSON array with processing results for each file
    """
    logger.info("📥 Received batch processing request")

    if 'files[]' not in request.files:
        return jsonify({"error": "No files provided"}), 400

    files = request.files.getlist('files[]')
    results = []

    for file in files:
        if file and allowed_file(file.filename):
            try:
                result = file_to_parsed(file)
                results.append({
                    "filename": file.filename,
                    "content": list(parsed_to_text_stream(result))
                })
            except Exception as e:
                logger.error(f"Error processing {file.filename}: {str(e)}")
                results.append({
                    "filename": file.filename,
                    "error": str(e)
                })

    return jsonify(results)

@app.route('/folder', methods=['POST'])
def process_folder():
    """
    Folder processing endpoint.
    Accepts a ZIP file containing multiple documents and processes all files.
    
    Request:
    - Form data with 'folder' field containing a ZIP archive
    
    Returns:
    - JSON array with processing results for each file in the ZIP
    """
    if 'folder' not in request.files:
        return jsonify({"error": "No folder uploaded"}), 400

    uploaded_zip = request.files['folder']

    if uploaded_zip and uploaded_zip.filename.endswith('.zip'):
        # Process zip file contents
        results = process_zip_contents(uploaded_zip)
        return jsonify(results)

    return jsonify({"error": "Invalid folder format"}), 400

# Add at the top with other configs
SESSION_TIMEOUT = 3600  # 1 hour in seconds

@app.route('/fileUrl/init', methods=['POST'])
def init_processing():
    """
    Remote file processing initialization endpoint.
    Starts processing a file from a remote URL.
    
    Request:
    - JSON with:
      - url: string (URL to the file)
      - filename: string (optional)
      - config: object with processing configuration:
        - semantic_chunking: boolean
        - embeddings_provider: string
        - minTokens: integer
        - maxTokens: integer
        - chunkOverlap/overlap: integer
        - useTokens: boolean
    
    Returns:
    - JSON with session ID and processing metadata
    """
    try:
        logger.info("📥 Received init request")
        data = request.get_json()
        
        if not data:
            logger.error("❌ No JSON data in request")
            return jsonify({'error': 'No data provided'}), 400
            
        if 'url' not in data:
            logger.error("❌ No URL in request data")
            return jsonify({'error': 'No URL provided'}), 400

        url = data['url']
        filename = data.get('filename')
        config = data.get('config', {})

        # Extract chunking parameters from config
        if 'chunking' in config:
            chunking_config = config['chunking']
            config['minTokens'] = chunking_config.get('minTokens', 125)
            config['maxTokens'] = chunking_config.get('maxTokens', 250)
            config['chunkOverlap'] = chunking_config.get('overlap', 45)
            config['useTokens'] = chunking_config.get('useTokens', True)
            config['semantic_chunking'] = chunking_config.get('semantic', False)
            
        logger.info(f"🔄 Processing file: {filename} from URL: {url}")
        logger.info(f"⚙️ init_processing first config: {config}")

        # Process file but store results in memory/cache
        try:
            result = file_to_parsed(
                file_path=url,
                config=config,
                filename=filename
            )
            
            # Convert generator to list
            chunks = list(parsed_to_text_stream(result))
            
            # Generate session ID
            session_id = str(uuid.uuid4())
            
            # Store chunks with timestamp
            app.config[f'chunks_{session_id}'] = {
                'chunks': chunks,
                'timestamp': time.time(),
                'total_chunks': len(chunks)
            }
            
            response_data = {
                'total_chunks': len(chunks),
                'session_id': session_id
            }
            
            logger.info(f"✅ Init successful: {response_data}")
            return jsonify(response_data)

        except Exception as e:
            logger.error(f"❌ Error in file_to_parsed: {str(e)}", exc_info=True)
            return jsonify({'error': f'Processing error: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"❌ Error initializing processing: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/fileUrl/batch', methods=['POST'])
def get_chunks_batch():
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        batch_number = int(data.get('batch_number', 0))
        batch_size = int(data.get('batch_size', 50))

        if not session_id:
            logger.warning("No session ID provided")
            return jsonify({'error': 'No session ID provided'}), 400

        # Get chunks from session storage
        session_key = f'chunks_{session_id}'
        session_data = app.config.get(session_key)
        
        if not session_data or not isinstance(session_data, dict):
            logger.warning(f"Session expired or invalid: {session_id}")
            return jsonify({'error': 'Session expired or invalid'}), 404
            
        chunks = session_data.get('chunks', [])
        if not isinstance(chunks, list):
            logger.error(f"Invalid chunks format for session {session_id}")
            return jsonify({'error': 'Invalid chunks format'}), 500

        # Calculate start and end indices
        start_idx = batch_number * batch_size
        
        # If start index is beyond the chunks length, return empty array
        if start_idx >= len(chunks):
            logger.info(f"Batch {batch_number} requested but only {len(chunks)//batch_size + 1} batches exist")
            return jsonify([])
        
        end_idx = min(start_idx + batch_size, len(chunks))
        
        # Get the batch
        current_batch = chunks[start_idx:end_idx]
        
        # Update last access time
        session_data['timestamp'] = time.time()
        
        # Check if this is the last batch
        is_last_batch = end_idx >= len(chunks)
        
        logger.info(f"Returning batch {batch_number}: {len(current_batch)} chunks (is_last_batch: {is_last_batch})")
        return jsonify(current_batch)

    except Exception as e:
        logger.error(f"Error fetching batch: {str(e)}", exc_info=True)
        return jsonify({'error': str(e)}), 500

@app.route('/fileUrl/stream', methods=['POST'])
def stream_chunks():
    try:
        data = request.get_json()
        session_id = data.get('session_id')
        batch_size = data.get('batch_size', 50)

        if not session_id:
            return jsonify({'error': 'No session ID provided'}), 400

        def generate():
            chunks = app.config.get(f'chunks_{session_id}', {}).get('chunks', [])
            for i in range(0, len(chunks), batch_size):
                batch = chunks[i:i + batch_size]
                yield json.dumps(batch) + '\n'

        return Response(
            generate(),
            mimetype='application/x-ndjson',
            headers={
                'X-Accel-Buffering': 'no',  # Disable nginx buffering
                'Cache-Control': 'no-cache',
                'Transfer-Encoding': 'chunked'
            }
        )

    except Exception as e:
        logger.error(f"Streaming error: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Add a cleanup function to remove expired sessions
def cleanup_expired_sessions():
    current_time = time.time()
    for key in list(app.config.keys()):
        if key.startswith('chunks_'):
            session_data = app.config[key]
            if isinstance(session_data, dict):
                last_access = session_data.get('timestamp', 0)
                if current_time - last_access > SESSION_TIMEOUT:
                    app.config.pop(key, None)
                    logger.info(f"Cleaned up expired session {key}")

# Run cleanup every 15 minutes
from apscheduler.schedulers.background import BackgroundScheduler
scheduler = BackgroundScheduler()
scheduler.add_job(func=cleanup_expired_sessions, trigger="interval", minutes=15)
scheduler.start()

if __name__ == "__main__":
    logger.info("🚀 Starting OpenParse server")
    logger.info(f"🧵 Threads configured: {os.getenv('THREADS', 'not set')}")
    logger.info(f"Port: {os.getenv('PORT', '8084')}")
    logger.info(f"Python path: {os.getenv('PYTHONPATH')}")
    
    if os.getenv("ENVIRONMENT") == "development":
        # Use Werkzeug for development
        from werkzeug.serving import run_simple
        http_port = int(os.getenv("OPENPARSE_HTTP_PORT", "8084"))
        logger.info(f"🌱 Starting Werkzeug development server on port {http_port}")
        run_simple(
            hostname="0.0.0.0",
            port=http_port,
            application=app,
            use_reloader=True,
            use_debugger=True,
            threaded=True
        )
    else:
        # For production, we'll use Gunicorn
        # This block won't actually run - Gunicorn will import app directly
        logger.info("✅ App initialized, ready for Gunicorn")
        logger.info("📝 If crashing locally check if $ENVIRONMENT is set. See app.py.")
