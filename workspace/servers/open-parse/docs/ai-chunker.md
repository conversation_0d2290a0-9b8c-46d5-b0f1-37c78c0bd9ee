
# AI Chunker

Break up a PDF into pages
For Each page in Parrallel
- Send the page to the AI
- Tell it to chunk it with TOKENS_PER_CHUNK

We're asking for
{
  completed_chunks: Array<string>
  chunks_needing_previous_context: Array<string>
  chunks_needing_next_context: Array<string>
}

For Each completed chunk - Return it
For Each chunks_needing_previous_context - Add chunks from the bottom of the previous page
For Each chunks_needing_next_context - Add chunks from the top of the next page

Hope that's enough
