import tiktoken

# https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb
def text_to_chunks(text, max_tokens=768, token_buffer=128):
    """
    Splits text into chunks, each <= max_tokens in length 
    according to the 4o tokenization rules.

    :param text: The raw text to split.
    :param tokenizer_name: The model tokenizer to use (e.g. 'gpt-4', 'gpt-3.5-turbo').
    :param max_tokens: The maximum tokens per chunk.
    :return: A list of text chunks.
    """

    if max_tokens <= token_buffer:
        raise ValueError("max_tokens must be greater than token_buffer")

    # 1. Initialize the tokenizer
    tokenizer = tiktoken.get_encoding("o200k_base")
    # If you want to load a specific encoding for a specific model:
    # tokenizer = tiktoken.encoding_for_model(tokenizer_name)

    # 2. Convert the entire text to a list of tokens (integers)
    tokens = tokenizer.encode(text)

    chunks = []
    start = 0
    
    # 3. Generate sub-lists of tokens, each up to max_tokens
    while start < len(tokens):
        end = start + max_tokens
        chunk_tokens = tokens[start:end]

        # Decode the token chunk back into a string
        chunk_text = tokenizer.decode(chunk_tokens)
        chunks.append(chunk_text)

        start = end - token_buffer

    return chunks

def text_to_chunk_stream(text, max_tokens=768, token_buffer=128):
    if max_tokens <= token_buffer:
        raise ValueError("max_tokens must be greater than token_buffer")

    tokenizer = tiktoken.get_encoding("o200k_base")
    # convert text to tokens
    tokens = tokenizer.encode(text)

    start = 0

    while start < len(tokens):
        end = start + max_tokens
        # extract tokens
        chunk_tokens = tokens[start:end]

        # decode tokens to text
        yield tokenizer.decode(chunk_tokens)

        # set next start to current end
        start = end - token_buffer


def text_stream_to_chunk_stream(text_gen, max_tokens=768, token_buffer=128):
    if max_tokens <= token_buffer:
        raise ValueError("max_tokens must be greater than token_buffer")

    tokenizer = tiktoken.get_encoding("o200k_base")

    buffer_tokens = []

    for partial_text in text_gen:
        # tokenize this piece
        partial_tokens = tokenizer.encode(partial_text)
        # accumulate in buffer
        buffer_tokens.extend(partial_tokens)

        # as long as we have >= max_tokens, yield chunks
        while len(buffer_tokens) >= max_tokens:
            # get the first max_tokens from the buffer
            chunk_tokens = buffer_tokens[:max_tokens]
            # remove the max tokens from the buffer
            buffer_tokens = buffer_tokens[max_tokens - token_buffer:]

            yield tokenizer.decode(chunk_tokens)

    # after reading everything, if there's anything left in buffer, yield it
    if buffer_tokens:
        yield tokenizer.decode(buffer_tokens)
