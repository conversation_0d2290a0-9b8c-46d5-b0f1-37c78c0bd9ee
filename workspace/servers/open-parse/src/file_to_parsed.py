import os
import uuid
import time
import logging
import tempfile
import requests
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

UPLOAD_FOLDER = '/openparse-tmp'

# import openparse
from openparse import processing, DocumentParser


# Ollama setup
# semantic_pipeline = processing.SemanticIngestionPipeline(
#     embedding_provider="ollama",
#     # model="bge-large",  # or "nomic-embed-text"
#     model="nomic-embed-text",  # or "nomic-embed-text"
#     min_tokens=64,
#     max_tokens=1024,
# )

"""
Ollama Config
{
  embeddings_provider: "ollama",
  api_url: os.environ.get("OLLAMA_API_URL")
}
"""

"""
Cloudflare Config
{
  embeddings_provider: "cloudflare",
  model: "@cf/baai/bge-base-en-v1.5",  # Cloudflare's BGE model
  api_token: os.environ.get("CLOUDFLARE_API_TOKEN"),
  account_id: os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
}
"""

semantic_pipeline = processing.SemanticIngestionPipeline(
    min_tokens=64,
    max_tokens=1024,
    embeddings_provider="cloudflare",
    model="@cf/baai/bge-base-en-v1.5",
    api_token=os.environ.get("CLOUDFLARE_API_KEY"),
    account_id=os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
)

def save_file_as_unique(uploaded_file):
    _, ext = os.path.splitext(uploaded_file.filename)
    random_name = f"{uuid.uuid4()}{ext}"
    full_path = os.path.join(UPLOAD_FOLDER, random_name)
    uploaded_file.save(full_path)
    return full_path


try:
  # parser = openparse.DocumentParser()
    parser = DocumentParser(
        use_markitdown=True,
        processing_pipeline=semantic_pipeline
    )
    logger.info("✅ Parser initialized")
except Exception as e:
    logger.error(f"❌ Error initializing parser: {e}")
    parser = None

def file_to_parsed(
    file_path: str,
    config: dict = None,
    filename: str = None
) -> List[Dict[str, Any]]:
    """Process a file and return parsed chunks."""
    logger.info(f"📄 Processing file URL: {file_path}")
    logger.info(f"⚙️ file_to_parsed config: {config}")
    logger.info(f"📛 Filename: {filename}")
    
    try:
        # Extract config values for logging purposes
        min_tokens = config.get('minTokens', 256) if config else 256
        max_tokens = config.get('maxTokens', 1024) if config else 1024
        semantic_chunking = config.get('semantic', False) if config else False
        # Get embeddings provider from nested config
        embeddings_provider = 'none'
        if config and 'embeddings' in config and isinstance(config['embeddings'], dict):
            embeddings_provider = config['embeddings'].get('provider', 'none')

        # Log the actual credentials being used for the request
        logger.info("Making request with credentials:")
        logger.info(f"API Token length: {len(os.environ.get('CLOUDFLARE_API_KEY', ''))}")
        logger.info(f"Account ID present: {'CLOUDFLARE_ACCOUNT_ID' in os.environ}")

        # Use semantic chunking if specified
        if semantic_chunking:
            processing_pipeline = processing.SemanticIngestionPipeline(
                min_tokens=min_tokens,
                max_tokens=max_tokens,
                embeddings_provider=embeddings_provider,
                model="@cf/baai/bge-base-en-v1.5",
                api_token=os.environ.get("CLOUDFLARE_API_KEY"),
                account_id=os.environ.get("CLOUDFLARE_ACCOUNT_ID"),
            )
        else:
            processing_pipeline = processing.BasicIngestionPipeline()

        # Initialize parser with the chosen pipeline and pass the entire config object
        parser = DocumentParser(
            use_markitdown=True,
            processing_pipeline=processing_pipeline,
            config=config  # Pass the entire config object as a kwarg
        )

        # Download the file to a temporary location
        logger.info("Starting file download...")
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(filename)[1] if filename else '') as temp_file:
            logger.info(f"Created temporary file: {temp_file.name}")
            
            logger.info("Making HTTP request...")
            response = requests.get(file_path)
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            
            if not response.ok:
                logger.error(f"Download failed with status {response.status_code}")
                logger.error(f"Response content: {response.text}")
                response.raise_for_status()
                
            logger.info(f"Writing content to temporary file (content length: {len(response.content)})")
            temp_file.write(response.content)
            temp_file.flush()
            
            logger.info("Starting document parsing...")
            result = parser.parse(
                temp_file.name,
                embeddings_provider=embeddings_provider if semantic_chunking else None
            )
            logger.info("Document parsing completed successfully")

        # Clean up
        logger.info(f"Cleaning up temporary file: {temp_file.name}")
        os.unlink(temp_file.name)
        
        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        logger.error(f"Request details - URL: {file_path}")
        if hasattr(e.response, 'text'):
            logger.error(f"Response content: {e.response.text}")
        raise
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise


def parsed_to_jsontext(result):
    return result.model_dump_json()

def parsed_to_text(result):
    result = ""
    for node in result.nodes:
        if(node.text): result += " " + node.text
    return result

def parsed_to_text_stream(result):
    for node in result.nodes:
        if(node.text): yield node.text
