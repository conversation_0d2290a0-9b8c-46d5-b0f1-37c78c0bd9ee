import zipfile
from io import BytesIO
import logging

logger = logging.getLogger(__name__)

from src.utils.allowed_files import allowed_file
from src.file_to_parsed import file_to_parsed, parsed_to_text_stream


def process_zip_contents(zip_file):
    results = []
    with zipfile.ZipFile(zip_file, 'r') as zip_ref:
        for filename in zip_ref.namelist():
            if allowed_file(filename):
                with zip_ref.open(filename) as file:
                    try:
                        content = BytesIO(file.read())
                        content.filename = filename
                        result = file_to_parsed(content)
                        results.append({
                            "filename": filename,
                            "content": list(parsed_to_text_stream(result))
                        })
                    except Exception as e:
                        logger.error(f"Error processing {filename}: {str(e)}")
                        results.append({
                            "filename": filename,
                            "error": str(e)
                        })
    return results
