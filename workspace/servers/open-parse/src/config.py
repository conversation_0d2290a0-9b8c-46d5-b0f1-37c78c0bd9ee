import os
from pathlib import Path

try:
    from dotenv import load_dotenv
except ImportError:
    print("⚠️ python-dotenv not installed, falling back to basic env handling")
    def load_dotenv(dotenv_path=None):
        # Simple fallback that does nothing
        pass

def load_env_files_with_dotenv():
    print(f"🔍 Current working directory: {os.getcwd()}")
    env_folder = os.getenv('ENV_FOLDER')
    print(f"🔍 Looking for env files in: {env_folder}")
    if not env_folder:
        print("⚠️ No ENV_FOLDER specified")
        return

    cwd_contents = os.listdir(os.getcwd())
    print(cwd_contents)

    cwd_contents = os.listdir(env_folder)
    print(cwd_contents)

    env_path = Path(env_folder)
    if not env_path.exists():
        print(f"⚠️ ENV_FOLDER path doesn't exist: {env_folder}")
        return

    for env_file in env_path.glob('*.env'):
      previous_env = os.environ.copy()
      print(f"📝 Loading environment file: {env_file}")
      load_dotenv(dotenv_path=env_file)
      next_env = os.environ.copy()
      for key in next_env:
          if key not in previous_env or next_env[key] != previous_env[key]:
              print(f"✅ Loaded: {key}")


def load_env_files():
    print(f"🔍 Current working directory: {os.getcwd()}")
    env_folder = os.getenv('ENV_FOLDER')
    print(f"🔍 Looking for env files in: {env_folder}")
    if not env_folder:
        print("⚠️ No ENV_FOLDER specified")
        return

    env_path = Path(env_folder)
    if not env_path.exists():
        print(f"⚠️ ENV_FOLDER path doesn't exist: {env_folder}")
        return

    # Read all .env files in the directory
    for env_file in env_path.glob('*.env'):
        print(f"📝 Loading environment file: {env_file}")
        try:
            with open(env_file) as f:
                for line in f:
                    line = line.strip()
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue

                    # Split on first '=' only
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()

                        # Remove quotes if present
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]

                        os.environ[key] = value
                        print(f"✅ Loaded: {key}")
        except Exception as e:
            print(f"❌ Error loading {env_file}: {str(e)}")
