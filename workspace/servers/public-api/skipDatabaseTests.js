const fs = require('fs');
const path = require('path');
const { glob } = require('glob');

// List of test files that are still failing
const failingTests = [
  'setup/database/setupDBs.test.ts'
];

(async () => {
  const files = await glob("tests/unit/src/**/*.test.{ts,tsx}", { cwd: __dirname });
  console.log("🗄️ Found test files: ", files.length);

  let skippedCount = 0;
  
  for (const failingTest of failingTests) {
    const matchingFiles = files.filter(file => file.includes(failingTest));
    
    for (const file of matchingFiles) {
      const absoluteFilePath = path.resolve(__dirname, file);
      const newFilePath = absoluteFilePath.replace('.test.', '.test.skip.');
      
      try {
        fs.renameSync(absoluteFilePath, newFilePath);
        console.log(`📛 Renamed ${absoluteFilePath} to ${newFilePath}`);
        skippedCount++;
      } catch (error) {
        console.error(`❌ Failed to rename ${absoluteFilePath} to ${newFilePath}:`, error);
      }
    }
  }
  
  console.log(`✅ Skipped ${skippedCount} failing tests`);
})();
