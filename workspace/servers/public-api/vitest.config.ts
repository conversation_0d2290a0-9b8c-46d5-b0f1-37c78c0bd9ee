import { defineConfig } from "vitest/config";
import { resolve } from "path";

export default defineConfig({
  test: {
    globals: true,
    environment: "node",
    include: ["tests/**/*.{test,spec}.{js,jsx,ts,tsx}"],
    setupFiles: ["./vitest.setup.ts"],
    coverage: {
      provider: "istanbul",
      reporter: ["text", "json", "html"],
      exclude: ["node_modules/"]
    },
    deps: {
      inline: [/react-markdown/, /vfile/, /unist-util-stringify-position/, /unified/, /bail/]
    }
  },
  resolve: {
    extensions: [".ts", ".tsx", ".js", ".jsx", ".json", ".node"],
    alias: {
      "../../../../../../../src": resolve(__dirname, "./src"),
      "../../../../../src": resolve(__dirname, "./src"),
      "@": resolve(__dirname, "./src"),
      "~": resolve(__dirname, "./tests")
    }
  }
});
