import path from "path";
import fs from "fs";
import dotenv from "dotenv";
import { vi } from "vitest";

// Make vi available globally
global.vi = vi;

// Add Jest compatibility layer
global.jest = {
  fn: vi.fn,
  mock: vi.mock,
  spyOn: vi.spyOn,
  resetAllMocks: vi.resetAllMocks,
  clearAllMocks: vi.clearAllMocks,
  restoreAllMocks: vi.restoreAllMocks
} as any;

function getEnvDir(){
  let projectRoot;
  if(process.env.GITHUB_WORKSPACE) {
    // In GitHub Actions, use the GITHUB_WORKSPACE environment variable
    projectRoot = process.env.GITHUB_WORKSPACE;
    console.log("🙋🏻‍♂️ Using GITHUB_WORKSPACE environment variable: " + projectRoot);
  } else {
    // Locally, calculate the project root based on the directory structure
    projectRoot = path.resolve(__dirname, "./../../../");
    console.log("🙋🏻‍♂️❔ Using project root: " + projectRoot);
  }
  return path.join(projectRoot, "private-keys/local");
}

const envDir = getEnvDir();

// Read the directory and filter out files that don't end with '.env'
const envFiles = fs.readdirSync(envDir).filter(file=>file.endsWith(".env"));

// Load each .env file
envFiles.forEach(file=>{
  const envPath = path.join(envDir, file);
  console.log("⬇️  Loading environment variables from:", envPath);
  dotenv.config({ path: envPath });
});
