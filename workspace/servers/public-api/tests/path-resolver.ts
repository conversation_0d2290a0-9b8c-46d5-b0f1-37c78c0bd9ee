/**
 * This module provides path resolution helpers for tests
 */

import path from 'path';

// Root directory of the project
const ROOT_DIR = path.resolve(__dirname, '..');

/**
 * Resolves a path relative to the src directory
 * @param relativePath Path relative to src directory
 * @returns Absolute path
 */
export function fromSrc(relativePath: string): string {
  return path.resolve(ROOT_DIR, 'src', relativePath);
}

/**
 * Resolves a path relative to the tests directory
 * @param relativePath Path relative to tests directory
 * @returns Absolute path
 */
export function fromTests(relativePath: string): string {
  return path.resolve(ROOT_DIR, 'tests', relativePath);
}

/**
 * Resolves a path relative to the project root
 * @param relativePath Path relative to project root
 * @returns Absolute path
 */
export function fromRoot(relativePath: string): string {
  return path.resolve(ROOT_DIR, relativePath);
}
