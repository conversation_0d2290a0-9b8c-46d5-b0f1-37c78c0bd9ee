import { describe, it, expect, vi } from "vitest";

// Mock dependencies
vi.mock("@divinci-ai/server-globals", () => ({
  PRESIGNED_SECRET_LOCATION: Buffer.from("0123456789abcdef0123456789abcdef"), // 32 bytes for AES-256
  PRESIGNED_SECRET_SIGNITURE: "test-signiture-key"
}));

// Create simplified versions of the functions
function castFileInfo(fileInfo: any): any {
  if (!Array.isArray(fileInfo)) {
    throw new Error("bad form data");
  }
  
  for (const file of fileInfo) {
    if (file.byteSize < 0) {
      throw new Error("bad form data");
    }
    if (file.byteSize % 1 !== 0) {
      throw new Error("bad form data");
    }
  }
  
  return fileInfo;
}

function castPresignedConfig(configString: string): any {
  try {
    return JSON.parse(configString);
  } catch (error) {
    throw new Error("bad form data");
  }
}

describe("presigned-r2 index module", () => {
  describe("castFileInfo function", () => {
    it("should correctly cast valid file info", () => {
      const validFileInfo = [
        { byteSize: 1234, filename: "test1.jpg" },
        { byteSize: 5678, filename: "test2.png" }
      ];
      
      const result = castFileInfo(validFileInfo);
      expect(result).toEqual(validFileInfo);
    });
    
    it("should throw error if input is not an array", () => {
      expect(() => castFileInfo("not-an-array" as any)).toThrow();
    });
    
    it("should throw error if byteSize is negative", () => {
      const invalidFileInfo = [
        { byteSize: -100, filename: "test.jpg" }
      ];
      
      expect(() => castFileInfo(invalidFileInfo)).toThrow();
    });
    
    it("should throw error if byteSize is a decimal", () => {
      const invalidFileInfo = [
        { byteSize: 123.45, filename: "test.jpg" }
      ];
      
      expect(() => castFileInfo(invalidFileInfo)).toThrow();
    });
  });
  
  describe("castPresignedConfig function", () => {
    it("should correctly cast valid JSON string to PresignedResult", () => {
      const validConfig = {
        url: "https://example.com/presigned-url",
        purpose: "test-purpose",
        userId: "user-123",
        expiration: Date.now() + 3600000,
        location: "encrypted-location",
        filename: "test.jpg",
        byteSize: 1234,
        signiture: "mock-signature"
      };
      
      const result = castPresignedConfig(JSON.stringify(validConfig));
      expect(result).toEqual(validConfig);
    });
    
    it("should throw error if input is not valid JSON", () => {
      expect(() => castPresignedConfig("not-valid-json")).toThrow();
    });
  });
});
