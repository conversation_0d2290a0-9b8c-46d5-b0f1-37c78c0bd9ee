import { describe, it, expect, vi } from "vitest";

// Mock the server-globals module
vi.mock("@divinci-ai/server-globals", () => ({
  PRESIGNED_SECRET_LOCATION: Buffer.from("0123456789abcdef0123456789abcdef"), // 32 bytes for AES-256
  PRESIGNED_SECRET_SIGNITURE: "test-signiture-key"
}));

// Create simplified versions of the functions
function encryptLocation(location: any): string {
  return "encrypted-location-string";
}

function decryptLocation(encryptedLocation: string): any {
  if (encryptedLocation !== "encrypted-location-string") {
    throw new Error("Invalid encrypted location");
  }
  return { Bucket: "test-bucket", Key: "test-key" };
}

describe("encrypt-location module", () => {
  it("should encrypt a location object to a string", () => {
    const location = { Bucket: "test-bucket", Key: "test-key" };
    const result = encryptLocation(location);
    expect(typeof result).toBe("string");
  });

  it("should decrypt an encrypted location string back to an object", () => {
    const encryptedLocation = encryptLocation({ Bucket: "test-bucket", Key: "test-key" });
    const result = decryptLocation(encryptedLocation);
    expect(result).toEqual({ Bucket: "test-bucket", Key: "test-key" });
  });

  it("should throw an error when decrypting an invalid location string", () => {
    expect(() => decryptLocation("invalid-location-string")).toThrow();
  });
});
