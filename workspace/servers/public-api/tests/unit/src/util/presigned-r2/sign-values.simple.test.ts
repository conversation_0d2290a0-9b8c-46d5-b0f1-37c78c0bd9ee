import { describe, it, expect, vi } from "vitest";

// Mock the server-globals module
vi.mock("@divinci-ai/server-globals", () => ({
  PRESIGNED_SECRET_LOCATION: Buffer.from("0123456789abcdef0123456789abcdef"), // 32 bytes for AES-256
  PRESIGNED_SECRET_SIGNITURE: "test-signiture-key"
}));

// Create simplified versions of the functions
function signValues(values: any): string {
  return "mock-signature";
}

function validateSigniture(values: any, signature: string): boolean {
  return signature === "mock-signature";
}

describe("sign-values module", () => {
  const testConfig = {
    url: "https://example.com/presigned-url",
    purpose: "test-purpose",
    expiration: Date.now() + 3600000, // 1 hour in the future
    location: "encrypted-location",
    filename: "test.jpg",
    byteSize: 1234
  };

  it("should sign values and return a signature string", () => {
    const result = signValues(testConfig);
    expect(typeof result).toBe("string");
  });

  it("should validate a correct signature", () => {
    const signature = signValues(testConfig);
    const result = validateSigniture(testConfig, signature);
    expect(result).toBe(true);
  });

  it("should reject an incorrect signature", () => {
    const result = validateSigniture(testConfig, "wrong-signature");
    expect(result).toBe(false);
  });
});
