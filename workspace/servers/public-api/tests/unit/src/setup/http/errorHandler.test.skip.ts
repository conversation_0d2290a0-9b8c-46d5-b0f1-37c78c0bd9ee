import { errorHandler } from "../../../../../src/setup/http";

import { Request, Response, NextFunction } from "express";

jest.spyOn(console, "log").mockImplementation(()=>{});

describe("errorHandler", ()=>{
  let res: Partial<Response>;
  let req: Partial<Request>;
  let next: NextFunction;

  beforeEach(()=>{
    req = {
      originalUrl: "/test",
      method: "GET"
    };
    res = {
      statusCode: 200,
      json: jest.fn().mockReturnThis()
    };
    next = jest.fn() as NextFunction;
  });

  test("should handle non-object error", ()=>{
    errorHandler("Test Error", req as Request, res as Response, next);
    expect(res.statusCode).toBe(500);
    expect(res.json).toHaveBeenCalledWith({
      status: "error",
      message: "non-object error",
      context: {},
    });
  });

  test("should handle object error with status and message", ()=>{
    const err = { status: 404, message: "Not Found" };
    errorHandler(err, req as Request, res as Response, next);

    expect(res.statusCode).toBe(404);
    expect(res.json).toHaveBeenCalledWith({
      status: "error",
      message: "Not Found",
      context: {},
    });
  });

  test("should handle object error without status and message", ()=>{
    const err = { value: "Not Found value" };
    errorHandler(err, req as Request, res as Response, next);

    expect(res.statusCode).toBe(500);
    expect(res.json).toHaveBeenCalledWith({
      status: "error",
      message: "Unknown error",
      context: {},
    });
  });

  test("should handle object error with stack and context", ()=>{
    const err = { status: 500, message: "Internal Error", context: { user: "John Doe" }, stack: "Error at line 1" };
    errorHandler(err, req as Request, res as Response, next);

    expect(res.statusCode).toBe(500);
    expect(res.json).toHaveBeenCalledWith({
      status: "error",
      message: "Internal Error",
      context: { user: "John Doe" },
    });
  });
});
