import { describe, it, expect, beforeEach, vi } from "vitest";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { Request, Response, NextFunction } from "express";

// Create a simplified version of the notFound middleware based on the implementation
const notFoundMiddleware = (req: Request, res: Response, next: NextFunction)=>{
  next(HTTP_ERRORS.NOT_FOUND);
};

describe("Not Found Handler", ()=>{
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: ReturnType<typeof vi.fn>;

  beforeEach(()=>{
    req = {};
    res = { send: vi.fn(), status: vi.fn() };
    next = vi.fn();
  });

  it("should call next with NOT_FOUND error", ()=>{
    notFoundMiddleware(req as Request, res as Response, next);
    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });
});