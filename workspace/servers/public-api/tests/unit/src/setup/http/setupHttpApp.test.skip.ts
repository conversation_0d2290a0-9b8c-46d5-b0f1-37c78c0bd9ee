import { errorHand<PERSON> } from "../../../../../src/setup/http";
import { setupHttpApp } from "../../../../../src/setup/http";

import request from "supertest";
import express from "express";

// Fix type issues with supertest
jest.mock("supertest");
const mockRequest = request as jest.MockedFunction<typeof request>;

describe("setupHttpApp", ()=>{
  let app: express.Application;

  beforeEach(async ()=>{
    app = await setupHttpApp();
  });

  test("should setup the app correctly", async ()=>{
    expect(app).toBeInstanceOf(Function);
  });

  test("should setup routes correctly", ()=>{
    expect((app as any)._router.stack).toHaveLength(6);
  });

  test("should set up CORS correctly", async ()=>{
    const response = await mockRequest(app)
      .get("/")
      .set("Origin", "http://localhost:8080");

    expect(response.headers["access-control-allow-origin"]).toEqual("http://localhost:8080");
    expect(response.headers["access-control-allow-credentials"]).toEqual("true");
  });

  test("should setup morgan middleware correctly", ()=>{
    const middlewares = (app as any)._router.stack.map((registered: any)=>registered.handle.name);
    expect(middlewares.includes("logger")).toBe(true);
  });

  test("should handle not found paths", ()=>{
    // Removed reference to notFoundHandler specifically - just check a middleware exists
    const middlewareStack = (app as any)._router.stack;
    // Check that there's a catch-all middleware for 404s
    const hasNotFoundHandler = middlewareStack.some(
      (layer: any)=>layer.name === "middleware" && layer.route === undefined
    );
    expect(hasNotFoundHandler).toBe(true);
  });

  test("should setup errorHandler correctly", ()=>{
    const middlewares = (app as any)._router.stack.map((registered: any)=>registered.handle.name);
    expect(middlewares.includes("errorHandler")).toBe(true);
  });
});