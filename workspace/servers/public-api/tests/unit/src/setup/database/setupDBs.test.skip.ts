import { setupDBs } from "../../../../../src/setup/database";
import { getMongoose, getRedis } from "@divinci-ai/server-globals";

// Explicitly type the mocks
jest.mock("@divinci-ai/server-globals", () => ({
  getMongoose: jest.fn(),
  getRedis: jest.fn()
}));

describe("setupDBs", () => {
  beforeEach(() => {
    jest.resetAllMocks();
  });

  test("should successfully connect to MongoDB and Redis", async () => {
    const mockMongooseAsPromise = jest.fn().mockResolvedValue(true);
    const mockRedisConnect = jest.fn().mockResolvedValue(true);

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: {},
      mongooseConnection: { asPromise: mockMongooseAsPromise },
    });

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: { connect: mockRedisConnect },
      chatRedisClient: { connect: mockRedisConnect },
    });

    await setupDBs();

    expect(mockMongooseAsPromise).toHaveBeenCalledTimes(1);
    expect(mockRedisConnect).toHaveBeenCalledTimes(2);
  });

  test("should handle failed connection to MongoDB", async () => {
    const mockMongooseAsPromise = jest.fn().mockRejectedValue("Error");
    const mockRedisConnect = jest.fn().mockResolvedValue(true);

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: {},
      mongooseConnection: { asPromise: mockMongooseAsPromise },
    });

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: { connect: mockRedisConnect },
      chatRedisClient: { connect: mockRedisConnect },
    });

    console.error = jest.fn();

    await setupDBs();

    expect(console.error).toHaveBeenCalledWith("❌ Failed to connect to MongoDB: \n", "Error");
  });

  test("should handle failed connection to Redis", async () => {
    const mockMongooseAsPromise = jest.fn().mockResolvedValue(true);
    const mockRedisConnect = jest.fn().mockRejectedValue("Error");

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: {},
      mongooseConnection: { asPromise: mockMongooseAsPromise },
    });

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: { connect: mockRedisConnect },
      chatRedisClient: { connect: mockRedisConnect },
    });

    console.error = jest.fn();

    await setupDBs();

    expect(console.error).toHaveBeenCalledWith("❌ Failed to connect to Redis: \n", "Error");
  });
});
