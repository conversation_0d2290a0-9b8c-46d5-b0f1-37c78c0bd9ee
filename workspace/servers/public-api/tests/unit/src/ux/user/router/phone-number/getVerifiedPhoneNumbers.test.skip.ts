import { getVerifiedPhoneNumbers } from '../../../../../../../src/ux/user/router/phone-number';

import * as request from 'supertest';
import { Types } from 'mongoose';
import { Server } from 'http';

let server: Server;
let savedPhoneNumbers;

describe('getVerifiedPhoneNumbers', () => {
  beforeAll(() => {
    server = Server.create();
  });

  beforeEach(async() => {
    savedPhoneNumbers = [
      { _id: new Types.ObjectId(), phoneNumber: '1234567890', subscription: null, ownerUser: "user1" },
      { _id: new Types.ObjectId(), phoneNumber: '0987654321', subscription: 'abc', ownerUser: "user2" },
    ];
    await savedPhoneNumbers.forEach(async phoneNumber => await PhoneNumberModel.create(phoneNumber));
  });

  afterEach(async () => {
    await PhoneNumberModel.deleteMany({});
  });

  afterAll(async () => {
    await server.destroy();
  });

  it('should return all verified phone numbers for a user', async () => {
    const req = { user: "user1" };

    await request(server)
      .get('/')
      .set('user', req.user)
      .expect(200)
      .then(response => {
        expect(response.body).toMatchObject([{
          _id: expect.any(String),
          phoneNumber: '1234567890',
          subscription: null
        }]);
      });
  });

  it('should return empty array if no verified phone numbers found for a user', async () => {
    const req = { user: "user3" };

    await request(server)
      .get('/')
      .set('user', req.user)
      .expect(200)
      .then(response => {
        expect(response.body).toEqual([]);
      });
  });

  it('should unravel subscription id if present', async () => {
    const req = { user: "user2" };

    await request(server)
      .get('/')
      .set('user', req.user)
      .expect(200)
      .then(response => {
        expect(response.body).toMatchObject([{
          _id: expect.any(String),
          phoneNumber: '0987654321',
          subscription: 'abc'
        }]);
      });
  });
});