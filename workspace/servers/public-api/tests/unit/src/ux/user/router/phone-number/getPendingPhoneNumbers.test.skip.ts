import { getPendingPhoneNumbers } from "../../../../../../../src/ux/user/router/phone-number";

import { PhoneNumberVerificationModel } from "@divinci-ai/server-models";
import { getUserById } from "@divinci-ai/server-globals";

jest.mock("@divinci-ai/server-globals", ()=>({
  getUserById: jest.fn()
}));

jest.mock("@divinci-ai/server-models", ()=>({
  PhoneNumberVerificationModel: {
    find: jest.fn()
  }
}));

const mockRequest = ()=>{
  const req = {};
  req.body = jest.fn().mockReturnValue(req);
  req.params = jest.fn().mockReturnValue(req);
  return req;
};

const mockResponse = ()=>{
  const res = {};
  res.send = jest.fn().mockReturnValue(res);
  res.statusCode = jest.fn().mockReturnValue(res);
  return res;
};

const mockNext = jest.fn();

PhoneNumberVerificationModel.find.mockImplementation(()=>({
  exec: jest.fn().mockResolvedValue([{ verification: "test" }])
}));

describe("Tests for getPendingPhoneNumbers", ()=>{
  test("return 200 and send verifications", async ()=>{
    getUserById.mockReturnValue("123");
    const req = mockRequest();
    const res = mockResponse();
    await getPendingPhoneNumbers(req, res, mockNext);
    expect(res.statusCode).toHaveBeenCalledWith(200);
    expect(res.send).toHaveBeenCalledWith([{ verification: "test" }]);
  });

  test("return error and call next with error", async ()=>{
    PhoneNumberVerificationModel.find.mockImplementationOnce(()=>{
      throw new Error("Test Error");
    });
    getUserById.mockReturnValue("123");
    const req = mockRequest();
    const res = mockResponse();
    await getPendingPhoneNumbers(req, res, mockNext);
    expect(mockNext).toHaveBeenCalledWith(new Error("Test Error"));
  });
});