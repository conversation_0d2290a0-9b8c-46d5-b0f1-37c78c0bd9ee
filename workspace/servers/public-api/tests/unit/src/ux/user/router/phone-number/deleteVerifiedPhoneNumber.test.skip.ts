import { deleteVerifiedPhoneNumber } from '../../../../../../../src/ux/user/router/phone-number';

const { Types } = require('mongoose');
const httpMocks = require('node-mocks-http');
const { auth0http } = require('@divinci-ai/server-globals');
const { PhoneNumberModel } = require('@divinci-ai/server-models');
const { HTTP_ERRORS } = require('@divinci-ai/server-utils');

jest.mock('@divinci-ai/server-globals');
jest.mock('@divinci-ai/server-models');

describe('deleteVerifiedPhoneNumber', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should delete a phone number and return it', async () => {
    const phoneNumberMock = {
      ownerUser: 'userIdMock',
      _id: Types.ObjectId()
    };

    const req = httpMocks.createRequest({
      method: 'DELETE',
      url: '/user/phone-number',
      params: {
        phoneId: phoneNumberMock._id
      }
    });

    const res = httpMocks.createResponse();

    const next = jest.fn();

    PhoneNumberModel.findOneAndRemove = jest.fn().mockReturnValue(Promise.resolve(phoneNumberMock));
    auth0http.getUserId = jest.fn().mockReturnValue(phoneNumberMock.ownerUser);
    
    await deleteVerifiedPhoneNumber(req, res, next);
    
    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(phoneNumberMock);
  });

  it('should throw NOT_FOUND error if number not found', async () => {
    const phoneNumberMock = {
      ownerUser: 'userIdMock',
      _id: Types.ObjectId()
    };

    const req = httpMocks.createRequest({
      method: 'DELETE',
      url: '/user/phone-number',
      params: {
        phoneId: phoneNumberMock._id
      }
    });

    const res = httpMocks.createResponse();

    const next = jest.fn();

    PhoneNumberModel.findOneAndRemove = jest.fn().mockReturnValue(Promise.resolve(null));
    auth0http.getUserId = jest.fn().mockReturnValue(phoneNumberMock.ownerUser);
    
    await deleteVerifiedPhoneNumber(req, res, next);
    
    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });
});