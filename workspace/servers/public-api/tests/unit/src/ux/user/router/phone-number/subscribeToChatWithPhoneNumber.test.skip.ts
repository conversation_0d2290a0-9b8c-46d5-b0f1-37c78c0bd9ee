import { subscribeToChatWithPhoneNumber } from "../../../../../../../src/ux/user/router/phone-number";

import { Types } from "mongoose";
import { RequestHandler } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { ChatModel, PhoneNumberModel } from "@divinci-ai/server-models";

jest.mock("mongoose", ()=>({
  Types: {
    ObjectId: jest.fn().mockReturnValue("mockedId")
  }
}));

jest.mock("@divinci-ai/server-models", ()=>({
  PhoneNumberModel: {
    findOne: jest.fn(),
  },
  ChatModel: {
    findOne: jest.fn(),
  },
}));

jest.mock("@divinci-ai/server-utils", ()=>({
  HTTP_ERRORS: {
    NOT_FOUND: "Not Found",
    BAD_FORM: "Bad Form",
  },
}));

describe("subscribeToChatWithPhoneNumber", ()=>{
  let req, res, next;
  beforeEach(()=>{
    req = {
      params: {
        phoneId: "1234"
      },
      body: {
        chatId: "5678"
      }
    };
    res = {
      statusCode: null,
      send: jest.fn()
    };
    next = jest.fn();
  });

  it("should subscribe to chat with phone number", async ()=>{
    PhoneNumberModel.findOne.mockResolvedValue({
      save: jest.fn(),
    });
    ChatModel.findOne.mockResolvedValue({});

    await subscribeToChatWithPhoneNumber(req, res, next);

    expect(res.statusCode).toBe(200);
    expect(res.send).toHaveBeenCalledWith({ status: "ok" });
  });

  it("should throw an error if phone number not found", async ()=>{
    PhoneNumberModel.findOne.mockResolvedValueOnce(null);
    await subscribeToChatWithPhoneNumber(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it("should throw an error if chat not found", async ()=>{
    PhoneNumberModel.findOne.mockResolvedValueOnce({
      save: jest.fn(),
    });
    ChatModel.findOne.mockResolvedValueOnce(null);

    await subscribeToChatWithPhoneNumber(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it("should throw an error if chatId equals none", async ()=>{
    req.body.chatId = "none";
    PhoneNumberModel.findOne.mockResolvedValue(null);

    await subscribeToChatWithPhoneNumber(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

});