import { submitPhoneNumber } from "../../../../../../../src/ux/user/router/phone-number";

import { Request } from "express";
import { getUserById, getMongoose } from "@divinci-ai/server-globals";
import { PhoneNumberVerificationModel } from "@divinci-ai/server-models";
// Using ESLint-compliant import with type annotation to handle missing types
// @ts-expect-error - Missing type declarations for node-mocks-http
import * as httpMocks from "node-mocks-http";

jest.mock("@divinci-ai/server-globals");
jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/utils", ()=>({
  randomNumbers: jest.fn(()=>"12345678")
}));

// Get mocked mongoose
const { mongoose } = getMongoose();

describe("submitPhoneNumber", ()=>{

  let req: Partial<Request>;
  let res: any; // httpMocks response has special methods that aren't in Express Response
  let next: jest.Mock;

  beforeEach(()=>{
    req = httpMocks.createRequest();
    res = httpMocks.createResponse();
    next = jest.fn();
  });

  afterEach(()=>{
    jest.clearAllMocks();
  });

  it("should return 200 and verification code for valid requests", async ()=>{
    // Fix ObjectId call to use new
    (getUserById as jest.Mock).mockReturnValue(new mongoose.Types.ObjectId());
    (PhoneNumberVerificationModel.findOneAndUpdate as jest.Mock).mockReturnValue({ exec: jest.fn() });
    req.body = {
      phonenumber: "1234567890"
    };

    await submitPhoneNumber(req as Request, res, next);

    expect(res._isEndCalled()).toBeTruthy();
    expect(res._getStatusCode()).toBe(200);
    expect(res._getData()).toEqual({
      status: "ok",
      verficationCode: "12345678"
    });
  });

  it("should call the next middleware when an error occurred", async ()=>{
    const errorMessage = "Error throwing";
    (getUserById as jest.Mock).mockImplementation(()=>{ throw new Error(errorMessage); });

    await submitPhoneNumber(req as Request, res, next);

    expect(next).toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith(new Error(errorMessage));
  });

  it("should throw error when phonenumber field is missing", async ()=>{
    (getUserById as jest.Mock).mockReturnValue(new mongoose.Types.ObjectId());

    await submitPhoneNumber(req as Request, res, next);

    expect(next).toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith(expect.any(Error));
  });

});