import { deletePendingPhoneNumber } from '../../../../../../../src/ux/user/router/phone-number';

import { Request, Response, NextFunction } from 'express';
import { Types } from "mongoose";

jest.mock('@divinci-ai/server-globals', () => ({
  auth0http: {
    getUserId: jest.fn(),
  },
}));

jest.mock('@divinci-ai/server-models', () => ({
  PhoneNumberVerificationModel: {
    findOneAndRemove: jest.fn(),
  },
}));

jest.mock('@divinci-ai/server-utils', () => ({
  getParam: jest.fn(),
  HTTP_ERRORS: {
    NOT_FOUND: 'Not found',
  },
}));

const { getUserId } = require('@divinci-ai/server-globals').auth0http;
const { findOneAndRemove } = require('@divinci-ai/server-models').PhoneNumberVerificationModel;
const { getParam, HTTP_ERRORS } = require('@divinci-ai/server-utils');

describe('deletePendingPhoneNumber', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: NextFunction;
  let verificationResult;

  beforeEach(() => {
    req = {};
    res = {
      statusCode: null,
      send: jest.fn(),
    };
    next = jest.fn();
    verificationResult = { _id: 'phoneId' };
  });

  it('should handle success', async () => {
    getUserId.mockReturnValue('user_id');
    getParam.mockReturnValue('phoneId');
    findOneAndRemove.mockResolvedValue(verificationResult);

    await deletePendingPhoneNumber(req as Request, res as Response, next);

    expect(res.statusCode).toBe(200);
    expect(res.send).toHaveBeenCalledWith(verificationResult);
  });

  it('should handle not found', async () => {
    getUserId.mockReturnValue('user_id');
    getParam.mockReturnValue('phoneId');
    findOneAndRemove.mockResolvedValue(null);

    await deletePendingPhoneNumber(req as Request, res as Response, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('should handle errors', async () => {
    const error = new Error('request failed');
    getUserId.mockImplementation(() => {
      throw error;
    });

    await deletePendingPhoneNumber(req as Request, res as Response, next);

    expect(next).toHaveBeenCalledWith(error);
  });
});