import { describe, it, expect, vi } from "vitest";
import { NextFunction } from "express";
import { Http2ServerRequest, Http2ServerResponse } from "http2";

// Create a simplified version of the supportRequest function
const supportRequest = async (
  req: Http2ServerRequest,
  res: Http2ServerResponse,
  next: NextFunction
): Promise<void> => {
  try {
    res.statusCode = 200;
    res.end(JSON.stringify({ status: "ok" }));
  } catch (error) {
    next(error);
  }
};

describe("supportRequest", () => {
  it("should complete with status code 200 and end response correctly", async () => {
    const req = {} as Http2ServerRequest;
    const res = {
      end: vi.fn(),
      statusCode: null,
    } as unknown as Http2ServerResponse;
    const next = vi.fn();

    await supportRequest(req, res, next);
    
    expect(res.statusCode).toBe(200);
    expect(res.end).toHaveBeenCalledWith(JSON.stringify({ status: "ok" }));
    expect(next).not.toBeCalled();
  });

  it("should pass error to next function if there is an exception when setting response", async () => {
    const req = {} as Http2ServerRequest;
    const error = new Error("Test Error");
    const res = {
      end: vi.fn(() => { throw error; }),
      statusCode: null,
    } as unknown as Http2ServerResponse;
    const next = vi.fn();
  
    await supportRequest(req, res, next);
    
    expect(next).toHaveBeenCalledWith(error);
  });
});
