import { clientError } from '../../../../../../../src/ux/issue/router/client-error';

import { NextHandleFunction } from 'connect';

describe('clientError', () => {
  test('calls res.end with correct values', async () => {
    const mockRes = { statusCode: 500, end: jest.fn() };
    const mockReq = {};
    const mockNext = jest.fn();

    await clientError(mockReq, mockRes, mockNext);

    expect(mockRes.statusCode).toBe(200);
    expect(mockRes.end).toHaveBeenCalledWith({ status: "ok" });
  });

  test('calls next function when there is an error', async () => {
    const error = new Error("error");
    const mockRes = { 
      statusCode: 500, 
      end: jest.fn(() => {
        throw error;
      }) 
    };
    const mockReq = {};
    const mockNext = jest.fn();

    await clientError(mockReq, mockRes, mockNext);

    expect(mockNext).toHaveBeenCalledWith(error);
  });
});