import { getSavedChats } from '../../../../../../../src/ux/ai-chat/router/chat-share';

const express = require('express');

jest.mock('express');
jest.mock('mongoose');
jest.mock('@divinci-ai/server-globals');
jest.mock('@divinci-ai/server-models');
jest.mock('@divinci-ai/models');
jest.mock('@divinci-ai/server-utils');
jest.mock('@divinci-ai/utils');

describe('Testing getSavedChats Function', () => {

  it('Should handle successful case', async () => {
    const req = { headers: { cookie: 'abc' } };
    const res = express.response;
    const next = jest.fn();
    
    require('@divinci-ai/server-globals').auth0http.getUserId.mockReturnValue('user-123');
    require('@divinci-ai/server-models').SavedChatsModel.findOne.mockResolvedValue({ chats: [] });

    res.json = jest.fn().mockReturnValue(res);
    res.status = jest.fn().mockReturnValue(res);

    await getSavedChats(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([]);
  });

  it('Should handle error case', async () => {
    const req = { headers: { cookie: 'abc' } };
    const res = express.response;
    const next = jest.fn();
    
    require('@divinci-ai/server-globals').auth0http.getUserId.mockReturnValue('user-123');
    require('@divinci-ai/server-models').SavedChatsModel.findOne.mockRejectedValue(new Error('Test Error'));

    await getSavedChats(req, res, next);

    expect(next).toHaveBeenCalledWith(new Error('Test Error'));
  });

  it('Should handle savedChats !== null case', async () => {
    const req = { headers: { cookie: 'abc' } };
    const res = express.response;
    const next = jest.fn();
    
    require('@divinci-ai/server-globals').auth0http.getUserId.mockReturnValue('user-123');
    require('@divinci-ai/server-models').SavedChatsModel.findOne.mockResolvedValue({ chats: [{ id: '123' }] });
    require('@divinci-ai/server-models').ChatModel.find.mockResolvedValue([{
      _id: '123',
      title: 'testTitle',
      ownerUser: 'user-123'
    }]);
    require('@divinci-ai/server-models').getMaxRatingOfEachTarget.mockResolvedValue([4]);

    res.json = jest.fn().mockReturnValue(res);
    res.status = jest.fn().mockReturnValue(res);

    await getSavedChats(req, res, next);

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith([{
      chat: {
        _id: '123',
        title: 'testTitle',
        ownerUser: 'user-123'
      },
      rating: 4
    }]);
  });
});