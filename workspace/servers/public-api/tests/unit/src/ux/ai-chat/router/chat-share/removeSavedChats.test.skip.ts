import { removeSavedChats } from '../../../../../../../src/ux/ai-chat/router/chat-share';

describe('removeSavedChats', () => {
  test('should remove the specified chat', async () => {
    const req = { user: { _id: 'userid' }, body: { chatId: 'chatid' } };
    const res = { json: jest.fn(), statusCode: null };
    const next = jest.fn();

    await removeSavedChats(req, res, next);
    
    expect(res.json).toHaveBeenCalled();
  });

  test('should handle invalid chatId', async () => {
    const req = { user: { _id: 'userid' }, body: { chatId: 12345 } };
    const res = { json: jest.fn(), statusCode: null };
    const next = jest.fn();

    await removeSavedChats(req, res, next);
    
    expect(next).toHaveBeenCalled();
  });
  
  test('should return an empty array if no chats saved', async () => {
    const req = { user: { _id: 'userid' }, body: { chatid: 'string' } };
    const res = { json: jest.fn(), statusCode: null };
    const next = jest.fn();

    await removeSavedChats(req, res, next);

    expect(res.json).toHaveBeenCalledWith([]);
    expect(res.statusCode).toBe(200);
  });

  test('should return 200 status and updated chat list after successful deletion', async () => {
    const req = { user: { _id: 'userid' }, body: { chatId: 'chatid' } };
    const res = { json: jest.fn(), statusCode: null };
    const next = jest.fn();

    await removeSavedChats(req, res, next);

    expect(res.json).toHaveBeenCalled();
    expect(res.statusCode).toBe(200);
  });

  test('should handle server errors', async () => {
    const req = { user: { _id: 'userid' }, body: { } };
    const res = { json: jest.fn(), statusCode: null };
    const next = jest.fn();

    await removeSavedChats(req, res, next);

    expect(next).toHaveBeenCalled();
  });
});