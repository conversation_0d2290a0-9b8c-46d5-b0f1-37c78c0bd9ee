import { ensureIsPublic } from '../../../../../../../../src/ux/ai-chat/router/chat-permission/middleware';

import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { isChatPublic } from "../../../../../../../../src/ux/ai-chat/router/chat-permission/functions";

jest.mock('../../../../../../../../src/ux/ai-chat/router/chat-permission/functions', () => {
  return {
    isChatPublic: jest.fn(),
  }
});

describe('ensureIsPublic', () => {
  it('should call next if chat is public', async () => {
    const req: any = { params: { chatId: '123' } };
    const res: any = {};
    const next = jest.fn();
    (isChatPublic as jest.MockedFunction<any>).mockResolvedValue(true);

    await ensureIsPublic(req, res, next);

    expect(next).toHaveBeenCalledWith();
  });

  it('should call next with FORBIDDEN error if chat is not public', async () => {
    const req: any = { params: { chatId: '123' } };
    const res: any = {};
    const next = jest.fn();
    (isChatPublic as jest.MockedFunction<any>).mockResolvedValue(false);

    await ensureIsPublic(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  it('should call next with error if there is an error while checking is chat public', async () => {
    const req: any = { params: { chatId: '123' } };
    const res: any = {};
    const next = jest.fn();
    const sampleError = new Error('sample error');

    (isChatPublic as jest.MockedFunction<any>).mockRejectedValue(sampleError);
    await ensureIsPublic(req, res, next);

    expect(next).toHaveBeenCalledWith(sampleError);
  });
});