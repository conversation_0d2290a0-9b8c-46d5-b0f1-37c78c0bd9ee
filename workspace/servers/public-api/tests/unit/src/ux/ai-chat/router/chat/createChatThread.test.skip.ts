import { createChatThread } from '../../../../../../../src/ux/ai-chat/router/chat';

import { RequestHandler, Response, NextFunction } from "express";
import * as express from 'express';
import * as httpMocks from 'node-mocks-http';
import { ChatModel } from "@divinci-ai/server-models";
import { getUserId } from "@divinci-ai/server-utils";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { jsonBody, getParam, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";

jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/server-utils");
jest.mock("@divinci-ai/utils");

describe('Chat Thread tests', () => {
  let request: httpMocks.MockRequest<express.Request>;
  let response: httpMocks.MockResponse<express.Response>;
  let nextFunction: NextFunction = jest.fn();

  beforeEach(() => {
    request = httpMocks.createRequest();
    response = httpMocks.createResponse();
    nextFunction.mockReset();
  });

  afterAll(() => {
    jest.resetAllMocks();
  });

  test("Should respond with 200 when creating valid chat thread", async () => {
    getUserId.mockReturnValue('123');
    jsonBody.mockReturnValue(Promise.resolve({ title: 'test' }));
    ChatModel.findOne.mockReturnValue(Promise.resolve(null));
    ChatModel.create.mockReturnValue(Promise.resolve({}));

    await createChatThread(request, response, nextFunction);

    expect(nextFunction).not.toHaveBeenCalled();
    expect(response.statusCode).toBe(200);
  });

  test("Should call next function with error when body is invalid", async () => {
    getUserId.mockReturnValue('123');
    jsonBody.mockReturnValue(Promise.resolve({}));

    await createChatThread(request, response, nextFunction);

    expect(nextFunction).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
    expect(response.statusCode).not.toBe(200);
  });

  test("Should call next function with error when chat thread already exists", async () => {
    getUserId.mockReturnValue('123');
    jsonBody.mockReturnValue(Promise.resolve({ title: 'test' }));
    ChatModel.findOne.mockReturnValue(Promise.resolve({}));

    await createChatThread(request, response, nextFunction);

    expect(nextFunction).toHaveBeenCalledWith(HTTP_ERRORS.ALREADY_EXISTS);
    expect(response.statusCode).not.toBe(200);
  });
});
