import { setSavedChats } from '../../../../../../../src/ux/ai-chat/router/chat-share';

import { SavedChatsModel, ChatModel } from "@divinci-ai/server-models";
import mongoose from 'mongoose';
import express from 'express';
import httpMocks from 'node-mocks-http';

jest.mock('@divinci-ai/server-globals', () => ({
  auth0http: {
    getUserId: jest.fn().mockReturnValue('testUserId'),
  }
}));

jest.mock("@divinci-ai/server-utils", () => ({
  jsonBody: jest.fn().mockResolvedValue({ chatId: 'testChatId', description: 'testDescription' }),
}));

jest.mock('@divinci-ai/server-models', () => ({
  SavedChatsModel: {
    findOneAndUpdate: jest.fn().mockResolvedValue({
      chats: [
        { id: new mongoose.Types.ObjectId('testChatId') },
      ]
    }),
  },
  ChatModel: {
    exists: jest.fn().mockResolvedValue(true)
  },
}));

jest.mock('mongoose', () => ({
  Types: {
    ObjectId: jest.fn().mockReturnValue('testObjectId')
  }
}));

describe('setSavedChats', () => {
  let req, res, next;

  beforeEach(() => {
    req = httpMocks.createRequest();
    res = httpMocks.createResponse();
    next = jest.fn();
  });

  it('should successfully save a chat', async () => {
    await setSavedChats(req, res, next);
    expect(res.statusCode).toBe(200);
    expect(next).not.toHaveBeenCalled();
  });

  it('should handle errors', async () => {
    const errorMessage = 'Error';
    const rejectedPromise = Promise.reject(errorMessage);
    SavedChatsModel.findOneAndUpdate.mockReturnValueOnce(rejectedPromise);
    
    await setSavedChats(req, res, next);
    
    expect(next).toHaveBeenCalledWith(errorMessage);
  });
});