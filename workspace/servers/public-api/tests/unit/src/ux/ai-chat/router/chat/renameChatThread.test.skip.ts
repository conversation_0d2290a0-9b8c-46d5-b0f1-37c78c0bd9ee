
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { renameChatThread } from "../../../../../../../src/ux/ai-chat/router/chat.ts";
import { serverGlobals, serverModels, serverUtils } from "@divinci-ai/server-globals";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { ObjectId } from "mongoose";
import { createRequest, createResponse } from 'node-mocks-http';
import jestMock from 'jest-mock';

jest.mock('@divinci-ai/server-globals', () => ({
    getUserId: jest.fn(),
    jsonBody: jest.fn(),
    ChatModel: {
        findOne: jest.fn(),
        findOneAndUpdate: jest.fn()
    },
    getParam: jest.fn(),
    HTTP_ERRORS: {
        BAD_FORM: 'bad form',
        ALREADY_EXISTS: 'already exists',
        NOT_FOUND: 'not found'
    }
}));

describe('Testing renameChatThread', () => {

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should throw BAD_FORM when title is not a string', async () => {
        const req = createRequest();
        const res = createResponse();
        serverGlobals.jsonBody.mockResolvedValue({ title: 123 });

        try {
            await renameChatThread(req, res, jestMock.fn());
        } catch (e) {
            expect(e).toEqual(HTTP_ERRORS.BAD_FORM);
        }
    });

    it('should throw ALREADY_EXISTS when chat with this title already exists', async () => {
        const req = createRequest();
        const res = createResponse();
        serverGlobals.jsonBody.mockResolvedValue({ title: 'chat' });
        serverGlobals.ChatModel.findOne.mockResolvedValue(true);

        try {
            await renameChatThread(req, res, jestMock.fn());
        } catch (e) {
            expect(e).toEqual(HTTP_ERRORS.ALREADY_EXISTS);
        }
    });

    it('should throw NOT_FOUND when chat not found', async () => {
        const req = createRequest();
        const res = createResponse();
        serverGlobals.jsonBody.mockResolvedValue({ title: 'chat' });
        serverGlobals.ChatModel.findOne.mockResolvedValue(null);

        try {
            await renameChatThread(req, res, jestMock.fn());
        } catch (e) {
            expect(e).toEqual(HTTP_ERRORS.NOT_FOUND);
        }
    });

    it('should rename chat correctly and return updated chat', async () => {
        const req = createRequest();
        const res = createResponse();
        const next = jestMock.fn();
        const updatedChat = {
            title: 'update chat'
        };
        serverGlobals.jsonBody.mockResolvedValue({ title: 'chat' });
        serverGlobals.ChatModel.findOne.mockResolvedValue(false);
        serverGlobals.ChatModel.findOneAndUpdate.mockResolvedValue(updatedChat);

        await renameChatThread(req, res, next);

        expect(res._getData()).toEqual(updatedChat);
        expect(res.statusCode).toBe(200);
        expect(next).not.toBeCalled();
    });

    it('should call next with error on exception', async () => {
        const req = createRequest();
        const res = createResponse();
        const err = new Error('error');
        const next = jestMock.fn();

        serverGlobals.jsonBody.mockRejectedValue(err);

        await renameChatThread(req, res, next);

        expect(next).toBeCalledWith(err);
    });
});
