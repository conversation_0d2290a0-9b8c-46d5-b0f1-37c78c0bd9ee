import { stripFromChatMessage } from '../../../../../../../src/ux/ai-chat/router/chat-message';

import { RequestHandler, Response, NextFunction } from "express";
import { ChatModel } from "@divinci-ai/server-models";
import * as serverUtils from "@divinci-ai/server-utils";
import * as serverGlobals from "@divinci-ai/server-globals";
import * as utils from "@divinci-ai/utils";

jest.mock("@divinci-ai/server-models", () => ({
  ChatModel: {
    findOneAndUpdate: jest.fn(),
  },
}));
jest.mock("@divinci-ai/server-globals", () => ({
  getRedis: jest.fn(),
  auth0http: {
    getUserId: jest.fn(),
  },
}));
jest.mock("@divinci-ai/server-utils", () => ({
  getParam: jest.fn(),
  HTTP_ERRORS: {
    UNAVAILABLE: "Unavailable",
  },
}));
jest.mock("@divinci-ai/utils", () => ({
  castToObject: jest.fn(),
}));

describe("'stripFromChatMessage' function tests", () => {
  let req: Partial<RequestHandler>, res: Partial<Response>, next: NextFunction;
  beforeEach(() => {
    req = {};
    res = { json: jest.fn(), statusCode: 200 };
    next: jest.fn();
  });

  test("Valid behavior, strips message", async () => {
    serverGlobals.auth0http.getUserId.mockReturnValue("user_id");
    serverUtils.getParam.mockReturnValueOnce("chatId");
    serverUtils.getParam.mockReturnValueOnce("messageId");
    ChatModel.findOneAndUpdate.mockReturnValueOnce(Promise.resolve({ stripFromId: () => [] }));
    ChatModel.findOneAndUpdate.mockReturnValueOnce(Promise.resolve({}));

    stripFromChatMessage(req as RequestHandler, res as Response, next);
    await flushPromises();

    expect(res.json).toHaveBeenCalledWith({});
    expect(res.statusCode).toBe(200);
  });

  test("Valid chat is null", async () => {
    serverGlobals.auth0http.getUserId.mockReturnValue("user_id");
    serverUtils.getParam.mockReturnValueOnce("chatId");
    serverUtils.getParam.mockReturnValueOnce("messageId");
    ChatModel.findOneAndUpdate.mockReturnValueOnce(Promise.resolve(null));
    
    try {
      await stripFromChatMessage(req as RequestHandler, res as Response, next);
    } catch (error) {
      expect(error).toBe(serverUtils.HTTP_ERRORS.UNAVAILABLE);
    }
  });

  test("Error in processing", async () => {
    serverGlobals.auth0http.getUserId.mockReturnValue("user_id");
    serverUtils.getParam.mockReturnValueOnce("chatId");
    serverUtils.getParam.mockReturnValueOnce("messageId");
    ChatModel.findOneAndUpdate.mockImplementation(() => {
      throw new Error("Test error");
    });

    try {
      await stripFromChatMessage(req as RequestHandler, res as Response, next);
    } catch (error) {
      expect(error.message).toBe("Test error");
    }
  });
});

const flushPromises = () => new Promise(setImmediate);