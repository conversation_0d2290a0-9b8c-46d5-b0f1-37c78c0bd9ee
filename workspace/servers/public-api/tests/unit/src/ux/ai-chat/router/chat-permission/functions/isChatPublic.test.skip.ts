import { isChatPublic } from '../../../../../../../../src/ux/ai-chat/router/chat-permission/functions';

import { AnonymousRoleTypes, CHAT_ROLES, AI_CHAT_LOCATION } from '@divinci-ai/models';
import { PermissionModel } from '@divinci-ai/server-models';
jest.mock('@divinci-ai/server-models');

const mockFindOne = {
  exec: jest.fn(),
};
PermissionModel.findOne = jest.fn(() => mockFindOne);

describe('isChatPublic function', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should return true if chat is not banned', async () => {
    const chatId = 'chatId';
    const responseData = { anonymousRole: AnonymousRoleTypes.anonymous };
    mockFindOne.exec.mockResolvedValueOnce(responseData);
    const result = await isChatPublic(chatId);
    expect(PermissionModel.findOne).toBeCalledWith({
      target: {
        ...AI_CHAT_LOCATION,
        id: chatId
      }
    });
    expect(result).toBe(true);
  });

  test('should return false if anonymous role equals chat roles banned', async () => {
    const chatId = 'chatId';
    const responseData = { anonymousRole: CHAT_ROLES.BANNED };
    mockFindOne.exec.mockResolvedValueOnce(responseData);
    const result = await isChatPublic(chatId);
    expect(result).toBe(false);
  });

  test('should return true if allowance is null', async () => {
    const chatId = 'chatId';
    mockFindOne.exec.mockResolvedValueOnce(null);
    const result = await isChatPublic(chatId);
    expect(result).toBe(true);
  });

  test('should throw error if PermissionModel.findOne throws error', async () => {
    mockFindOne.exec.mockRejectedValueOnce(new Error('Test error'));
    await expect(isChatPublic('chatId')).rejects.toThrow('Test error');
  });
});