import { getTrendingChats } from '../../../../../../../src/ux/ai-chat/router/chat-rating';

import { ChatModel } from '@divinci-ai/server-models';
import { RequestHandler } from 'express';
import { Types } from 'mongoose';
describe('Get Trending Chats', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  it('should respond with status code 200 and return chats', async () => {
    const req = {};
    const res = {
      statusCode: 400, 
      json: jest.fn().mockImplementation(() => res),
    };
    const next = jest.fn();
    jest.spyOn(ChatModel, 'find').mockResolvedValue([
      {
        _id: Types.ObjectId('1'),
        title: 'TestChat1',
        ownerUser: 'TestUser1',
      },
      {
        _id: Types.ObjectId('2'),
        title: 'TestChat2',
        ownerUser: 'TestUser2',
      },
    ]);
    await getTrendingChats(req, res, next);
    expect(res.statusCode).toEqual(200);
    expect(res.json).toHaveBeenCalled();
  });
  it('should throw error when failing to fetch chats', async () => {
    const req = {};
    const res = {
      statusCode: 400, 
      json: jest.fn(),
    };
    const next = jest.fn();
    jest.spyOn(ChatModel, 'find').mockImplementation(() => {
      throw new Error('Failed to fetch chats');
    });
    await getTrendingChats(req, res, next);
    expect(next).toHaveBeenCalledWith(new Error('Failed to fetch chats'));
  });
});