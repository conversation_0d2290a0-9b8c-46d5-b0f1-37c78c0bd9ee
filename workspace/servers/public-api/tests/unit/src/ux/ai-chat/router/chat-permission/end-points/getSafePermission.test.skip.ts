import { getSafePermission } from '../../../../../../../../src/ux/ai-chat/router/chat-permission/end-points';

import { Request, Response } from 'express';
import { getUsersChatRole, isChatPublic } from "../../../../../../../../src/ux/ai-chat/router/chat-permission/functions";

jest.mock('../../../../../../../../src/ux/ai-chat/router/chat-permission/functions');

describe('getSafePermission', () => {
  
  let req, res, next, spy;

  beforeEach(() => {
    req = {
      params: {
        chatId: '123'
      },
      user: {
        sub: '456'
      }
    };
    res = {
      statusCode: null,
      json: jest.fn()
    };
    next = jest.fn();
    getUsersChatRole.mockResolvedValue('admin');
    isChatPublic.mockResolvedValue(true);
  });

  afterEach(() => {
    getUsersChatRole.mockReset();
    isChatPublic.mockReset();
    if (spy) spy.mockRestore();
  });

  it('responds with HTTP 200 and role, isPublic data', async () => {
    await getSafePermission(req, res, next);
    expect(res.statusCode).toBe(200);
    expect(res.json).toBeCalledWith({ role: 'admin', isPublic: true });
  });

  it('handles errors by calling next with error', async () => {
    const error = new Error('fake error');
    getUsersChatRole.mockRejectedValueOnce(error);
    await getSafePermission(req, res, next);
    expect(next).toBeCalledWith(error);
  });

  it('does not catch errors thrown from res.json', async () => {
    const error = new Error('fake error');
    res.json.mockImplementationOnce(() => { throw error; });
    await expect(getSafePermission(req, res, next)).rejects.toThrow(error);
  });

});