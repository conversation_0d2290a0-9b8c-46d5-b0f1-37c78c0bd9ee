import { getChatRating } from "../../../../../../../src/ux/ai-chat/router/chat-rating";
import * as chatModel from "@divinci-ai/server-models";
import * as serverUtils from "@divinci-ai/server-utils";
import * as serverGlobals from "@divinci-ai/server-globals";
import { Request, Response } from "express";

jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/server-utils");
jest.mock("@divinci-ai/server-globals");

describe("getChatRating tests", ()=>{
  beforeEach(()=>{
    jest.clearAllMocks();
  });

  test("Should respond with 200 and correct rating if chat exists", async ()=>{
    const mockReq = {
      params: { chatId: "1234" },
    };
    const mockRes = {
      statusCode: 500,
      json: jest.fn(),
    };
    const mockNext = jest.fn();

    (chatModel.ChatModel.exists as jest.Mock).mockResolvedValue(true);
    (serverGlobals as any).auth0http = {
      getUserIdOptional: jest.fn().mockReturnValue("user1")
    };
    (serverUtils.getParam as jest.Mock).mockReturnValue("1234");
    (chatModel.getRatingOfTarget as jest.Mock).mockResolvedValue({ rating: 4 });

    await getChatRating(mockReq as Request, mockRes as Response, mockNext);

    expect(mockRes.statusCode).toBe(200);
    expect(mockRes.json).toHaveBeenCalledWith({ rating: 4 });
  });

  test("Should call next function with error if chat does not exist", async ()=>{
    const mockReq = {
      params: { chatId: "1234" },
    };
    const mockRes = {
      statusCode: 500,
      json: jest.fn(),
    };
    const mockNext = jest.fn();

    (chatModel.ChatModel.exists as jest.Mock).mockResolvedValue(false);
    (serverGlobals as any).auth0http = {
      getUserIdOptional: jest.fn().mockReturnValue("user1")
    };
    (serverUtils.getParam as jest.Mock).mockReturnValue("1234");

    await getChatRating(mockReq as Request, mockRes as Response, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockNext.mock.calls[0][0].message).toBe("🔍 Chat doesn't exist.");
  });
});
