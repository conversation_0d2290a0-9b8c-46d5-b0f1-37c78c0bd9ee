
import { setChatRating } from "../../../../../../../src/ux/ai-chat/router/chat-rating.ts";
import { Types } from "mongoose";
import { RequestHandler } from "express";
import { ChatModel, IndividualRatingModel, getRatingOfTarget, setRating, getTrending, getContreversial } from "@divinci-ai/server-models";
import { getUserId, getUserIdOptional, auth0http } from "@divinci-ai/server-globals";
import { castToObject, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, getParam, jsonBody } from "@divinci-ai/server-utils";

jest.mock("@divinci-ai/server-globals");
jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/server-utils");

describe('setChatRating', () => {
    let req, res, next;

    beforeEach(() => {
      req = {
        params: {},
        body: {},
        headers: {},
      };

      res = {
        statusCode: null,
        json: jest.fn(),
      };

      next = jest.fn();
    });

    it('should validate chat existence', async () => {
      getParam.mockReturnValueOnce('invalid_chat_id');
      ChatModel.findById = jest.fn().mockResolvedValue(null);      
      await setChatRating(req, res, next);
      expect(next).toHaveBeenCalledWith(HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND('chat doesn\'t exist'));
    });

    it('should validate message existence in chat', async () => {
      getParam.mockReturnValueOnce('valid_chat_id').mockReturnValueOnce('invalid_message_id');
      ChatModel.findById = jest.fn().mockResolvedValue({ messages: [{ _id: 'valid_message_id' }] });
      await setChatRating(req, res, next);
      expect(next).toHaveBeenCalledWith(HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND('message doesn\'t exist'));
    });

    it('should validate rating value', async () => {
      getParam.mockReturnValueOnce('valid_chat_id').mockReturnValueOnce('valid_message_id');
      ChatModel.findById = jest.fn().mockResolvedValue({ messages: [{ _id: 'valid_message_id' }] });
      jsonBody.mockResolvedValue({ rating: 5 });
      await setChatRating(req, res, next);
      expect(next).toHaveBeenCalledWith(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('rating must be -1, 0 or 1'));
    });

    it('should update rating value and return status 200', async () => {
      getParam.mockReturnValueOnce('valid_chat_id').mockReturnValueOnce('valid_message_id');
      getUserId.mockReturnValueOnce('valid_user_id');
      ChatModel.findById = jest.fn().mockResolvedValue({ messages: [{ _id: 'valid_message_id' }] });
      jsonBody.mockResolvedValue({ rating: 1 });
      setRating.mockResolvedValue({});
      getRatingOfTarget.mockResolvedValue([]);
      await setChatRating(req, res, next);
      expect(res.statusCode).toBe(200);
      expect(res.json).toHaveBeenCalled();
    });
});