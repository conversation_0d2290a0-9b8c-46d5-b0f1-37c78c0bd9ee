import { categorizeText } from "../../../../../../../src/ux/ai-chat/router/chat-categorizing";

import { CHAT_CATEGORY, ChatModel } from "@divinci-ai/server-models";
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { getUserById } from "@divinci-ai/server-globals";
import { Request, Response } from "express";

jest.mock("express", ()=>{
  return {
    Request: jest.fn().mockImplementation(()=>{
      return {
        get: jest.fn().mockReturnThis()
      };
    }),
    Response: jest.fn().mockImplementation(()=>{
      return {
        statusCode: 0,
        json: jest.fn(),
        end: jest.fn()
      };
    }),
  };
});

jest.mock("@divinci-ai/server-models", ()=>{
  return {
    ChatModel: {
      categorizeText: jest.fn()
    },
    CHAT_CATEGORY: {
      text: "text",
      image: "image",
      audio: "audio",
      video: "video",
      unknown: "unknown"
    }
  };
});

jest.mock("@divinci-ai/server-globals", ()=>{
  return {
    getUserById: jest.fn(),
  };
});

jest.mock("@divinci-ai/server-utils", ()=>{
  return {
    HTTP_ERRORS: {
      BAD_FORM: "Bad form",
    },
    jsonBody: jest.fn()
  };
});

describe("categorizeText tests", ()=>{
  test("No user id raises error", async ()=>{
    getUserById.mockReturnValueOnce(undefined);
    const res = new Response();
    const req = new Request();

    await expect(categorizeText(req, res, ()=>{})).rejects.toThrow();
  });

  test("Non-object body raises error", async ()=>{
    getUserById.mockReturnValueOnce("test");
    jest.mock("@divinci-ai/server-utils");
    const res = new Response();
    const req = new Request();

    await expect(categorizeText(req, res, ()=>{})).rejects.toThrow(HTTP_ERRORS.BAD_FORM);
  });

  test("Non-string content raises error", async ()=>{
    getUserById.mockReturnValueOnce("test");
    jest.mock("@divinci-ai/server-utils");
    const res = new Response();
    const req = new Request();

    await expect(categorizeText(req, res, ()=>{})).rejects.toThrow(HTTP_ERRORS.BAD_FORM);
  });

  test("Proper response on text categorization", async ()=>{
    getUserById.mockReturnValueOnce("test");
    jest.mock("@divinci-ai/server-utils");
    const res = new Response();
    const req = new Request();
    ChatModel.categorizeText.mockReturnValueOnce({
      category: CHAT_CATEGORY.text,
      confidence: 0.95
    });

    await expect(categorizeText(req, res, ()=>{})).resolves.toBeUndefined();
    await expect(res.statusCode).toEqual(200);
  });
});
