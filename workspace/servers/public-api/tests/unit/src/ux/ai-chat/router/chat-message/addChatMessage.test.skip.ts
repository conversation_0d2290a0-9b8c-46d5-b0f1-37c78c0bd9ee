
const {addChatMessage} = require("../../../../../../../src/ux/ai-chat/router/chat-message.ts");
const {ChatModel} = require("@divinci-ai/server-models");
const {auth0http, getRedis} = require("@divinci-ai/server-globals");
const {castToObject, jsonBody, getParam, HTTP_ERRORS} = require("@divinci-ai/server-utils");
const httpMocks = require("node-mocks-http");

jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/server-globals");
jest.mock("@divinci-ai/server-utils");

describe("addChatMessage", () => {
  let req, res, next, chatMethodAddChatMessage;

  beforeEach(() => {
    req = httpMocks.createRequest();
    res = httpMocks.createResponse();
    next = jest.fn();

    chatMethodAddChatMessage = jest.fn();
    ChatModel.addChatMessage = chatMethodAddChatMessage;

    auth0http.getUserId.mockReturnValue("user_id");
    getRedis.chatRedisClient = { chatRedisClient: jest.fn() };

    getParam.mockReturnValue("chatId");
    jsonBody.mockReturnValue(Promise.resolve({ content: "content", category: "category" }));
    castToObject.mockReturnValue({ content: "content", category: "category" });
  });

  it("addChatMessage calls next with HTTP_ERRORS.BAD_FORM if json.content is not of type string", async () => {
    jsonBody.mockReturnValueOnce(Promise.resolve({ content: 123, category: "category" }));

    await addChatMessage(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
  });

  it("addChatMessage calls next with HTTP_ERRORS.BAD_FORM if json.category is not of type string", async () => {
    jsonBody.mockReturnValueOnce(Promise.resolve({content: "content", category: 123 }));

    await addChatMessage(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
  });

  it("addChatMessage should call chatMethodAddChatMessage with proper arguments", async () => {
    await addChatMessage(req, res, next);

    expect(chatMethodAddChatMessage).toHaveBeenCalledWith({
      category: "category",
      user_id: "user_id",
      chatId: "chatId",
      content: "content",
      replyTo: undefined
    });
  });

  it("addChatMessage should send response with status 200 and correct body", async () => {
    await addChatMessage(req, res, next);

    expect(res.statusCode).toBe(200);
    expect(res._getData()).toEqual({ status: "ok" });
  });

  it("addChatMessage should catch the error and call next with it", async () => {
    const error = new Error("Error");
    chatMethodAddChatMessage.mockReturnValueOnce(Promise.reject(error));

    await addChatMessage(req, res, next);

    expect(next).toHaveBeenCalledWith(error);
  });
});
