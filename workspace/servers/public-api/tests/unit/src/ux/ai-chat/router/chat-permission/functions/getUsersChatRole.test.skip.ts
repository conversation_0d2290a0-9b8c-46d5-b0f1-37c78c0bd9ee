import { getUsersChatRole } from '../../../../../../../src/ux/ai-chat/router/chat-permission/functions';

const { ChatModel, PermissionModel } = require("@divinci-ai/server-models");
const { getUserById } = require("@divinci-ai/server-globals").auth0api;
const { DEFAULT_ALLOWANCE, CHAT_ROLES_TYPE } = require("@divinci-ai/models");

jest.mock("@divinci-ai/server-models", () => ({
    ChatModel: { exists: jest.fn() },
    PermissionModel: { getRoleOfUser: jest.fn() }
}));

jest.mock("@divinci-ai/server-globals", () => ({
    auth0api: { getUserById: jest.fn() }
}));

jest.mock("@divinci-ai/models", () => ({
    DEFAULT_ALLOWANCE: { ownerRole: 'Owner' },
    CHAT_ROLES_TYPE: jest.fn()
}));

describe('getUsersChatRole', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('returns chat owner role if userId is owner', async () => {
        const userId = 'someUserId';
        const chatId = 'someChatId';

        getUserById.mockResolvedValue({ email: 'someEmail' });
        ChatModel.exists.mockResolvedValue(true);
        const role = await getUsersChatRole(userId, chatId);
        expect(role).toBe(DEFAULT_ALLOWANCE.ownerRole);
    });

    it('returns the user role if user is not owner', async () => {
        const userId = 'someUserId';
        const chatId = 'someChatId';
        const mockRole = 'someRole';

        getUserById.mockResolvedValue({ email: 'someEmail' });
        ChatModel.exists.mockResolvedValue(false);
        PermissionModel.getRoleOfUser.mockResolvedValue(mockRole);

        const role = await getUsersChatRole(userId, chatId);
        expect(role).toBe(mockRole);
    });

    it('throws error if getUserById throws error', async () => {
        const userId = 'someUserId';
        const chatId = 'someChatId';
        const mockError = new Error('Error getting user');

        getUserById.mockRejectedValue(mockError);

        await expect(getUsersChatRole(userId, chatId)).rejects.toThrow(mockError);
    });

    it('does not call ChatModel.exists and PermissionModel.getRoleOfUser if userId is null', async () => {
        const userId = null;
        const chatId = 'someChatId';

        await getUsersChatRole(userId, chatId);

        expect(ChatModel.exists).not.toHaveBeenCalled();
        expect(PermissionModel.getRoleOfUser).not.toHaveBeenCalled();
    });
});