import { trendingTargetsToChatRatings } from "../../../../../../../src/ux/ai-chat/router/chat-rating";
import { getContreversialChats } from "../../../../../../../src/ux/ai-chat/router/chat-rating";

import { RequestHandler } from "express";
import { ChatModel, IndividualRatingModel, getRatingOfTarget, setRating, getTrending, getContreversial } from "@divinci-ai/server-models";
import { RatedSubTarget, condenseTarget, unravelTarget, AI_CHAT_LOCATION } from "@divinci-ai/models";
import { auth0http } from "@divinci-ai/server-globals";
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, getParam, jsonBody } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";

jest.mock("@divinci-ai/server-models");
jest.mock("mongoose");

// Set up the mocks more explicitly
const mockGetContreversial = getContreversial as jest.MockedFunction<typeof getContreversial>;
const mockChatModel = ChatModel as jest.Mocked<typeof ChatModel>;

describe("getContreversialChats", ()=>{
  let req, res, next;

  beforeEach(()=>{
    req = { headers: { authorization: "Bearer token" } };
    res = { statusCode: null, json: jest.fn() };
    next = jest.fn();
    auth0http.getUserIdOptional = jest.fn().mockReturnValue("userId");

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it("should handle error", async ()=>{
    mockGetContreversial.mockRejectedValue("error");
    await getContreversialChats(req, res, next);
    expect(next).toHaveBeenCalledWith("error");
  });

  it("should return chat ratings", async ()=>{
    mockGetContreversial.mockResolvedValue([
      { parent: condenseTarget({ database: AI_CHAT_LOCATION.database, model: AI_CHAT_LOCATION.model, id: "chatId1" }), up: 5, down: 2 },
      { parent: condenseTarget({ database: AI_CHAT_LOCATION.database, model: AI_CHAT_LOCATION.model, id: "chatId2" }), up: 10, down: 8 },
    ]);

    mockChatModel.find.mockResolvedValue([
      { _id: "chatId1", title: "chat1", ownerUser: "user1" },
      { _id: "chatId2", title: "chat2", ownerUser: "user2" },
    ]);

    await getContreversialChats(req, res, next);
    expect(res.statusCode).toEqual(200);
    expect(res.json).toHaveBeenCalledWith([
      { chat: { _id: "chatId1", title: "chat1", ownerUser: "user1" }, rating: { up: 5, down: 2 } },
      { chat: { _id: "chatId2", title: "chat2", ownerUser: "user2" }, rating: { up: 10, down: 8 } },
    ]);
  });
});

describe("trendingTargetsToChatRatings", ()=>{
  beforeEach(()=>{
    jest.clearAllMocks();

    // Set up mock for ChatModel.find for the trendingTargetsToChatRatings tests
    mockChatModel.find.mockResolvedValue([
      { _id: "chatId1", title: "chat1", ownerUser: "user1" },
      { _id: "chatId2", title: "chat2", ownerUser: "user2" },
    ]);
  });

  it("should return chat ratings", async ()=>{
    const results = await trendingTargetsToChatRatings("userId", [
      { parent: condenseTarget({ database: AI_CHAT_LOCATION.database, model: AI_CHAT_LOCATION.model, id: "chatId1" }), up: 5, down: 2 },
      { parent: condenseTarget({ database: AI_CHAT_LOCATION.database, model: AI_CHAT_LOCATION.model, id: "chatId2" }), up: 10, down: 8 },
    ]);

    expect(ChatModel.find).toHaveBeenCalledWith({ _id: { $in: [new Types.ObjectId("chatId1"), new Types.ObjectId("chatId2")] } });
    expect(results).toEqual([
      { chat: { _id: "chatId1", title: "chat1", ownerUser: "user1" }, rating: { up: 5, down: 2 } },
      { chat: { _id: "chatId2", title: "chat2", ownerUser: "user2" }, rating: { up: 10, down: 8 } },
    ]);
  });
});
