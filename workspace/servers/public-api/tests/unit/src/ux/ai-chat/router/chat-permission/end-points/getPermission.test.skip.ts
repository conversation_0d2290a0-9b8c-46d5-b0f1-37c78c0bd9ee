import { getPermission } from '../../../../../../../../src/ux/ai-chat/router/chat-permission/end-points';

const { PermissionModel } = require('@divinci-ai/server-models');
const { getParam } = require('@divinci-ai/server-utils');
const { AI_CHAT_LOCATION } = require('@divinci-ai/models');
const mongoose = require('mongoose');
const httpMocks = require('node-mocks-http');

jest.mock('@divinci-ai/server-models', () => ({
  PermissionModel: { findOne: jest.fn() }
}));

jest.mock('@divinci-ai/server-utils', () => ({
  getParam: jest.fn()
}));

jest.mock('@divinci-ai/models', () => ({
  AI_CHAT_LOCATION: { id: '123' }
}));

jest.mock('mongoose');

describe('getPermission', () => {
  let req;
  let res;
  let next;

  beforeEach(() => {
    req = httpMocks.createRequest();
    res = httpMocks.createResponse();
    next = jest.fn();
  });

  it('should call getParam with correct arguments', async () => {
    await getPermission(req, res, next);
    expect(getParam).toHaveBeenCalledWith(req, 'chatId');
  });

  it('should find a permission by chat id', async () => {
    await getPermission(req, res, next);
    expect(PermissionModel.findOne).toHaveBeenCalled();
  });

  it('should handle response when no permission found', async () => {
    PermissionModel.findOne.mockResolvedValueOnce(null);
    await getPermission(req, res, next);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toEqual({});
  });

  it('should handle response when permission is found', async () => {
    PermissionModel.findOne.mockResolvedValueOnce({ownerRole: 'owner', anonymousRole: 'anon', loggedInRole: 'logged', specifiedRole: 'spec'});
    await getPermission(req, res, next);
    expect(res.statusCode).toBe(200);
    expect(res._getJSONData()).toBe({ownerRole: 'owner', anonymousRole: 'anon', loggedInRole: 'logged', specifiedRole: 'spec'});
  });

  it('should not explode on mongoose model level errors', async () => {
    PermissionModel.findOne.mockRejectedValueOnce(new Error('Mongoose exploded'));
    await getPermission(req, res, next);
    expect(next).toHaveBeenCalled();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
