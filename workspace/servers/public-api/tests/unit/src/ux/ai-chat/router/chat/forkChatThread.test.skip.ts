import { forkChatThread } from '../../../../../../../src/ux/ai-chat/router/chat';

import { RequestHandler, Response } from 'express';
import { ChatModel } from '@divinci-ai/server-models';
import { ObjectId } from 'mongoose';

const mockRequest = (body, params, user) => ({
  body,
  params,
  user
});

const mockResponse = () => {
  const res: Partial<Response> = {};
  res.statusCode = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const mockNext = () => jest.fn();

jest.mock('@divinci-ai/server-globals', () => ({
  auth0http: {
    getUserId: jest.fn()
  }
}));

jest.mock('@divinci-ai/server-models', () => ({
  ChatModel: {
    create: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn()
  },
}));

jest.mock('@divinci-ai/server-utils', () => ({
  getParam: jest.fn(),
  jsonBody: jest.fn(),
}));

describe('forkChatThread', () => {
  it('throws BAD_FORM if json.title is not string', async () => {
    const req = mockRequest({ title: 123 }, { chatId: 'abc' }, null);
    const res = mockResponse();
    const next = mockNext();

    await forkChatThread(req as any, res as any, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
  });

  it('throws BAD_FORM if chat with the same title exists', async () => {
    const req = mockRequest({ title: 'title' }, { chatId: 'abc' }, 'user_id');
    const res = mockResponse();
    const next = mockNext();
    (ChatModel.findOne as jest.Mock).mockResolvedValueOnce({});

    await forkChatThread(req as any, res as any, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('Chat with this title already exists'));
  });

  it('throws NOT_FOUND if chatToFork is not found', async () => {
    const req = mockRequest({ title: 'title' }, { chatId: 'abc' }, 'user_id');
    const res = mockResponse();
    const next = mockNext();
    (ChatModel.findOne as jest.Mock).mockResolvedValueOnce(null);
    (ChatModel.findById as jest.Mock).mockResolvedValueOnce(null);

    await forkChatThread(req as any, res as any, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('throws BAD_FORM if user trying to fork own chat', async () => {
    const req = mockRequest(
      { title: 'title' },
      { chatId: 'abc' },
      'user_id'
    );

    const res = mockResponse();
    const next = mockNext();
    (ChatModel.findOne as jest.Mock).mockResolvedValueOnce(null);
    (ChatModel.findById as jest.Mock).mockResolvedValueOnce({ ownerUser: 'user_id' });

    await forkChatThread(req as any, res as any, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM('You already own this chat'));
  });

  it('returns 200 and created chat if all conditions are met', async () => {
    const req = mockRequest(
      { title: 'title' },
      { chatId: 'abc' },
      'user_id'
    );

    const res = mockResponse();
    const next = mockNext();

    const chatToFork = {
      _id: ObjectId('abc'),
      ownerUser: 'different_user_id',
      toJSON: () => ({ _id: 'abc', ownerUser: 'different_user_id' })
    };

    (ChatModel.findOne as jest.Mock).mockResolvedValueOnce(null);
    (ChatModel.findById as jest.Mock).mockResolvedValueOnce(chatToFork);
    (ChatModel.create as jest.Mock).mockResolvedValueOnce('created_chat');

    await forkChatThread(req as any, res as any, next);

    expect(res.statusCode).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith('created_chat');
  });
});
