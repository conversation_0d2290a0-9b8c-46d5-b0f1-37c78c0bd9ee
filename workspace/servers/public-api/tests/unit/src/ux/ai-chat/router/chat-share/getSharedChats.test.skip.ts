
const request = require('supertest');
const express = require('express');
const { getMaxRatingOfEachTarget } = require("@divinci-ai/server-models");
const { getSharedChats } = require("../../../../../../../src/ux/ai-chat/router/chat-share.ts");

jest.mock('@divinci-ai/server-globals', () => ({
  getOpenAI: () => ({ openai: {} }),
  auth0http: { getUserId: () => 'testUserID' },
  auth0api: { getUserById: jest.fn() },
}));

jest.mock('@divinci-ai/server-models', () => ({
  PermissionModel: { find: jest.fn() },
  ChatModel: { find: jest.fn() },
  getMaxRatingOfEachTarget: jest.fn(),
}));

jest.mock('@divinci-ai/utils', () => ({
  escapeRegExp: jest.fn(),
}));

describe('getSharedChats', () => {
  it('Should return combined response', async () => {
    const app = express();
    app.use(express.json());
    app.get('/chat', getSharedChats);
    const permMock = [{ target: 'dummyTarget', users: [{ email: 'test', role: 'owner' }] }];
    const chatMock = [{ _id: 'bob', title: 'titleTest', ownerUser: 'sam' }];
    const ratingsMock = [4.5];

    require('@divinci-ai/auth0api').getUserById.mockResolvedValue({ email: 'test' });
    require('@divinci-ai/server-models').PermissionModel.find.mockResolvedValue(permMock);
    require('@divinci-ai/server-models').ChatModel.find.mockResolvedValue(chatMock);
    getMaxRatingOfEachTarget.mockResolvedValue(ratingsMock);

    const res = await request(app).get('/chat');

    expect(res.statusCode).toEqual(200);
    expect(res.body).toEqual([{ chat: chatMock[0], rating: ratingsMock[0] }]);
  });

  it('Should handle errors', async () => {
    const app = express();
    app.use(express.json());
    app.get('/chat', getSharedChats);

    require('@divinci-ai/auth0api').getUserById.mockRejectedValue(new Error('Test Error'));

    const res = await request(app).get('/chat');

    expect(res.statusCode).toEqual(500);
    expect(res.text).toEqual('Test Error');
  });
});
