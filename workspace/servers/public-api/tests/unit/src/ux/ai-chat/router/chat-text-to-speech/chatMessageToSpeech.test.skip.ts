import { chatMessageToSpeech } from "../../../../../../../src/ux/ai-chat/router/chat-text-to-speech";

import { RequestHandler, Request, Response, NextFunction } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { getTextToSpeech } from "@divinci-ai/server-globals";
const {
  textToSpeech: googleTextToSpeech
} = getTextToSpeech();
import { ChatModel, CHAT_CATEGORY_ENUM } from "@divinci-ai/server-models";
import { getUserById } from "@divinci-ai/server-globals";

jest.mock("@divinci-ai/server-utils");
jest.mock("@divinci-ai/server-globals");
jest.mock("@divinci-ai/server-models");

describe("ChatMessageToSpeech Test Suite", ()=>{
  let req: Request;
  let res: Response;
  let next: NextFunction;
  let chat: any;

  beforeEach(()=>{
    req = {} as Request;
    res = {
      statusCode: jest.fn(),
      set: jest.fn(),
      send: jest.fn()
    } as any;
    next = jest.fn();

    chat = {
      messages: [
        {
          _id: "1",
          content: "Hello",
          category: CHAT_CATEGORY_ENUM.TEXT
        }
      ]
    };

    getParam.mockReturnValue("1");
    getUserId.mockReturnValue("user1");
    ChatModel.findById.mockResolvedValue(chat);
    googleTextToSpeech.mockReturnValue({ mimetype: "audio/wav", content: "<audio-content>" });
  });

  afterEach(()=>{
    jest.resetAllMocks();
  });

  it("should throw NOT_FOUND if chat is null", async ()=>{
    ChatModel.findById.mockResolvedValue(null);

    await chatMessageToSpeech(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it("should throw NOT_FOUND if message is undefined", async ()=>{
    getParam.mockReturnValueOnce("1").mockReturnValueOnce("2");

    await chatMessageToSpeech(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it("should throw BAD_FORM if message category is not TEXT", async ()=>{
    chat.messages[0].category = CHAT_CATEGORY_ENUM.IMAGE;

    await chatMessageToSpeech(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
  });

  it("should throw SERVER_ERROR if googleTextToSpeech encounters an error", async ()=>{
    googleTextToSpeech.mockRejectedValue(new Error("Test error"));

    await chatMessageToSpeech(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.SERVER_ERROR);
  });

  it("should set the correct HTTP headers and send the speech content", async ()=>{
    await chatMessageToSpeech(req, res, next);

    expect(res.set).toHaveBeenCalledWith("Content-Type", "audio/wav");
    expect(res.send).toHaveBeenCalledWith("<audio-content>");
  });
});