import { getChatThread } from '../../../../../../../src/ux/ai-chat/router/chat';

import { Request, Response } from 'express';
import { ChatModel } from '@divinci-ai/server-models';
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

jest.mock('@divinci-ai/server-models');
jest.mock('@divinci-ai/server-utils');

describe('getChatThread', () => {
  let req: Request;
  let res: Response;
  let next: jest.Mock;

  beforeEach(() => {
    req = {
      params: { chatId: '123' },
    } as unknown as Request;

    res = {
      statusCode: 0,
      json: jest.fn(),
    } as unknown as Response;

    next = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should return 200 and the chat if chat is found', async () => {
    const mockChat = { id: '123', name: 'Test chat' };
    ChatModel.findById = jest.fn().mockResolvedValue(mockChat);

    await getChatThread(req, res, next);

    expect(res.statusCode).toEqual(200);
    expect(res.json).toHaveBeenCalledWith(mockChat);
    expect(next).not.toHaveBeenCalled();
  });

  it('should call next with error if chat is not found', async () => {
    ChatModel.findById = jest.fn().mockResolvedValue(null);

    await getChatThread(req, res, next);

    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('should call next with error if any occurs during the process', async () => {
    const mockError = new Error('Something went wrong');
    ChatModel.findById = jest.fn().mockRejectedValue(mockError);

    await getChatThread(req, res, next);

    expect(next).toHaveBeenCalledWith(mockError);
  });
});
