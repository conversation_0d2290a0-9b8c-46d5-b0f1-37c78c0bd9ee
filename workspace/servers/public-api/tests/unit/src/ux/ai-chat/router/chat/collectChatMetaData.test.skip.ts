import { collectChatMetaData } from '../../../../../../../src/ux/ai-chat/router/chat';

import { RequestHandler, Response } from 'express';
import { ChatModel, collectMetaData } from "@divinci-ai/server-models";
import { HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from '@divinci-ai/server-utils';
import { ObjectId } from 'mongoose';
import { getParam, jsonBody } from "@divinci-ai/server-utils";

jest.mock('../../../../../../../src/ux/ai-chat/router/chat.ts');
jest.mock('express');
jest.mock('@divinci-ai/server-models');
jest.mock('@divinci-ai/server-utils');
jest.mock('mongoose');

const mockJsonFn = jest.fn();
const mockStatusFn = jest.fn().mockReturnValue({ json: mockJsonFn });
const res = { status: mockStatusFn } as unknown as Response;

describe('collectChatMetaData', () => {
  it('should throw error if chat does not exist', async () => {
    const req = { params: { chatId: new ObjectId() } };
    ChatModel.findById.mockResolvedValue(null);

    await expect(collectChatMetaData(req, res, jest.fn())).rejects.toThrow(HTTP_ERRORS.NOT_FOUND);
  });

  it('should throw error for invalid JSON body', async () => {
    const req = { params: { chatId: new ObjectId() }, body: 'invalid' };
    ChatModel.findById.mockResolvedValue({});
    jsonBody.mockRejectedValue(new Error());

    await expect(collectChatMetaData(req, res, jest.fn())).rejects.toThrow(HTTP_ERRORS.BAD_FORM);
  });

  it('should throw error for bad form', async () => {
    const req = { params: { chatId: new ObjectId() }, body: { replyTo: 123 } };
    ChatModel.findById.mockResolvedValue({});
    jsonBody.mockResolvedValue({ replyTo: 123 });

    await expect(collectChatMetaData(req, res, jest.fn())).rejects.toThrow(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM);
  });

  it('should throw error for invalid content', async () => {
    const req = { params: { chatId: new ObjectId() }, body: { content: 123 } };
    ChatModel.findById.mockResolvedValue({});
    jsonBody.mockResolvedValue({ content: 123 });

    await expect(collectChatMetaData(req, res, jest.fn())).rejects.toThrow(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM);
  });

  it('should return metadata successfully', async () => {
    const req = { params: { chatId: new ObjectId() }, body: { replyTo: 'Id1', content: 'Test' } };
    const chat = { _id: new ObjectId(), replyTo: 'Id1', content: 'Test' };
    const metadata = { id: 'Meta1', data: 'Example' };

    ChatModel.findById.mockResolvedValue(chat);
    jsonBody.mockResolvedValue({ replyTo: 'Id1', content: 'Test' });
    collectMetaData.mockResolvedValue(metadata);

    await collectChatMetaData(req, res, jest.fn());

    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(metadata);
  });
});
