import { deleteChatThread } from '../../../../../../../src/ux/ai-chat/router/chat';

import { Request, Response, NextFunction } from 'express';
import { ChatModel } from "@divinci-ai/server-models";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

jest.mock("@divinci-ai/server-models");
jest.mock("@divinci-ai/server-utils");

describe('deleteChatThread', () => {
  let req: Request;
  let res: Response;
  let next: NextFunction;
  
  beforeEach(() => {
    req = {
      params: {
        chatId: '123',
      },
      user: { sub: 'testing_user_id' },
    } as any;

    res = {
      json: jest.fn(),
      status: jest.fn(() => res),
    } as any;

    next = jest.fn();

    (ChatModel.findOneAndRemove as jest.Mock).mockClear();
  });

  it('should remove the chat and respond with json if chat exists', async () => {
    const chatObject = { _id: '123', ownerUser: 'testing_user_id' };
    (ChatModel.findOneAndRemove as jest.Mock).mockResolvedValue(chatObject);
    
    await deleteChatThread(req, res, next);
    
    expect(ChatModel.findOneAndRemove).toHaveBeenCalledWith({
      _id: '123',
      ownerUser: 'testing_user_id'
    });
    expect(res.json).toHaveBeenCalledWith(chatObject); 
  });

  it('should throw a NOT_FOUND error and call next with error if chat does not exist', async () => {
    (ChatModel.findOneAndRemove as jest.Mock).mockResolvedValue(null);
    
    await deleteChatThread(req, res, next);
    
    expect(ChatModel.findOneAndRemove).toHaveBeenCalledWith({
      _id: '123',
      ownerUser: 'testing_user_id'
    });
    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('should call next with error if an exception is thrown during deletion', async () => {
    const error = new Error('unexpected error');
    (ChatModel.findOneAndRemove as jest.Mock).mockRejectedValue(error);
    
    await deleteChatThread(req, res, next);

    expect(next).toHaveBeenCalledWith(error);
  });
});