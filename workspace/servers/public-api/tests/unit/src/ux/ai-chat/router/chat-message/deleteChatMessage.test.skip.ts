import { deleteChatMessage } from '../../../../../../../src/ux/ai-chat/router/chat-message';

import { Request, Response } from 'express';
import { ChatModel } from '@divinci-ai/server-models';
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

jest.mock('@divinci-ai/server-globals', () => ({
  getRedis: () => ({ chatRedisClient: { publish: jest.fn() } }),
  auth0http: { getUserId: jest.fn() },
}));

jest.mock('@divinci-ai/server-models', () => ({
  ChatModel: { findOneAndUpdate: jest.fn() },
}));

jest.mock('@divinci-ai/server-utils', () => ({
  getParam: jest.fn(),
  jsonBody: jest.fn(),
  HTTP_ERRORS: { UNAVAILABLE: 'Unavailable' },
}));

const mockRequest = (data) => ({
  params: data,
});

const mockResponse = () => {
  const res = {};
  res.json = jest.fn().mockReturnValue(res);
  res.statusCode = jest.fn().mockReturnValue(res);
  return res;
};

const next = jest.fn();

describe('DeleteChatMessage', () => {

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should delete chat message', async () => {
    const req = mockRequest({ chatId: 'abc', messageId: 'xyz' });
    const res = mockResponse();

    ChatModel.findOneAndUpdate.mockResolvedValue('MockChat');
    await deleteChatMessage(req, res, next);

    expect(ChatModel.findOneAndUpdate).toHaveBeenCalled();
    expect(res.json).toHaveBeenCalledWith('MockChat');
    expect(res.statusCode).toHaveBeenCalledWith(200);
    expect(next).not.toHaveBeenCalled();
  });

  it('should throw error when update is null', async () => {
    const req = mockRequest({ chatId: 'abc', messageId: 'xyz' });
    const res = mockResponse();

    ChatModel.findOneAndUpdate.mockResolvedValue(null);
    await deleteChatMessage(req, res, next);

    expect(ChatModel.findOneAndUpdate).toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith(HTTP_ERRORS.UNAVAILABLE);
    expect(res.json).not.toHaveBeenCalled();
    expect(res.statusCode).not.toHaveBeenCalled();
  });

  it('should call next on any other errors', async () => {
    const req = mockRequest({ chatId: 'abc', messageId: 'xyz' });
    const res = mockResponse();

    ChatModel.findOneAndUpdate.mockRejectedValue('Error');
      
    await deleteChatMessage(req, res, next);

    expect(ChatModel.findOneAndUpdate).toHaveBeenCalled();
    expect(next).toHaveBeenCalledWith('Error');
    expect(res.json).not.toHaveBeenCalled();
    expect(res.statusCode).not.toHaveBeenCalled();
  });
});