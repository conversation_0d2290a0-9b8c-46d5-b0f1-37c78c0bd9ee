import {updatePermission} from '../../../../../../../../src/ux/ai-chat/router/chat-permission/end-points';

// Import the required dependencies and function
import { Request, Response } from 'express';
import PermissionModel from '@divinci-ai/server-models';
import { CHAT_ROLES } from '@divinci-ai/models';
import getParam from '@divinci-ai/server-utils';

jest.mock('@divinci-ai/server-globals');
jest.mock('@divinci-ai/server-utils');
jest.mock('@divinci-ai/server-models');
jest.mock('@divinci-ai/models');

describe('updatePermission', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: jest.Mock;

  beforeEach(() => {
    // Mock request and response
    mockRequest = {};
    mockResponse = {
      json: jest.fn(),
      statusCode: 0
    };

    nextFunction = jest.fn();
  });

  it('should handle errors correctly', async () => {
    const err = new Error('Error Occurred');
    getParam.mockImplementationOnce(() => { throw err; });
    await updatePermission(mockRequest as Request, mockResponse as Response, nextFunction);
    expect(nextFunction).toHaveBeenCalledWith(err);
  });

  it('should handle the case of undefined permission', async () => {
    const chatId = "test";
    const userId = "123";
    getParam.mockReturnValue(chatId);
    getUserId.mockReturnValue(userId);
    PermissionModel.findOne.mockResolvedValue(null);
    PermissionModel.mockImplementation(() => ({
      save: jest.fn().mockResolvedValue({})
    }));

    await updatePermission(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(getParam).toHaveBeenCalledWith(mockRequest, 'chatId');
    expect(PermissionModel.findOne).toHaveBeenCalled();
    expect(PermissionModel).toHaveBeenCalled();
    expect(mockResponse.statusCode).toBe(200);
  });

  it('should handle the case of defined permission', async () => {
    const chatId = "test";
    getParam.mockReturnValue(chatId);
    PermissionModel.findOne.mockResolvedValue({
      anonymousRole: CHAT_ROLES.MODERATOR,
      save: jest.fn().mockResolvedValue({})
    });
  });

  it('should throw error for invalid allowance', async () => {
    const chatId = "test";
    getParam.mockReturnValue(chatId);
    PermissionModel.findOne.mockResolvedValue({
      anonymousRole: CHAT_ROLES.GUEST,
      save: jest.fn().mockResolvedValue({})
    });

    const json = { anonymousRole: CHAT_ROLES.BANNED };
    jsonBody.mockResolvedValue(json);

    await updatePermission(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(nextFunction).toHaveBeenLastCalledWith(HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("this doc is already public, can't revert back"));
  });
});