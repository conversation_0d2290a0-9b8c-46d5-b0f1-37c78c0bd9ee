import { allowedRoleMiddleware } from '../../../../../../../../src/ux/ai-chat/router/chat-permission/middleware';

import { RequestHandler } from "express";
import { CHAT_ROLES } from "@divinci-ai/models";
import { ChatModel, PermissionModel } from"@divinci-ai/server-models";
import { getUserIdOptional } from "@divinci-ai/server-globals";

jest.mock("@divinci-ai/server-globals");
jest.mock("@divinci-ai/server-models");

let nextFn;
let mockReq;
beforeEach(() => {
  ChatModel.exists = jest.fn();
  PermissionModel.isUserAllowed = jest.fn();

  getUserIdOptional.mockClear();
  nextFn = jest.fn();
  
  mockReq = {
    params: {
      chatId: '12345'
    }
  }
});

describe('allowedRoleMiddleware', () => {
  it('runs next middleware when user is chat owner', async () => {
    getUserIdOptional.mockReturnValue('1111');
    ChatModel.exists.mockResolvedValue(true);

    const middleware = allowedRoleMiddleware([CHAT_ROLES.CHAT_MEMBER]);
    await middleware(mockReq, {}, nextFn);

    expect(ChatModel.exists).toHaveBeenCalledWith({ _id: '12345', ownerUser: '1111' });
    expect(nextFn).toHaveBeenCalled();
  });

  it('throws FORBIDDEN when user is not allowed', async () => {
    getUserIdOptional.mockReturnValue('2222');
    ChatModel.exists.mockResolvedValue(false);
    PermissionModel.isUserAllowed.mockResolvedValue(false);

    const middleware = allowedRoleMiddleware([CHAT_ROLES.CHAT_MEMBER]);
    await expect(middleware(mockReq, {}, nextFn)).rejects.toThrow('FORBIDDEN');
  });

  it('runs next middleware when user has valid role', async () => {
    getUserIdOptional.mockReturnValue('3333');
    ChatModel.exists.mockResolvedValue(false);
    PermissionModel.isUserAllowed.mockResolvedValue(true);

    const middleware = allowedRoleMiddleware([CHAT_ROLES.CHAT_MEMBER]);
    await middleware(mockReq, {}, nextFn);

    expect(nextFn).toHaveBeenCalled();
  });

  it('throws errors when error occurs in higher level in the stack', async () => {
    getUserIdOptional.mockReturnValue('4444');
    ChatModel.exists.mockRejectedValue(new Error('Something went wrong'));

    const middleware = allowedRoleMiddleware([CHAT_ROLES.CHAT_MEMBER]);
    await expect(middleware(mockReq, {}, nextFn)).rejects.toThrow('Something went wrong');
  });
});