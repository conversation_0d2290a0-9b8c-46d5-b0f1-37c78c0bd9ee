import { describe, it, expect, vi } from 'vitest';
// Import using the @ alias
import { supportRequest } from '@/ux/issue/router/support-request';

describe('Path Alias Test', () => {
  it('should be able to import modules using @ path alias', () => {
    // If we can import the module, this test will pass
    expect(supportRequest).toBeDefined();
    expect(typeof supportRequest).toBe('function');
  });

  it('should be able to call the supportRequest function', async () => {
    // Create mock objects
    const req = {};
    const res = {
      end: vi.fn(),
      statusCode: null
    };
    const next = vi.fn();

    // Call the function
    await supportRequest(req, res, next);
    
    // Verify it worked
    expect(res.statusCode).toBe(200);
    expect(res.end).toHaveBeenCalledWith({ status: "ok" });
  });
});
