import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import { ChatPreferenceModel } from "@divinci-ai/server-models";
import { jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const addRelease: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [body, preferences] = await Promise.all([
      jsonBody(req),
      ChatPreferenceModel.findOrCreate(userId),
    ]);
    const { releaseId } = castShallowObject(body, { releaseId: "string" });
    if(!preferences.savedReleases.includes(releaseId)){
      preferences.savedReleases.push(releaseId);
      preferences.markModified("savedReleases");
      await preferences.save();
    }

    res.statusCode = 200;
    res.json(preferences);
  }catch(e){
    next(e);
  }
};

export const getReleases: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const preferences = await ChatPreferenceModel.findOrCreate(userId);
    res.statusCode = 200;
    res.json(preferences);
  }catch(e){
    next(e);
  }
};

export const removeRelease: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [body, preferences] = await Promise.all([
      jsonBody(req),
      ChatPreferenceModel.findOrCreate(userId),
    ]);
    const { releaseId } = castShallowObject(body, { releaseId: "string" });
    const index = preferences.savedReleases.indexOf(releaseId);
    if(index > -1){
      preferences.savedReleases.splice(index, 1);
      preferences.markModified("savedReleases");
      await preferences.save();
    }

    res.statusCode = 200;
    res.json(preferences);
  }catch(e){
    next(e);
  }
};
