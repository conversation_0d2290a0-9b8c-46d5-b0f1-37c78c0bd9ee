import { HTTP_ERRORS } from "@divinci-ai/server-utils";

import { FineTuneCSVConfigModel, R2_BUCKET_FINETUNE_CSV } from "@divinci-ai/server-models";

import { R2_INSTANCE } from "../../util";

export async function updateFile(
  target: string,
  params: { whitelabelId: string, csvId: string },
  fileLocation: { bucket: string, objectKey: string }
){
  const doc = await FineTuneCSVConfigModel.findOne({ target, _id: params.csvId });

  if(!doc) throw HTTP_ERRORS.NOT_FOUND;

  const oldPointer = doc.itemPointer;

  doc.itemPointer = {
    bucket: R2_BUCKET_FINETUNE_CSV,
    objectKey: `${params.whitelabelId}/${fileLocation.objectKey}`,
  };

  await Promise.all([
    R2_INSTANCE.copyObject({
      CopySource: `${fileLocation.bucket}/${fileLocation.objectKey}`,
      Bucket: doc.itemPointer.bucket,
      Key: doc.itemPointer.objectKey
    }),
    R2_INSTANCE.deleteObject({
      Bucket: oldPointer.bucket,
      Key: oldPointer.objectKey,
    }).catch((e: Error)=>(
      console.error("Failed to delete files from R2 when updating csv file", e)
    ))
  ]);

  await doc.save();

  return doc;
}
