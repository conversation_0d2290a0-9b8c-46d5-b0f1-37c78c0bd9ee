
import { FineTuneCSVConfigModel, R2_BUCKET_FINETUNE_CSV } from "@divinci-ai/server-models";

import { R2_INSTANCE } from "../util";

export async function createFile(
  target: string,
  whitelabelId: string,
  config: {
    title: string,
    headers: string,
    headerConfig: string,
    flaggerConfig: string,
  },
  fileLocation: { bucket: string, objectKey: string }
){
  const doc = new FineTuneCSVConfigModel({
    target,
    title: config.title,
    headers: JSON.parse(String(config.headers)),
    headerConfig: JSON.parse(String(config.headerConfig)),
    flaggerConfig: JSON.parse(String(config.flaggerConfig)),
    itemPointer: {
      bucket: R2_BUCKET_FINETUNE_CSV,
      objectKey: `${whitelabelId}/${fileLocation.objectKey}`,
    },
  });

  await doc.validate();

  await R2_INSTANCE.copyObject({
    CopySource: `${fileLocation.bucket}/${fileLocation.objectKey}`,
    Bucket: doc.itemPointer.bucket,
    Key: doc.itemPointer.objectKey
  });

  await doc.save();

  return doc;
}
