import { condenseTarget } from "@divinci-ai/models";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { IncomingMessage } from "http";

export async function getWhitelabelTarget(
  req: IncomingMessage & { params?: any }
): Promise<{ target: string, whitelabelId: string }>{
  const whitelabelId = getParam(req, "whitelabelId");

  const whitelabel = await WhiteLabelModel.findOne({ _id: whitelabelId });
  if(!whitelabel) throw HTTP_ERRORS.NOT_FOUND;

  // Try using the WHITE_LABEL_LOCATION constant that's likely already defined
  const target = condenseTarget({
    model: "whitelabel",
    database: "default",
    id: whitelabelId
  });

  return { target, whitelabelId };
}

