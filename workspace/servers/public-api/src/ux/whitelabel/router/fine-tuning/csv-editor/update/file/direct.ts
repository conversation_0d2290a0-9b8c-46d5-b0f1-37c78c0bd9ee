import { RequestHand<PERSON> } from "express";
import { CastedBusBoy, getParam } from "@divinci-ai/server-utils";

import { getWhitelabelTarget, fileHandler } from "../../util";

const updateCSVBusboyHandleBody = CastedBusBoy.create({
  itemPointer: "file" // Change from "json" to "string" and parse it manually later
}, fileHandler);

import { updateFile } from "./updateFile";
export const directUpdateFile: RequestHandler = async (req, res, next)=>{
  try {
    const csvId = getParam(req, "csvId");
    const [body, { target, whitelabelId }] = await Promise.all([
      updateCSVBusboyHandleBody.consumeRequest(req),
      getWhitelabelTarget(req),
    ]);

    const doc = await updateFile(
      target,
      { whitelabelId, csvId },
      body.itemPointer
    );

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
};
