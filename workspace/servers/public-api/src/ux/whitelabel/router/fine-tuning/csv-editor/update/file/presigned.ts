import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { json<PERSON><PERSON>, getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";

import { FineTuneCSVConfigModel } from "@divinci-ai/server-models";

import {
  createPresignedURL, castPresignedConfig, handlePresignedURL
} from "../../../../../../../util/presigned-r2";
import {
  getWhitelabelTarget,
  R2_INSTANCE,
} from "../../util";

import { castShallowObject } from "@divinci-ai/utils";
import { getUserId } from "@divinci-ai/server-globals";

const PRESIGNED_PURPOSE = "Finetune CSVFile Update";
export const presignedPrepare: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const csvId = getParam(req, "csvId");
    const [unCastedBody, { target }] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req)
    ]);

    const doc = await FineTuneCSVConfigModel.findOne({ target, _id: csvId });

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    const fileInfo = castShallowObject(
      unCastedBody, { filename: "string", byteSize: "number" }
    );

    await getWhitelabelTarget(req);
    const presigned = await createPresignedURL(
      R2_INSTANCE,
      { userId, purpose: PRESIGNED_PURPOSE },
      fileInfo
    );

    res.statusCode = 200;
    res.json(presigned);
  }catch(e){
    next(e);
  }
};

import { updateFile } from "./updateFile";
export const presignedFinalized: RequestHandler = async (req, res, next)=>{
  try {

    const userId = getUserId(req);
    const csvId = getParam(req, "csvId");
    const [unCastedBody, { target, whitelabelId }] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    const {
      itemPointer: presignedUncasted,
    } = castShallowObject(unCastedBody, {
      itemPointer: "string"
    });

    const itemPointer = await handlePresignedURL(
      R2_INSTANCE,
      { userId, purpose: PRESIGNED_PURPOSE },
      castPresignedConfig(presignedUncasted),
    );

    const doc = await updateFile(
      target,
      { whitelabelId, csvId },
      { bucket: itemPointer.Bucket, objectKey: itemPointer.Key }
    );

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
};
