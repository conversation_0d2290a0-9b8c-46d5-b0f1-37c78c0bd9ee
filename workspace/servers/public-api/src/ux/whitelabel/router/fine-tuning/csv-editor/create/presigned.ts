import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { CastedBusBoy, jsonBody } from "@divinci-ai/server-utils";

import {
  createPresignedURL, castPresignedConfig, handlePresignedURL
} from "../../../../../../util/presigned-r2";
import {
  getWhitelabelTarget,
  R2_INSTANCE,
  fileHandler
} from "../util";

export const newDocBusboyHandleBody = CastedBusBoy.create({
  title: "string", headers: "string",
  headerConfig: "string", flaggerConfig: "string",
  itemPointer: "file"
}, fileHandler);

import { castShallowObject } from "@divinci-ai/utils";
import { getUserId } from "@divinci-ai/server-globals";


const PRESIGNED_PURPOSE = "Finetune CSVFile Create";
export const presignedPrepare: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [unCastedBody] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    const fileInfo = castShallowObject(
      unCastedBody, { filename: "string", byteSize: "number" }
    );

    const presigned = await createPresignedURL(
      R2_INSTANCE,
      { userId, purpose: PRESIGNED_PURPOSE },
      fileInfo
    );

    res.statusCode = 200;
    res.json(presigned);
  }catch(e){
    next(e);
  }
};

import { createFile } from "./createFile";
export const presignedFinalized: RequestHandler = async (req, res, next)=>{
  try {
    const userId = getUserId(req);
    const [unCastedBody, { target, whitelabelId }] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    const {
      title,
      headers: headersUncasted,
      headerConfig: headerConfigUncasted,
      flaggerConfig: flaggerConfigUncasted,
      itemPointer: presignedUncasted,
    } = castShallowObject(unCastedBody, {
      title: "string",
      headers: "string",
      headerConfig: "string",
      flaggerConfig: "string",
      itemPointer: "string"
    });

    const itemPointer = await handlePresignedURL(
      R2_INSTANCE,
      { userId, purpose: PRESIGNED_PURPOSE },
      castPresignedConfig(presignedUncasted)
    );

    const doc = await createFile(
      target, whitelabelId,
      {
        title: title,
        headers: headersUncasted,
        headerConfig: headerConfigUncasted,
        flaggerConfig: flaggerConfigUncasted,
      },
      { bucket: itemPointer.Bucket, objectKey: itemPointer.Key }
    );

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
};
