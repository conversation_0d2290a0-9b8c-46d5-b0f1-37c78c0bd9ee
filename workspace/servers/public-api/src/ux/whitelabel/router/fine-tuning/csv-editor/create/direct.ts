import { Request<PERSON><PERSON><PERSON> } from "express";
import { CastedBusBoy } from "@divinci-ai/server-utils";

import { getWhitelabelTarget, fileHandler } from "../util";

import { createFile } from "./createFile";
export const newDocBusboyHandleBody = CastedBusBoy.create({
  title: "string", headers: "string",
  headerConfig: "string", flaggerConfig: "string",
  itemPointer: "file"
}, fileHandler);

export const directUpload: RequestHandler = async (req, res, next)=>{
  try {
    const [unCastedBody, { target, whitelabelId }] = await Promise.all([
      newDocBusboyHandleBody.consumeRequest(req),
      getWhitelabelTarget(req),
    ]);

    const doc = await createFile(
      target, whitelabelId,
      {
        title: unCastedBody.title,
        headers: String(unCastedBody.headers),
        headerConfig: String(unCastedBody.headerConfig),
        flaggerConfig: String(unCastedBody.flaggerConfig),
      },
      unCastedBody.itemPointer
    );

    res.status(200).json(doc);
  }catch(e){
    next(e);
  }
};
