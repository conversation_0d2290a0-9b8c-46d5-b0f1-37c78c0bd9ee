import { CastedBusBoy } from "@divinci-ai/server-utils";
import { R2BusBoyFileHandler } from "@divinci-ai/server-globals";
import { getFineTuneR2Instance } from "@divinci-ai/server-globals";
// import { R2_BUCKET_FINETUNE_CSV } from "@divinci-ai/server-models";

export { R2FilePointer } from "@divinci-ai/server-globals";

// Export the R2 instance for use in the router
export const R2_INSTANCE = getFineTuneR2Instance();
export const PRIVATE_UPLOADS_BUCKET = "private-temporary-uploads";

// Create a file handler for R2
export const fileHandler = new R2BusBoyFileHandler(
  R2_INSTANCE,
  PRIVATE_UPLOADS_BUCKET,
  {}, // Empty metadata object
  null // Optional: Set to null or a Set of allowed MIME types
);

export const updateDocBusboyHandleBody = new CastedBusBoy({
  title: "string",
  headers: "string",
  headerConfig: "string",
  flaggerConfig: "string"
}, fileHandler);


export const chunkBusboyHandleBody = new CastedBusBoy({
  chunkIndex: "string",
  totalChunks: "string",
  chunk: "file"
}, fileHandler);
