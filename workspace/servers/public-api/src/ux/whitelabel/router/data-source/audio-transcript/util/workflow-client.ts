import fetch from "node-fetch-commonjs";
import { WORKFLOW_API_URL } from "@divinci-ai/server-tools";
import { CLOUDFLARE_API_KEY } from "@divinci-ai/server-globals";

// Start the video-to-audio workflow
export async function startVideoToAudioWorkflow(payload: {
  videoFile: {
    fileId: string,
    objectKey: string,
    fileName: string,
    title?: string,
    description?: string,
  },
  instanceId: string,
  whitelabelId: string,
  auth0Token: string,
}){
  const response = await fetch(`${WORKFLOW_API_URL}/api/video-to-audio`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${CLOUDFLARE_API_KEY}`
    },
    body: JSON.stringify(payload)
  });

  if(!response.ok) {
    const error = await response.json();
    throw new Error(`Failed to start video-to-audio workflow: ${JSON.stringify(error)}`);
  }

  return await response.json();
}