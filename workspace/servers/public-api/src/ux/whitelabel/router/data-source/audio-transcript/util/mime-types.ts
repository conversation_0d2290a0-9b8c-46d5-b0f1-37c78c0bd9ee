
import { SPEAKER_DIARIZERS, SPEECH_TO_TEXT } from "@divinci-ai/server-tools";
import { r2 } from "./r2-constants";

export { r2 };

export const VALID_MIMETYPES = (function(){
  const allDiarizers = Object.values(SPEAKER_DIARIZERS.getAllTools());
  const options = new Set(allDiarizers[0].validMimetypes());
  filterMissing(allDiarizers);
  filterMissing(Object.values(SPEECH_TO_TEXT.getAllTools()));

  function filterMissing(processors: Array<{ validMimetypes(): Array<string> }>){
    for(const processor of processors){
      const processorMimetypes = processor.validMimetypes();
      const toRemove: Array<string> = [];
      for(const option of options){
        if(!processorMimetypes.includes(option)){
          toRemove.push(option);
        }
      }
      for(const item of toRemove){
        options.delete(item);
      }
    }
  }

  return options;
})();

