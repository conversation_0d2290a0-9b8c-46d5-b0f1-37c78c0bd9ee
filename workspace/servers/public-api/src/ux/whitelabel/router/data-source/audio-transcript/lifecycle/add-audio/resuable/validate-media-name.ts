
import { parse as pathParse } from "node:path";
import { lookup as mimetypeLookup } from "mime-types";

import {
  AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS,
  AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS,
} from "@divinci-ai/models";

export {
  AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS,
  AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS,
};

export function ensureValidMediaName(filename: string, e?: (message: string)=>any){
  const parsed = pathParse(filename);
  if(!parsed.ext){
    if(e) throw e("File requires an extension");
    throw new Error("File requires an extension");
  }
  const convertArgs = canConvertToMp3(filename);
  if(!convertArgs.support){
    if(e) throw e("Unsupported file type");
    throw new Error("Unsupported file type");
  }
  const mimetype = mimetypeLookup(filename);
  if(!mimetype){
    if(e) throw e("Invalid content type");
    throw new Error("Invalid content type");
  }
  return { mimetype, ...convertArgs };
}

function canConvertToMp3(filename: string){
  const originalFile = pathParse(filename);
  const ext = originalFile.ext.slice(1).toLowerCase();
  if(AUDIOSOURCE_SUPPORTED_VIDEO_EXTENSIONS.has(ext)){
    return { support: true, type: "video" };
  }
  if(AUDIOSOURCE_SUPPORTED_AUDIO_EXTENSIONS.has(ext)){
    return { support: true, type: "audio" };
  }
  return { support: false, type: "unknown" };
}
