import { Router } from "express";

export const router = Router({ mergeParams: true });

import { presignedPrepare, presignedFinalized } from "./add-audio/from-presigned";
router.post("/presigned/prepare", presignedPrepare);
router.post("/presigned/finalized", presignedFinalized);
import { createAudioTranscriptFromURL } from "./add-audio/from-url";
router.post("/url", createAudioTranscriptFromURL);
import { createAudioTranscriptFromFile } from "./add-audio/from-file";
router.post("/file", createAudioTranscriptFromFile);


import { listAudioTranscripts } from "./get-audio";
router.get("/", listAudioTranscripts);

import { getAudioTranscript } from "./get-audio";
router.get("/:audioId", getAudioTranscript);

import { updateAudioUserInfo } from "./modify-audio";
router.post("/:audioId/user-info", updateAudioUserInfo);
import { nameSpeakersAudioTranscript } from "./modify-audio";
router.post("/:audioId/name-speakers", nameSpeakersAudioTranscript);
import { reprocessAudioTranscript } from "./modify-audio";
router.post("/:audioId/reprocess", reprocessAudioTranscript);

import { updateSampleAudioTranscript } from "./samples";
router.post("/:audioId/samples/:sampleId", updateSampleAudioTranscript);

import { generateRagFile } from "./actions";
router.post("/:audioId/rag-file", generateRagFile);
import { generateFinetuneFile } from "./actions";
router.post("/:audioId/finetune-file", generateFinetuneFile);

import { deleteAudioTranscript } from "./modify-audio";
router.delete("/:audioId", deleteAudioTranscript);
