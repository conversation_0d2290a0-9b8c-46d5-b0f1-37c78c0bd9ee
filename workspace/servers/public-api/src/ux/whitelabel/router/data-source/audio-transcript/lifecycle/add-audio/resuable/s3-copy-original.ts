

import { r2 } from "../../../util/r2-constants";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { uniqueId } from "@divinci-ai/utils";
import { parse as pathParse } from "node:path";

export async function getS3Info(s3Location: { Bucket: string, Key: string }){
  const head = await r2.headObject(s3Location);
  const etag = head.ETag;
  if(!etag) throw HTTP_ERRORS_WITH_CONTEXT.UNAVAILABLE("Missing Etag on file");
  const contentLength = head.ContentLength;
  if(!contentLength) throw HTTP_ERRORS_WITH_CONTEXT.UNAVAILABLE("Missing contentLength on file");
  return { etag, contentLength };
}


export async function copyOriginalToS3(source: { Bucket: string, Key: string }, whitelabelId: string){
  const parsed = pathParse(source.Key);
  if(!parsed.ext){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Url requires an extension");
  }

  const desination = {
    Bucket: "workspace-audio",
    Key: `${whitelabelId}/${uniqueId()}${parsed.ext}`
  };

  await r2.copyObject({
    ...desination,
    CopySource: `${source.Bucket}/${source.Key}`,
  });

  return desination;
}
