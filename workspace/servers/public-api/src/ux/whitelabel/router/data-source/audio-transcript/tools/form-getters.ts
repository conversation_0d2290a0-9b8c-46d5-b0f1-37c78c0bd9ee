import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

export const doesAudioExist: RequestHandler = async function(req, res, next){
  try {
    const url = new URL(req.originalUrl, "http://localhost");

    const sourceType = url.searchParams.get("type");
    switch(sourceType){
      case "url": break;
      case "file": break;
      default: throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "type can only be one of video, url, file"
      );
    }
    const sourceId = url.searchParams.get("id");

    const { target } = await getWhitelabelTarget(req);
    const pages = await DataSourceAudioTranscriptModel.find({
      target,
      sourceOrigin: { sourceType, sourceId, }
    }).select({ _id: 1, title: 1 });

    res.statusCode = 200;
    res.json(pages);

  }catch(e){
    next(e);
  }
};

import { VALID_MIMETYPES } from "../util/mime-types";
export const validAudioMimetypes: RequestHandler = async function(req, res, next){
  try {
    await getWhitelabelTarget(req);

    res.statusCode = 200;
    res.json(Array.from(VALID_MIMETYPES));
  }catch(e){
    next(e);
  }
};
