import { HTTP_ERRORS_WITH_CONTEXT, S3Writable } from "@divinci-ai/server-utils";


import { r2 } from "../../../util/r2-constants";

import { pipeline } from "stream/promises";
import { uniqueId } from "@divinci-ai/utils";

import fetch from "node-fetch-commonjs";
import { parse as pathParse } from "node:path";
import { lookup as mimetypeLookup } from "mime-types";
import { Readable } from "node:stream";

export async function handleDirectUrl(whitelabelId: string, url: URL){
  const parsed = pathParse(url.pathname);
  const contentType = mimetypeLookup(parsed.base);
  if(!contentType){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("URL has an invalid mimetype");
  }

  const response = await fetch(url);
  if(!response.ok){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Can't retrieve url");
  }

  const r2Pointer = {
    Bucket: "workspace-audio",
    Key: `${whitelabelId}/${uniqueId()}${parsed.ext}`
  };

  await pipeline(
    response.body as Readable,
    new S3Writable(r2, {
      ...r2Pointer,
      ContentType: contentType,
      Metadata: {}
    })
  );

  return { r2Pointer, filename: parsed.base };
}
