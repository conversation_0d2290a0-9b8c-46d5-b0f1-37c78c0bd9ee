import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../util/whitelabel";
import { jsonBody, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { r2 } from "../util/mime-types";

import { AUDIO_TOOLS, SPEAKER_DIARIZERS, SPEECH_TO_TEXT } from "@divinci-ai/server-tools";

// Add this new handler for video diarization
export const dryRunDiarizeVideo: RequestHandler = async function(req, res, next){
  try {
    const [{ whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req)
    ]);

    const { videoId, diarizerTool } = castShallowObject(
      body, {
        videoId: "string",
        diarizerTool: "string"
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid request body format")
    );

    // Get video file information
    const videoFileInfo = await getVideoFileInfo(whitelabel._id.toString(), videoId);

    // Get the diarizer tool
    const diarizer = SPEAKER_DIARIZERS.tryToGet(diarizerTool);

    if(!diarizer) {
      throw new Error(`Diarizer tool '${diarizerTool}' not found`);
    }

    // Run diarization on the video file using processFile method
    const diarizationResult = await diarizer.api.processFile(
      {
        s3: r2, // You'll need to import r2 from the appropriate module
        Bucket: videoFileInfo.bucket,
        Key: videoFileInfo.audioPath || videoFileInfo.objectKey,
        originalName: videoFileInfo.fileName
      },
      { languageCode: "en-US" }
    );

    res.statusCode = 200;
    res.json(diarizationResult);
  }catch(e){
    next(e);
  }
};

// Add this new handler for video slicing
export const dryRunSliceVideo: RequestHandler = async function(req, res, next){
  try {
    const [{ whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req)
    ]);

    const { videoId, start, end } = castShallowObject(
      body, {
        videoId: "string",
        start: "number",
        end: "number"
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid request body format")
    );

    // Get video file information
    const videoFileInfo = await getVideoFileInfo(whitelabel._id.toString(), videoId);

    const sliceResult = await AUDIO_TOOLS.createSlice(
      {
        Bucket: videoFileInfo.bucket,
        Key: videoFileInfo.audioPath || videoFileInfo.objectKey
      },
      { start, end }
    );

    res.statusCode = 200;
    res.json(sliceResult);
  }catch(e){
    next(e);
  }
};

// Add this new handler for video transcription
export const dryRunTranscribeVideo: RequestHandler = async function(req, res, next){
  try {
    const [{ whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req)
    ]);

    const { videoId, transcriberTool } = castShallowObject(
      body, {
        videoId: "string",
        transcriberTool: "string"
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid request body format")
    );

    // Get video file information
    const videoFileInfo = await getVideoFileInfo(whitelabel._id.toString(), videoId);

    // Get the transcriber tool
    const transcriber = SPEECH_TO_TEXT.tryToGet(transcriberTool);

    if(!transcriber) {
      throw new Error(`Transcriber tool '${transcriberTool}' not found`);
    }

    // Run transcription on the video file using processFile method
    const transcriptionResult = await transcriber.api.processFile(
      {
        s3: r2,
        Bucket: videoFileInfo.bucket,
        Key: videoFileInfo.audioPath || videoFileInfo.objectKey,
        originalName: videoFileInfo.fileName
      },
      { languageCode: "en-US" }
    );

    res.statusCode = 200;
    res.json(transcriptionResult);
  }catch(e){
    next(e);
  }
};

// Add this new handler for video slicing and transcription
export const dryRunSliceTranscribeVideo: RequestHandler = async function(req, res, next){
  try {
    const [{ whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req)
    ]);

    const { videoId, start, end, transcriberTool } = castShallowObject(
      body, {
        videoId: "string",
        start: "number",
        end: "number",
        transcriberTool: "string"
      },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid request body format")
    );

    // Get video file information
    const videoFileInfo = await getVideoFileInfo(whitelabel._id.toString(), videoId);

    // Get the tools
    const tools = {
      transcriber: SPEECH_TO_TEXT.tryToGet(transcriberTool),
    };

    if(!tools.transcriber) {
      throw new Error(`Transcriber tool '${transcriberTool}' not found`);
    }

    // Run slicing on the video file using processFile method
    const sliceResult = await AUDIO_TOOLS.createSlice(
      {
        Bucket: videoFileInfo.bucket,
        Key: videoFileInfo.audioPath || videoFileInfo.objectKey,
      },
      { start, end }
    );

    // Run transcription on the slice audio using processFile method
    const transcriptionResult = await tools.transcriber.api.processFile(
      {
        s3: r2,
        Bucket: sliceResult.Bucket || videoFileInfo.bucket,
        Key: sliceResult.Key,
        originalName: videoFileInfo.fileName // Use videoFileInfo.fileName directly
      },
      { languageCode: "en-US" }
    );

    res.statusCode = 200;
    res.json({
      slice: sliceResult,
      transcription: transcriptionResult
    });
  }catch(e){
    next(e);
  }
};

// Helper function to get video file information
async function getVideoFileInfo(whitelabelId: string, videoId: string){
  // This would be implemented to retrieve video file information from your database
  // For now, we'll return a mock implementation
  return {
    objectKey: `${whitelabelId}/videos/${videoId}`,
    audioPath: `${whitelabelId}/audio/${videoId}.mp3`,
    bucket: "workspace-videos",
    fileName: `video-${videoId}.mp4`,
    title: "Video File",
    description: "Uploaded video file",
    mimeType: "video/mp4",
    size: 0
  };
}
