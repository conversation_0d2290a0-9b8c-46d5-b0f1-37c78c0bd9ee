import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const generateRagFile: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);
    const { speakers, chunkingTool } = castShallowObject(
      body, {
        speakers: "string[]", chunkingTool: "string"
      }
    );

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    const { fileDoc } = await foundDoc.generateRagFile(speakers, chunkingTool);

    res.statusCode = 200;
    res.json(fileDoc);
  }catch(e){
    next(e);
  }
};

export const generateFinetuneFile: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);
    const { prompters, responders } = castShallowObject(
      body, {
        prompters: "string[]", responders: "string[]"
      }
    );

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    const { finetuneDoc } = await foundDoc.generateFineTuneFile(prompters, responders);

    res.statusCode = 200;
    res.json(finetuneDoc);

  }catch(e){
    next(e);
  }
};
