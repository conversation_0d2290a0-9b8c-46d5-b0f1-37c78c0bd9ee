import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { IncomingMessage } from "http";

export function castURL(urlString: string){
  try {
    const url = new URL(urlString);
    if(url.pathname.at(-1) === "/") url.pathname = url.pathname.slice(0, -1);
    return url;
  }catch(e){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid URL");
  }
}

export async function getWhitelabelTarget(req: IncomingMessage){
  const whitelabelId = getParam(req, "whitelabelId");

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });

  const whitelabel = await WhiteLabelModel.findById(whitelabelId).select("_id");
  if(whitelabel === null){
    throw HTTP_ERRORS.NOT_FOUND;
  }

  return { whitelabel, target };
}
