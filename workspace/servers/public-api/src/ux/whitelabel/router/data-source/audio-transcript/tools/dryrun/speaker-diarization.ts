import { SPEAKER_DIARIZERS } from "@divinci-ai/server-tools";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

import { r2 } from "../../util/r2-constants";
import { handleDirectUrl } from "../../util/handle-url";
import { RequestHandler } from "express";


export const speakerDiarization: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [body] = await Promise.all([jsonBody(req)]);
    const { url, diarizerTool } = castShallowObject(
      body, {
        url: "string" as const, diarizerTool: "string" as const
      }
    );

    const diarizer = SPEAKER_DIARIZERS.tryToGetTool(diarizerTool);

    const { r2Pointer, filename } = await handleDirectUrl(
      whitelabelId, new URL(url)
    );

    const result = await diarizer.processFile(
      {
        s3: r2,
        ...r2Pointer,
        originalName: filename
      },
      { languageCode: "en-US" }
    );

    try {
      await r2.deleteObject(r2Pointer);
    }catch(e){
      console.error("couldn't cleanup file");
    }

    res.statusCode = 200;
    res.json(result);
  }catch(error){
    next(error);
  }
};
