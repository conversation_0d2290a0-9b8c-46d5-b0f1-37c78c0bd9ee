import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { CastedBusBoy } from "@divinci-ai/server-utils";
import { transcribeMedia } from "../resuable/transcribe-media";

import { R2BusBoyFileHandler } from "@divinci-ai/server-globals";

import { r2 } from "../../../util/r2-constants";
const fileHandler = new R2BusBoyFileHandler(
  r2,
  "private-temporary-uploads",
  {}, // Empty metadata object
);

const castAudioBody = CastedBusBoy.create(
  {
    mediaFile: "file",
    diarizerTool: "string",
    transcriberTool: "string",
  }, fileHandler,
  { mediaFile: (info)=>{
    ensureValidMediaName(info.filename);
    return true;
  } }
);

import { ensureValidMediaName } from "../resuable/validate-media-name";

export const createAudioTranscriptFromFile: RequestHandler = async function(req, res, next){
  try {
    const [{ target, whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      castAudioBody.consumeRequest(req),
    ]);

    const doc = await transcribeMedia(
      target, whitelabel._id.toString(),
      { diarizer: body.diarizerTool, transcriber: body.transcriberTool },
      { Bucket: body.mediaFile.bucket, Key: body.mediaFile.objectKey },
      {
        filename: body.mediaFile.info.filename,
        sourceType: "file",
        rawValue: body.mediaFile.info.filename,
      },
    );

    res.statusCode = 200;
    res.json(doc);
  }catch(e){
    next(e);
  }
};
