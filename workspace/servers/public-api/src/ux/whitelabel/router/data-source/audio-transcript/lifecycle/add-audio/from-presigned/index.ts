import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { createPresignedURL, castPresignedConfig, handlePresignedURL } from "../../../../../../../../util/presigned-r2";
import { getUserId } from "@divinci-ai/server-globals";
// import { R2_INSTANCE } from "../../../../../fine-tuning/csv-editor/util";
import { r2 } from "../../../util/r2-constants";

import { transcribeMedia } from "../resuable/transcribe-media";
import { ensureValidMediaName } from "../resuable/validate-media-name";

// Constants
const PRESIGNED_PURPOSE = "AudioTranscript Video Upload";

// Presigned URL preparation endpoint
export const presignedPrepare: RequestHandler = async function(req, res, next){
  console.log("🖋️ presignedPrepare hit!");
  try {
    const userId = getUserId(req);
    const [unCastedBody] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    // Handle both single file and multiple files
    const fileInfo = castShallowObject(unCastedBody, {
      filename: "string",
      byteSize: "number",
    });

    ensureValidMediaName(
      fileInfo.filename, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM
    );

    const presignedUrl = await createPresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      fileInfo
    );

    res.statusCode = 200;
    res.json(presignedUrl);
  }catch(e) {
    next(e);
  }
};

// Presigned URL finalization endpoint
export const presignedFinalized: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const [unCastedBody, { target, whitelabel }] = await Promise.all([
      jsonBody(req),
      getWhitelabelTarget(req),
    ]);

    const {
      diarizerTool,
      transcriberTool,
      mediaFile: mediaFileRaw,
    } = castShallowObject(unCastedBody, {
      diarizerTool: "string",
      transcriberTool: "string",
      mediaFile: "string"
    });

    const presignedConfig = castPresignedConfig(mediaFileRaw);

    const presigned = await handlePresignedURL(
      r2,
      { userId, purpose: PRESIGNED_PURPOSE },
      presignedConfig
    );

    const audioFile = await transcribeMedia(
      target, whitelabel._id.toString(),
      { diarizer: diarizerTool, transcriber: transcriberTool },
      presigned,
      {
        filename: presignedConfig.filename,
        sourceType: "file",
        rawValue: presignedConfig.filename,
      }
    );

    res.status(200).json(audioFile);
  }catch(e) {
    next(e);
  }
};
