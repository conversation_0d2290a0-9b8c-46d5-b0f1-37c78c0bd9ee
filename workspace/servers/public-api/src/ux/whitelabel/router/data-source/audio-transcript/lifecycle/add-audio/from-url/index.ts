import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../../util/whitelabel";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";

import { castShallowObject } from "@divinci-ai/utils";

import { handleDirectUrl } from "./url-to-s3";
import { ensureValidMediaName } from "../resuable/validate-media-name";
import { transcribeMedia } from "../resuable/transcribe-media";
export const createAudioTranscriptFromURL: RequestHandler = async function(req, res, next){
  try {

    const [{ target, whitelabel }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req)
    ]);
    const { url: urlRaw, diarizerTool, transcriberTool } = castShallowObject(
      body, {
        url: "string", diarizerTool: "string", transcriberTool: "string"
      }
    );

    if(!URL.canParse(urlRaw)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid url");
    }
    const url = new URL(urlRaw);

    ensureValidMediaName(
      url.pathname, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM
    );

    const audio = await handleDirectUrl(whitelabel._id.toString(), url);

    const doc = await transcribeMedia(
      target, whitelabel._id.toString(),
      { diarizer: diarizerTool, transcriber: transcriberTool },
      audio.r2Pointer,
      {
        filename: audio.filename,
        sourceType: "url",
        rawValue: urlRaw,
      },
      audio.r2Pointer
    );

    res.statusCode = 200;
    res.json(doc);
  }catch(e){
    next(e);
  }
};
