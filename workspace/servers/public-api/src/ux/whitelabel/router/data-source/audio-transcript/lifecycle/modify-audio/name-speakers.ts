import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";

export const nameSpeakersAudioTranscript:RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const [{ target }, uncastedBody] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);

    const body = castToObject(uncastedBody);

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    const speakers = new Set(foundDoc.samples.map((sample)=>(sample.speaker)));
    const names = new Set<string>();
    const speakerMap: Record<string, string> = {};
    for(const [speaker, name] of Object.entries(body)){
      if(typeof name !== "string"){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Speaker Name should be a string");
      }
      if(!name){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Speaker Name should not be an empty string");
      }
      if(!speakers.has(speaker)){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unexpected Speaker Id");
      }
      if(names.has(name)){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Duplicate Speaker Name");
      }
      names.add(name);
      speakerMap[speaker] = name;
    }

    foundDoc.speakerNames = speakerMap;

    await foundDoc.save();

    res.statusCode = 200;
    res.json(foundDoc);

  }catch(e){
    next(e);
  }
};