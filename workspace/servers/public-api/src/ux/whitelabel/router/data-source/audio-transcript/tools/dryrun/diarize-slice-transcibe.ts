import { AUDIO_TOOLS, SPEAKER_DIARIZERS, SPEECH_TO_TEXT, } from "@divinci-ai/server-tools";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

import { r2, CLOUDFLARE_AUDIO_PUBLIC_URL } from "../../util/r2-constants";
import { handleDirectUrl } from "../../util/handle-url";

import { RequestHandler } from "express";

type SpeakerSegment = {
  start: number, end: number, speaker: string,
};

export const diarizeSliceTranscribe: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [body] = await Promise.all([jsonBody(req)]);
    const { url, diarizerTool, transcriberTool } = castShallowObject(
      body, {
        url: "string", diarizerTool: "string", transcriberTool: "string"
      }
    );

    const diarizer = SPEAKER_DIARIZERS.tryToGetTool(diarizerTool);
    const transcriber = SPEECH_TO_TEXT.tryToGetTool(transcriberTool);


    const { r2Pointer, filename } = await handleDirectUrl(
      whitelabelId, new URL(url)
    );

    const filePointer = {
      s3: r2,
      ...r2Pointer,
      originalName: filename
    };

    // Todo: STREAMS! We will want to turn all workers into a stream
    const result = await diarizer.processFile(
      filePointer,
      { languageCode: "en-US" }
    );

    console.log("Diarizing Result:", result);

    let prevSegment: null | SpeakerSegment = null;
    const ignoredSlices: Array<SpeakerSegment> = [];
    const failedSlices: Array<SpeakerSegment & { message: string, audioURL?: string }> = [];
    const successfulSlices: Array<SpeakerSegment & { audioURL: string, transcription: string }> = [];
    const promises: Array<Promise<any>> = [];

    for(let i = 0; i < result.length; i++){
      const slice = result[i];
      if(prevSegment === null){
        prevSegment = slice;
        continue;
      }
      if(prevSegment.speaker === slice.speaker) {
        prevSegment.end = slice.end;
        continue;
      }
      if(shouldSkip(prevSegment)){
        ignoredSlices.push(prevSegment);
        prevSegment = slice;
        continue;
      }

      promises.push(processSegment(
        transcriber, filePointer, prevSegment,
        segmentFilename(filename, slice),
        failedSlices, successfulSlices
      ));
      prevSegment = slice;
    }

    if(prevSegment !== null && !shouldSkip(prevSegment)){
      promises.push(processSegment(
        transcriber, filePointer, prevSegment,
        segmentFilename(filename, prevSegment),
        failedSlices, successfulSlices
      ));
    }

    await Promise.all(promises);



    try {
      await r2.deleteObject(r2Pointer);
    }catch(e){
      console.error("couldn't cleanup file");
    }

    res.statusCode = 200;
    res.json({
      ignored: ignoredSlices.sort(sortByStart),
      failed: failedSlices.sort(sortByStart),
      success: successfulSlices.sort(sortByStart),
    });
  }catch(error){
    next(error);
  }
};

import { parse as pathParse } from "path";
function segmentFilename(filename: string, segment: SpeakerSegment){
  const file = pathParse(filename);
  return `${file.name}-${
    segment.start.toString().replaceAll(".", "_")
  }-${
    segment.end.toString().replaceAll(".", "_")
  }${file.ext}`;
}

function sortByStart(a: SpeakerSegment, b: SpeakerSegment){
  return a.start - b.start;
}

import { Organizer } from "@divinci-ai/server-tools";
type ExtractApiType<T> = T extends Organizer<infer U> ? U : never;

function shouldSkip(segment: SpeakerSegment){
  return segment.end - segment.start < 0.5;
}

async function processSegment(
  transcriber: ExtractApiType<typeof SPEECH_TO_TEXT>,
  filePointer: { s3: typeof r2, Bucket: string, Key: string, originalName: string },
  sliceOriginal: { speaker: string, start: number, end: number },
  newFile: string,
  fails: Array<SpeakerSegment & { message: string, audioURL?: string }>,
  successes: Array<SpeakerSegment & { audioURL: string, transcription: string }>
){
  const slice: SpeakerSegment & { audioURL?: string } = { ...sliceOriginal };
  try {
    const audioSlice = await AUDIO_TOOLS.createSlice(
      filePointer, { start: slice.start, end: slice.end }
    );
    slice.audioURL = CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioSlice.Key;

    const transcription = await transcriber.processFile(
      {
        s3: filePointer.s3,
        ...audioSlice,
        originalName: newFile
      },
      { languageCode: "en-US" }
    );

    successes.push({
      ...slice,
      transcription,
      audioURL: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioSlice.Key
    });
  }catch(e: any){
    console.log("Failed to transcribe slice:", e);
    fails.push({ ...slice, message: e.message });
  }
}
