import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

export const listAudioTranscripts: RequestHandler = async function(req, res, next){
  try {
    const { target } = await getWhitelabelTarget(req);
    const pages = await DataSourceAudioTranscriptModel.aggregate([
      {
        // 1. Filter HTMLPages by host
        $match: { target }
      },
      populateRagFiles("ragFiles"),
      populateFineTune("finetuneFiles"),
    ]);


    res.statusCode = 200;
    res.json(pages);
  }catch(e){
    next(e);
  }
};

import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { Types } from "mongoose";

export const getAudioTranscript: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const audioId = getParam(req, "audioId");

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    // Consume the request body using busboy and log the results for debugging
    const [whitelabel, pages] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId).select("_id"),
      DataSourceAudioTranscriptModel.aggregate([
        {
          // 1. Filter HTMLPages by host
          $match: { _id: new Types.ObjectId(audioId), target }
        },
        populateRagFiles("ragFiles"),
        populateFineTune("finetuneFiles"),
      ])
    ]);

    const page = pages[0];

    if(whitelabel === null || !page){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(page);
  }catch(e){
    next(e);
  }
};

import { DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME } from "@divinci-ai/models";
import { RagVectorFileModel } from "@divinci-ai/server-models";
function populateRagFiles(key: string){
  return {
    $lookup: {
      from: RagVectorFileModel.collection.name, // The actual MongoDB collection name for your File model
      let: { sourceId: { $toString: "$_id" } },
      pipeline: [
        // 1) Match only files referencing the current HTMLPage
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$source.model", DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME] }, // Match model
                { $eq: ["$source._id", "$$sourceId"] }         // Match source._id
              ]
            }
          }
        },
        // 2) Project only the fields you need
        {
          $project: {
            _id: 1,
            title: 1,
            updateTimestamp: 1,
            status: 1,
            // any other File fields you want to include
          }
        }
      ],
      as: key
    }
  };
}

import { FineTuneFileModel } from "@divinci-ai/server-models";
function populateFineTune(key: string){
  return {
    $lookup: {
      from: FineTuneFileModel.collection.name, // The actual MongoDB collection name for your File model
      let: { sourceId: { $toString: "$_id" } },
      pipeline: [
        // 1) Match only files referencing the current HTMLPage
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ["$source.model", DATA_SOURCE_AUDIO_TRANSCRIPT_MODEL_NAME] }, // Match model
                { $eq: ["$source._id", "$$sourceId"] }         // Match source._id
              ]
            }
          }
        },
        // 2) Project only the fields you need
        {
          $project: {
            _id: 1,
            title: 1,
            // any other File fields you want to include
          }
        }
      ],
      as: key
    }
  };
}
