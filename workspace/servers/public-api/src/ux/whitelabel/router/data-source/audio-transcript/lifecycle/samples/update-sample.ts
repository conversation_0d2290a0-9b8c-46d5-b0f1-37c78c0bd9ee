import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const updateSampleAudioTranscript:RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const sampleId = getParam(req, "sampleId");
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);

    const newValues = castShallowObject(body, {
      speaker: "string", transcript: "string"
    });

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    const updated = await foundDoc.updateSample(sampleId, newValues);

    res.statusCode = 200;
    res.json(updated);

  }catch(e){
    next(e);
  }
};
