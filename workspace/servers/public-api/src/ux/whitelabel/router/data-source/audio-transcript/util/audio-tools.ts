
import { SPEAKER_DIARIZERS, SPEECH_TO_TEXT } from "@divinci-ai/server-tools";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

export function getAudioTools(
  { diarizer, transcriber }: {
    diarizer: string,
    transcriber: string,
  }
){
  return {
    diarizer: SPEAKER_DIARIZERS.tryToGet(diarizer),
    transcriber: SPEECH_TO_TEXT.tryToGet(transcriber),
  };
}

export type AudioTools = ReturnType<typeof getAudioTools>;

export function validMimetypesForTools(contentType: string, tools: AudioTools){
  if(!tools.diarizer.api.validMimetypes().includes(contentType)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid Mimetype for Diarizer");
  }
  if(!tools.transcriber.api.validMimetypes().includes(contentType)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid Mimetype for transcriber");
  }
}
