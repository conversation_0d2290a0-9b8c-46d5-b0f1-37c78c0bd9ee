import { SPEECH_TO_TEXT } from "@divinci-ai/server-tools";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

import { r2 } from "../../util/r2-constants";
import { handleDirectUrl } from "../../util/handle-url";
import { RequestHandler } from "express";

export const audioTranscription: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [body] = await Promise.all([jsonBody(req)]);
    const { url, transcriberTool } = castShallowObject(
      body, {
        url: "string" as const, transcriberTool: "string" as const
      }
    );

    const transcriber = SPEECH_TO_TEXT.tryToGetTool(transcriberTool);

    const { r2Pointer, filename } = await handleDirectUrl(
      whitelabelId, new URL(url)
    );

    const result = await transcriber.processFile(
      {
        s3: r2,
        ...r2Pointer,
        originalName: filename
      },
      { languageCode: "en-US" }
    );

    try {
      await r2.deleteObject(r2Pointer);
    }catch(e){
      console.error("couldn't cleanup file");
    }

    res.statusCode = 200;
    res.json(result);
  }catch(error){
    next(error);
  }
};
