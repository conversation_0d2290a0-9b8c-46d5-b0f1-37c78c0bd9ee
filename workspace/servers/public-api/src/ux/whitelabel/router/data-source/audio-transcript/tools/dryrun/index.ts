import { Router } from "express";

export const router = Router({ mergeParams: true });
import { speakerDiarization } from "./speaker-diarization";
router.post("/diarize", speakerDiarization);

import { sliceAudio } from "./audio-slice";
router.post("/slice", sliceAudio);

import { audioTranscription } from "./transcription";
router.post("/transcribe", audioTranscription);

import { audioSliceTranscribe } from "./slice-transcribe";
router.post("/slice-transcribe", audioSliceTranscribe);

import { diarizeSliceTranscribe } from "./diarize-slice-transcibe";
router.post("/", diarizeSliceTranscribe);

