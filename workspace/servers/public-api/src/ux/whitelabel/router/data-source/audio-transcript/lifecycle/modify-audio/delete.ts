import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";

export const deleteAudioTranscript: RequestHandler = async (req, res, next) => {
  try {
    const audioId = getParam(req, "audioId");
    const { target } = await getWhitelabelTarget(req);
    const doc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!doc) throw HTTP_ERRORS.NOT_FOUND;

    await doc.deleteOne();

    res.statusCode = 200;
    res.json(doc);
  }catch(e){
    next(e);
  }
};
