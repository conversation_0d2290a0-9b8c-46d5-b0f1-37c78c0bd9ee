import { AUDIO_TOOLS, SPEECH_TO_TEXT } from "@divinci-ai/server-tools";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

import { r2, CLOUDFLARE_AUDIO_PUBLIC_URL } from "../../util/r2-constants";
import { handleDirectUrl } from "../../util/handle-url";
import { RequestHandler } from "express";


export const audioSliceTranscribe: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const [body] = await Promise.all([jsonBody(req)]);

    const { url, start, end, transcriberTool } = castShallowObject(
      body, {
        url: "string", start: "number", end: "number",
        transcriberTool: "string"
      }
    );

    const transcriber = SPEECH_TO_TEXT.tryToGetTool(transcriberTool);

    const { r2Pointer, filename } = await handleDirectUrl(
      whitelabelId, new URL(url)
    );

    const result = await AUDIO_TOOLS.createSlice(
      r2Pointer,
      { start, end }
    );

    console.log("slice audio:", CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + result.Key);

    const transcription = await transcriber.processFile(
      {
        s3: r2,
        ...result,
        originalName: filename
      },
      { languageCode: "en-US" }
    );

    try {
      await r2.deleteObject(r2Pointer);
    }catch(e){
      console.error("couldn't cleanup file");
    }

    res.statusCode = 200;
    res.json({
      Bucket: result.Bucket, Key: result.Key,
      publicURL: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + result.Key,
      transcription
    });
  }catch(error){
    next(error);
  }
};
