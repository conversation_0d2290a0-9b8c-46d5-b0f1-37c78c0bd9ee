import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../util/whitelabel";
import { getAudioTools } from "../../util/audio-tools";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const reprocessAudioTranscript: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);
    const { diarizerTool, transcriberTool } = castShallowObject(
      body, {
        diarizerTool: "string", transcriberTool: "string"
      }
    );

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    const tools = getAudioTools({
      diarizer: diarizerTool,
      transcriber: transcriberTool,
    });

    const { doc: updatedDoc } = await foundDoc.reprocessTranscript(tools);

    res.statusCode = 200;
    res.json(updatedDoc);
  }catch(e){
    next(e);
  }
};
