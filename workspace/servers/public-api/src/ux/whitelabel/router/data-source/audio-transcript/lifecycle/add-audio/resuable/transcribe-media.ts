
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";
import { getAudioTools } from "../../../util/audio-tools";
import { CLOUDFLARE_AUDIO_PUBLIC_URL } from "../../../util/r2-constants";
import { AUDIO_TOOLS } from "@divinci-ai/server-tools";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { getS3Info, copyOriginalToS3 } from "./s3-copy-original";
import { ensureValidMp3 } from "./ensure-valid-mp3";

export async function transcribeMedia(
  target: string, whitelabelId: string,
  toolIds: { diarizer: string, transcriber: string },
  originalS3Location: { Bucket: string, Key: string },
  sourceInfo: { filename: string, sourceType: "url" | "file", rawValue: string },
  copiedLocation?: { Bucket: string, Key: string }
){
  const convertArgs = await AUDIO_TOOLS.canConvertToMp3(sourceInfo.filename);
  if(!convertArgs.support){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }

  // Implement the logic to transcribe the video
  const tools = getAudioTools({
    diarizer: toolIds.diarizer,
    transcriber: toolIds.transcriber,
  });


  const { etag, contentLength } = await getS3Info(originalS3Location);
  const [audioS3Location, rawS3Location] = await Promise.all([
    ensureValidMp3(originalS3Location, whitelabelId),
    copiedLocation ? copiedLocation : copyOriginalToS3(originalS3Location, whitelabelId),
  ]);


  // Create a pending transcript record
  const { doc } = await DataSourceAudioTranscriptModel.createTranscript(
    target, tools, {
      sourceType: sourceInfo.sourceType,
      mediaType: convertArgs.type,
      sourceId: `${etag}-${contentLength}`,
      info: {
        rawValue: sourceInfo.rawValue,
        rawKey: rawS3Location.Key,
        rawBucket: rawS3Location.Bucket,
        fileType: convertArgs.type,
        filename: sourceInfo.filename,
        hash: etag,
        filesize: contentLength,
      }
    },
    {
      Bucket: audioS3Location.Bucket,
      Key: audioS3Location.Key,
      filename: sourceInfo.filename,
      publicUrl: CLOUDFLARE_AUDIO_PUBLIC_URL + "/" + audioS3Location.Key
    }
  );

  // Start the workflow to process the video
  /*
  await startVideoToAudioWorkflow({
    videoFile: {
      fileId: doc._id.toString(),
      objectKey: originalS3Location.Key,
      fileName: fileInfo.filename,
      title: "",
      description: ""
    },
    instanceId: uniqueId(),
    whitelabelId: whitelabelId,
    auth0Token: authToken
  });
  */

  return doc;
}


