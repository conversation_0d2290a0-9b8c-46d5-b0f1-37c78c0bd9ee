import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getWhitelabelTarget } from "../../util/whitelabel";
import { DataSourceAudioTranscriptModel } from "@divinci-ai/server-models";

import { getParam, HTTP_ERRORS, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const updateAudioUserInfo: RequestHandler = async function(req, res, next){
  try {
    const audioId = getParam(req, "audioId");
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req), jsonBody(req),
    ]);

    const { title, description } = castShallowObject(
      body, { title: "string", description: "string" }
    );

    const foundDoc = await DataSourceAudioTranscriptModel.findOne({
      target, _id: audioId
    });

    if(!foundDoc) throw HTTP_ERRORS.NOT_FOUND;

    foundDoc.userInfo = { title, description };

    await foundDoc.save();

    res.statusCode = 200;
    res.json(foundDoc);

  }catch(e){
    next(e);
  }
};
