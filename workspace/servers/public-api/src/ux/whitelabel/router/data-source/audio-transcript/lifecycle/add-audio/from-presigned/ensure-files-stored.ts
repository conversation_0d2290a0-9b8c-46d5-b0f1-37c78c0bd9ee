
import { r2 } from "../../../util/r2-constants";
import { extractFilesFromZip } from "@divinci-ai/server-utils";

const VIDEO_EXTENSIONS = [".mp4", ".mov", ".avi", ".mkv", ".webm", ".flv", ".wmv", ".m4v"];

export async function ensureFilesStored(files: Array<{
  Bucket: string,
  Key: string,
  filename: string,
  contentType?: string,
  size?: number,
}>){
  const allVideoFiles: Array<{
    Bucket: string,
    Key: string,
    filename: string,
    contentType?: string,
    size?: number,
  }> = [];

  await Promise.all(files.map(async (file)=>{
    // If it's not a zip file, handle it individually
    if(file.contentType !== "application/zip" && file.Key.toLowerCase().endsWith(".zip")) {
      // It's a video file, push it
      if(VIDEO_EXTENSIONS.some(ext=>file.Key.toLowerCase().endsWith(ext))){
        allVideoFiles.push(file);
      }
      return;
    }
    const extractedVideos = await extractFilesFromZip(
      r2, file, VIDEO_EXTENSIONS
    );
    allVideoFiles.push(...extractedVideos);
  }));

  return allVideoFiles;
}
