import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { Types } from "mongoose";
import { condenseTarget, unravelTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import {
  getUserId,
  getUsersActiveGroup,
} from "@divinci-ai/server-globals";

import {
  UserGroupModel,
  DocPermissionModel,
  WhiteLabelModel,
} from "@divinci-ai/server-models";

import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

const WHITELABEL_BEGINNING = new RegExp(`^${condenseTarget({ ...WHITE_LABEL_LOCATION, id: "" })}`);
export const listUserPermitted: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);

    const permissions = await DocPermissionModel.find({
      target: { $regex: WHITELABEL_BEGINNING },
      [`userPermissions.${userId}.userId`]: userId
    }).select("target");

    const whitelabelList = await WhiteLabelModel.getWhitelabelMeta(
      await WhiteLabelModel.find({
        _id: { $in: permissionsToIds(permissions) }
      }).exec()
    );

    res.statusCode = 200;
    res.json(whitelabelList);
  }catch(e){
    next(e);
  }
};

export const listGroupPermitted: RequestHandler = async function(req, res, next){
  try {
    const userId = getUserId(req);
    const groupId = getUsersActiveGroup(req);

    const group = await UserGroupModel.findOne({
      _id: new Types.ObjectId(groupId),
      "users.userId": userId
    }).select(["_id", "slug"]);
    if(!group){
      throw HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN("No access to organization");
    }

    const permissions = await DocPermissionModel.find({
      target: { $regex: WHITELABEL_BEGINNING },
      [`group.${group.slug}.groupSlug`]: group.slug
    }).select("target");

    const whitelabelList = await WhiteLabelModel.getWhitelabelMeta(
      await WhiteLabelModel.find({
        _id: { $in: permissionsToIds(permissions) }
      }).exec()
    );

    res.statusCode = 200;
    res.json(whitelabelList);
  }catch(e){
    next(e);
  }
};

function permissionsToIds(permissions: Array<InstanceType<typeof DocPermissionModel>>){
  const ids: Array<Types.ObjectId> = [];
  for(const permission of permissions){
    ids.push(new Types.ObjectId(unravelTarget(permission.target).id));
  }
  return ids;
}
