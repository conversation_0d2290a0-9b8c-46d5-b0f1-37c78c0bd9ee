import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK, jsonBody } from "@divinci-ai/server-utils";
import { castToObject } from "@divinci-ai/utils";
import mongoose from "mongoose";

export const updateRagVectorFileStatus: RequestHandler = async function(req, res, next){
  try {
    const fileId = getParam(req, "fileId");
    const reqBody = castToObject(await jsonBody(req));
    const { status } = reqBody;

    console.log("🔍 Updating file status:", { fileId, status });

    // Create a query with proper typing
    type QueryCondition = { fileId: string } | { _id: string };
    const query: { $or: QueryCondition[] } = { $or: [{ fileId }] };

    // Only add _id to query if it could be a valid ObjectId
    if(mongoose.Types.ObjectId.isValid(fileId)) {
      query.$or.push({ _id: fileId });
    }

    const updated = await RagVectorFileModel.findOneAndUpdate(
      query,
      { status },
      { new: true }
    );

    if(!updated) {
      console.error("❌ File not found:", { fileId, status });
      throw new HTTP_ERRORS_WITH_STACK.NOT_FOUND(`File not found: ${fileId}`);
    }

    console.log("✅ File status updated:", { fileId, status });
    res.json({ status: "success" });
  }catch(error) {
    console.error("❌ Error updating file status:", { error });
    next(error);
  }
};
