import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { RagVectorFileModel, RagVectorTextChunksStatus } from "@divinci-ai/server-models";
import { getParam } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { createShallowCaster } from "@divinci-ai/utils";
import multer from "multer";

interface FileInfo {
  title: string,
  description?: string,
}

// This interface is for TypeScript documentation
interface WorkflowRequestBody {
  files: FileInfo[],
  title: string,
  description: string,
  processorConfig: {
    chunkingTool: string,
    openparse?: {
      semantic?: boolean,
      embeddings?: {
        provider?: string,
      },
      minTokens?: number,
      maxTokens?: number,
    },
  },
  vectorizeConfig: {
    accountId: string,
    apiToken: string,
    projectName: string,
  },
}

// This is what we'll use for the actual casting
interface CastedWorkflowBody {
  files: string[],
  title: string,
  description: string,
  processorConfig: string,
  vectorizeConfig: string,
  [key: string]: string | number | boolean | string[] | number[] | boolean[],
}

const caster = createShallowCaster({
  files: "string[]",
  title: "string",
  description: "string",
  processorConfig: "string",
  vectorizeConfig: "string"
});

// Configure multer for file upload
const upload = multer({ storage: multer.memoryStorage() });

// First, define the handler separately
const handleFileWorkflow: RequestHandler = async (req, res, next)=>{
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const file = req.file;
    const title = req.body.title;
    const description = req.body.description || "";
    const processorConfig = JSON.parse(req.body.processorConfig);
    const vectorizeConfig = JSON.parse(req.body.vectorizeConfig);

    if(!file) {
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("No file uploaded");
    }

    console.log("📝 Processing file:", {
      title,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size
    });

    // Create file record without R2 operations
    const savedFile = await RagVectorFileModel.create({
      target,
      rawFileKey: `${target}/${crypto.randomUUID()}/${file.originalname}`,
      originalName: file.originalname,
      processorType: processorConfig.chunkingTool,
      title: title || file.originalname,
      description,
      status: RagVectorTextChunksStatus.CHUNKING  // Using the correct enum value
    });

    // Prepare workflow payload
    const workflowPayload = {
      files: [{
        fileId: savedFile._id.toString(),
        target,
        fileName: file.originalname,
        bucket: process.env.R2_BUCKET_NAME || "whitelabel-vector-index-local",
        objectKey: savedFile.rawFileKey,
        processor: processorConfig.chunkingTool,
        processorConfig: {
          semantic_chunking: processorConfig.openparse?.semantic ?? false,
          embeddings_provider: processorConfig.openparse?.embeddings?.provider || "cloudflare",
          minTokens: processorConfig.openparse?.minTokens,
          maxTokens: processorConfig.openparse?.maxTokens
        },
        fileData: `data:${file.mimetype};base64,${file.buffer.toString("base64")}` // Include mimetype in base64 data
      }],
      vectorizeConfig: {
        ...vectorizeConfig,
        projectName: vectorizeConfig.projectName || `${target}-vectors`,
        ragId: savedFile._id.toString()
      }
    };

    if(!process.env.CHUNKS_VECTORIZED_WORKFLOW_URL) {
      throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("❌ Workflow URL not configured. Please check your environment variables.");
    }

    // Forward to Cloudflare Worker
    const workflowResponse = await fetch(`${process.env.CHUNKS_VECTORIZED_WORKFLOW_URL}/api/process-document`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(workflowPayload)
    });

    if(!workflowResponse.ok) {
      const errorText = await workflowResponse.text();
      throw new Error(`Workflow request failed (${workflowResponse.status}): ${errorText}`);
    }

    const workflowResult = await workflowResponse.json();
    console.log("✅ Workflow started: ", workflowResult);

    return res.json({
      success: true,
      file: savedFile,
      workflow: workflowResult
    });
  }catch(error) {
    console.error("❌ Error in addRagVectorFileWorkflow:", error);
    next(error);
  }
};

// Then compose with multer middleware
export const addRagVectorFileWorkflow = [
  upload.single("file"),
  handleFileWorkflow
] as RequestHandler[];
