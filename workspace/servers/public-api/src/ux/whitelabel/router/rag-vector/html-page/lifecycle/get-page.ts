import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";

import { RAG_VECTOR_HTMLPAGE_MODEL_NAME } from "@divinci-ai/models";
import { RagVectorHTMLPageModel, RagVectorFileModel } from "@divinci-ai/server-models";
import { getWhitelabelTarget, castURL } from "../util";

export const getUniqueHosts: RequestHandler = async function(req, res, next){
  try {
    const { target } = await getWhitelabelTarget(req);
    const urlHosts = await RagVectorHTMLPageModel.distinct("urlHost", { target });
    res.statusCode = 200;
    res.json(urlHosts);
  }catch(e){
    next(e);
  }
};

export const getPageByURL: RequestHandler = async function(req, res, next){
  try {
    const { target } = await getWhitelabelTarget(req);
    const urlString = req.query.url as string;
    if(!urlString){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("url is required");
    }

    const url = castURL(urlString);
    url.hash = "";
    url.search = "";

    const page = await RagVectorHTMLPageModel.findOne({ target, url: url.href });

    if(page === null) throw HTTP_ERRORS.NOT_FOUND;

    res.statusCode = 200;
    res.json(page);

  }catch(e){
    next(e);
  }
};

export const listPages: RequestHandler = async function(req, res, next){
  try {
    const { target } = await getWhitelabelTarget(req);
    const matchConfig: { target: string, urlHost?: string | { $in: Array<string> } } = { target };
    if(Array.isArray(req.query.urlHost)){
      if(req.query.urlHost.length > 0){
        matchConfig.urlHost = { $in: req.query.urlHost.map((value)=>{
          if(typeof value === "string") return value;
          throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("urlHost items must be a string");
        }) };
      }
      console.log("urlHost Array:", matchConfig.urlHost);
    } else if(typeof req.query.urlHost === "string"){
      matchConfig.urlHost = req.query.urlHost;
      console.log("urlHost String:", matchConfig.urlHost);
    }

    const pages = await RagVectorHTMLPageModel.aggregate([
      {
        // 1. Filter HTMLPages by host
        $match: matchConfig
      },
      populateFilesOfHTML("files"),
    ]);


    res.statusCode = 200;
    res.json(pages);
  }catch(e){
    next(e);
  }
};

import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { WhiteLabelModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { Types } from "mongoose";

export const getPage: RequestHandler = async function(req, res, next){
  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const pageId = getParam(req, "pageId");

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    // Consume the request body using busboy and log the results for debugging
    const [whitelabel, pages] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId).select("_id"),
      RagVectorHTMLPageModel.aggregate([
        {
          // 1. Filter HTMLPages by host
          $match: { _id: new Types.ObjectId(pageId), target }
        },
        populateFilesOfHTML("files"),
      ])
    ]);

    const page = pages[0];

    if(whitelabel === null || !page){
      throw HTTP_ERRORS.NOT_FOUND;
    }

    res.statusCode = 200;
    res.json(page);
  }catch(e){
    next(e);
  }
};


function populateFilesOfHTML(key: string){
  return {
    $lookup: {
      from: RagVectorFileModel.collection.name, // The actual MongoDB collection name for your File model
      let: { sourceId: { $toString: '$_id' } },
      pipeline: [
        // 1) Match only files referencing the current HTMLPage
        {
          $match: {
            $expr: {
              $and: [
                { $eq: ['$source.model', RAG_VECTOR_HTMLPAGE_MODEL_NAME] }, // Match model
                { $eq: ['$source._id', '$$sourceId'] }         // Match source._id
              ]
            }
          }
        },
        // 2) Project only the fields you need
        {
          $project: {
            _id: 1,
            title: 1,
            updateTimestamp: 1,
            status: 1,
            // any other File fields you want to include
          }
        }
      ],
      as: key
    }
  };
}