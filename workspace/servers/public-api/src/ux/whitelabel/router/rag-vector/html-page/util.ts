import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";
import { WhiteLabelModel, RagVectorHTMLPageModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS, HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";
import { IncomingMessage } from "http";

type MongooseSelect = Array<string> | Record<string, 0 | 1> | string;

export function castURL(urlString: string){
  try {
    const url = new URL(urlString);
    if(url.pathname.at(-1) === "/") url.pathname = url.pathname.slice(0, -1);
    return url;
  }catch(e){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid URL");
  }
}

export async function getWhitelabelTarget(req: IncomingMessage){
  const whitelabelId = getParam(req, "whitelabelId");

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });

  const whitelabel = await WhiteLabelModel.findById(whitelabelId).select("_id");
  if(whitelabel === null){
    throw HTTP_ERRORS.NOT_FOUND;
  }

  return { whitelabel, target };
}

export async function ensureValidScrapedPage(req: IncomingMessage, select: MongooseSelect = { chunks: 0 }){
  const whitelabelId = getParam(req, "whitelabelId");
  const pageId = getParam(req, "pageId");

  const target = condenseTarget({
    ...WHITE_LABEL_LOCATION,
    id: whitelabelId,
  });

  // Consume the request body using busboy and log the results for debugging
  const [whitelabel, page] = await Promise.all([
    WhiteLabelModel.findById(whitelabelId).select("_id"),
    RagVectorHTMLPageModel.findOne({ _id: pageId, target }).select(select),
  ]);

  if(whitelabel === null || page === null){
    throw HTTP_ERRORS.NOT_FOUND;
  }

  return { whitelabel, page };

}
