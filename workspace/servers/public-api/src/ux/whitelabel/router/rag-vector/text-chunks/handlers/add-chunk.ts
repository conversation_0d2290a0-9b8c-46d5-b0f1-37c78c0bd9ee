import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonB<PERSON> } from "@divinci-ai/server-utils";
import { createShallowCaster } from "@divinci-ai/utils";

import { TextChunksConfig } from "../types";

const addCaster = createShallowCaster({
  text: "string", tags: "string[]", offset: "number?"
});
export function addChunk(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      const [textChunks, values] = await Promise.all([
        config.resolveTextChunks(req),
        jsonBody(req).then(addCaster),
      ]);

      const newChunk = await textChunks.addChunk({ text: values.text, tags: values.tags }, values.offset);

      res.status(200).json({ status: "ok", chunk: newChunk });

    }catch(error){
      next(error);
    }
  };
}
