import { SCRAPER_ASSISTANTS, RAWFILE_TO_CHUNKS } from "@divinci-ai/server-tools";
import { Router } from "express";
import { HTTP_ERRORS_WITH_CONTEXT, jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { crawlUrl } from "@divinci-ai/tools";
export const router = Router({ mergeParams: true });

router.get("/", async (req, res, next)=>{
  try {
    res.statusCode = 200;
    res.json({
      scrapers: SCRAPER_ASSISTANTS.getAllInfo(),
      chunkers: RAWFILE_TO_CHUNKS.getAllInfo(),
    });
  }catch(e){
    next(e);
  }
});

router.post("/crawl", async (req, res, next)=>{
  try {
    const [body] = await Promise.all([jsonBody(req)]);
    const { tool: toolSlug, url, limit, excludePaths, allowedPaths, runSitemap } = castShallowObject(
      body, {
        tool: "string", url: "string", limit: "number", runSitemap: "boolean",
        excludePaths: "string[]", allowedPaths: "string[]"
      }
    );

    try {
      new URL(url);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid URL");
    }

    if(!SCRAPER_ASSISTANTS.validTool(toolSlug)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid Tool");
    }
    if(limit < 1){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Limit must be at least 1");
    }
    const tool = SCRAPER_ASSISTANTS.getTool(toolSlug);
    const result = await crawlUrl(url, tool.scrapeUrl, {
      limit,
      runSitemap,
      excludePaths: excludePaths.length === 0 ? void 0 : excludePaths,
      allowedPaths: allowedPaths.length === 0 ? void 0 : allowedPaths,
    });
    res.statusCode = 200;
    res.json(result);
  }catch(error){
    next(error);
  }
});

router.post("/scrape", async (req, res, next)=>{
  try {
    const [body] = await Promise.all([jsonBody(req)]);
    const { tool: toolSlug, url } = castShallowObject(
      body, { tool: "string", url: "string" }
    );
    if(!SCRAPER_ASSISTANTS.validTool(toolSlug)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid Tool");
    }
    const tool = SCRAPER_ASSISTANTS.getTool(toolSlug);
    const result = await tool.scrapeUrl(url);
    res.statusCode = 200;
    res.json(result);
  }catch(error){
    next(error);
  }
});