import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { RagVectorHTMLPageModel } from "@divinci-ai/server-models";
import { getWhitelabelTarget } from "../util";
import { jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";

export const scrapeUrl: RequestHandler = async function(req, res, next){
  try {
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req),
    ]);

    const { url, scraperTool, chunkerTool, shouldChunk, addToRagVector } = castShallowObject(
      body, {
        url: "string", scraperTool: "string",
        chunkerTool: "string", shouldChunk: "boolean",
        addToRagVector: "string[]",
      }
    );

    const result = await RagVectorHTMLPageModel.scrapeURL(
      target, url, { scraperTool, chunkerTool, shouldChunk, addToRagVector }
    );

    res.statusCode = 200;
    res.json(result);
  }catch(e){
    next(e);
  }
};

export const crawlUrl: RequestHandler = async function(req, res, next){
  try {
    const [{ target }, body] = await Promise.all([
      getWhitelabelTarget(req),
      jsonBody(req),
    1]);

    const {
      url, scraperTool,
      ignoreSavedPaths, limit, runSitemap, minCacheDate,
      excludePaths, allowedPaths,
      chunkerTool, shouldChunk,
      addToRagVector,
    } = castShallowObject(
      body, {
        url: "string", scraperTool: "string",
        ignoreSavedPaths: "boolean", limit: "number", runSitemap: "boolean", minCacheDate: "number?",
        excludePaths: "string[]", allowedPaths: "string[]",
        chunkerTool: "string", shouldChunk: "boolean",
        addToRagVector: "string[]",
      }
    );

    const result = await RagVectorHTMLPageModel.crawlURL(
      target, url, {
        scraperTool,
        limit,
        ignoreSavedPaths,
        minCacheDate,
        allowedPaths, excludePaths,
        runSitemap,
        chunkerTool, shouldChunk,
        addToRagVector
      }
    );

    res.statusCode = 200;
    res.json(result);
  }catch(e){
    next(e);
  }
};
