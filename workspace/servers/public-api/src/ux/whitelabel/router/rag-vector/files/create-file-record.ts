import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK, jsonBody } from "@divinci-ai/server-utils";
import { castToObject, castShallowObject } from "@divinci-ai/utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const createFileRecord: RequestHandler = async function(req, res, next){
  console.log("📝 Create file record endpoint hit", {
    method: req.method,
    body: req.body,
    params: req.params
  });

  // Ensure this is a POST request
  if(req.method !== "POST") {
    return next(new HTTP_ERRORS_WITH_STACK.BAD_FORM("❌ Only POST method is allowed."));
  }

  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const reqBody = castToObject(await jsonBody(req));

    console.log("📦 Request body received:", reqBody);

    const { bucket, objectKey, originalName, chunkingTool, title, description } = castShallowObject(
      reqBody,
      {
        bucket: "string",
        objectKey: "string",
        originalName: "string",
        chunkingTool: "string",
        title: "string",
        description: "string",
      },
      new HTTP_ERRORS_WITH_STACK.BAD_FORM("Invalid JSON body"),
    );

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const { doc: fileRecord } = await RagVectorFileModel.createFileRecord(
      target,
      {
        bucket,
        objectKey,
        originalName,
      },
      chunkingTool,
      {
        title,
        description
      }
    );

    console.log("✅ File record created:", fileRecord);
    res.json({ status: "success", data: fileRecord });
  }catch(error) {
    console.error("❌ Error creating file record:", error);
    next(error);
  }
};
