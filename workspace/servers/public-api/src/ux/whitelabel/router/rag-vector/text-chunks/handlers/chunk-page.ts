import { RequestH<PERSON><PERSON>, Request } from "express";
import { createShallowCaster } from "@divinci-ai/utils";

import { TextChunksConfig } from "../types";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";


const getCaster = createShallowCaster({
  offset: "number", length: "number"
});
export function getChunks(config: TextChunksConfig): RequestHandler{
  return  async function(req, res, next){
    try {
      const offset = castToNumber(req.query, "offset");
      const length = castToNumber(req.query, "length");

      const query = getCaster({ offset, length });
      const textChunks = await config.resolveTextChunks(req);

      const chunks = await textChunks.getChunks(query.offset, query.length);

      res.status(200).json(chunks);

    }catch(error){
      next(error);
    }
  };
}

function castToNumber(query: Request["query"], key: string): number{
  const str = query[key];
  if(typeof str !== "string"){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(`${key} is expected to be a number`);
  }
  const num = Number.parseInt(str);
  if(Number.isNaN(num)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(`${key} is expected to be a number`);
  }
  return num;
}