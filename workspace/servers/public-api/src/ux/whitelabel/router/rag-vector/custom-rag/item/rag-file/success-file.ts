import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonBody } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { RagVectorModel } from "@divinci-ai/server-models";
import { getItemFromIdRequest } from "../../../../util";
import { updateSuccessAtomic } from "@divinci-ai/server-models";

export const successFileToRag: RequestHandler = async function(req, res, next){
  try {
    const [{ item }, body] = await Promise.all([
      getItemFromIdRequest(req, "ragId", RagVectorModel),
      jsonBody(req),
    ]);

    const { fileId } = castShallowObject(body, {
      fileId: "string"
    });

    console.log("Moving file to success:", { ragId: item._id, fileId });
    await updateSuccessAtomic(item, fileId);

    res.status(200).json({ status: "ok" });
  }catch(e) {
    next(e);
  }
};