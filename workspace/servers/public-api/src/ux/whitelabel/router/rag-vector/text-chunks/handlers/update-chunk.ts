import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { createShallowCaster, castToObject } from "@divinci-ai/utils";

import { TextChunksConfig } from "../types";

const updateCaster = createShallowCaster({
  text: "string",
  tags: "string[]"
});

export function updateChunk(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      const chunkId = getParam(req, "chunkId");
      const body = castToObject(await jsonBody(req));

      // Validate that chunks exist and are not empty
      if(!body.chunks || !Array.isArray(body.chunks) || body.chunks.length === 0){
        return res.status(400).json({
          status: "error",
          message: "No valid chunks provided",
          context: {}
        });
      }

      const [textChunks, chunkInfo] = await Promise.all([
        config.resolveTextChunks(req),
        Promise.all(body.chunks.map((chunk)=>(updateCaster(chunk))))
      ]);

      // Update each chunk
      await Promise.all(
        chunkInfo.map((chunk)=>(
          textChunks.updateChunk({
            _id: chunkId,
            text: chunk.text,
            tags: chunk.tags
          })
        ))
      );

      res.status(200).json({ status: "ok" });
    }catch(error){
      next(error);
    }
  };
}
