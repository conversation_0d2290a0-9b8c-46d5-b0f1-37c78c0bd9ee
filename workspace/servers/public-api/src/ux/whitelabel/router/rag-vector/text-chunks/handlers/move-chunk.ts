import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam, jsonBody } from "@divinci-ai/server-utils";
import { createShallowCaster } from "@divinci-ai/utils";

import { TextChunksConfig } from "../types";

const moveCaster = createShallowCaster({
  to: "number"
});
export function moveChunk(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      const chunkId = getParam(req, "chunkId");
      const [textChunks, { to }] = await Promise.all([
        config.resolveTextChunks(req),
        jsonBody(req).then(moveCaster),
      ]);

      await textChunks.moveChunkById(chunkId, to);

      res.status(200).json({ status: "ok" });
    }catch(error){
      next(error);
    }
  };
}
