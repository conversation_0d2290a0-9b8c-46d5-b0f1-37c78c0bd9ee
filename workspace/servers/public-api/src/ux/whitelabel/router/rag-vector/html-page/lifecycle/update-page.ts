import { Request<PERSON><PERSON><PERSON> } from "express";
import { jsonBody } from "@divinci-ai/server-utils";

import { castShallowObject } from "@divinci-ai/utils";
import { ensureValidScrapedPage } from "../util";

export const createPageFile: RequestHandler = async (req, res, next)=>{
  try {
    const [{ page }, body] = await Promise.all([
      ensureValidScrapedPage(req),
      jsonBody(req),
    ]);

    const { chunkerTool } = castShallowObject(
      body, { chunkerTool: "string" }
    );

    const { doc: fileDoc } = await page.createFile(chunkerTool);

    res.statusCode = 201;
    res.json({ page, file: fileDoc });
  }catch(e){
    next(e);
  }
};

export const cleanupPageFiles: RequestHandler = async (req, res, next)=>{
  try {
    const { page } = await ensureValidScrapedPage(req);

    const result = await page.cleanupFiles();

    res.statusCode = 200;
    res.json(result);
  }catch(e){
    next(e);
  }
};

export const deletePage: RequestHandler = async (req, res, next)=>{
  try {
    const { page } = await ensureValidScrapedPage(req);

    await page.deleteOne();

    res.statusCode = 200;
    res.json({ page });
  }catch(e){
    next(e);
  }
};
