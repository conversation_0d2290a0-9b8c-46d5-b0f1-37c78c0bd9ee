import { Router } from "express";

export const router = Router({ mergeParams: true });

import { scrapeUrl, crawlUrl } from "./add-url";

router.post("/scrape", scrapeUrl);
router.post("/crawl", crawlUrl);

import { listPages, getUniqueHosts, getPageByURL, getPage } from "./get-page";
router.get("/", listPages);
router.get("/hosts", getUniqueHosts);
router.get("/url", getPageByURL);
router.get("/:pageId", getPage);

import { createPageFile, cleanupPageFiles, deletePage } from "./update-page";

router.post("/:pageId/file", createPageFile);
router.delete("/:pageId/file", cleanupPageFiles);
router.delete("/:pageId", deletePage);
