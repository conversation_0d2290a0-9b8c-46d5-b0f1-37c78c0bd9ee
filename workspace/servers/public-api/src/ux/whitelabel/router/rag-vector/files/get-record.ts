import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { RagVectorFileModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS_WITH_STACK } from "@divinci-ai/server-utils";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

export const getRagVectorFileRecord: RequestHandler = async function(req, res, next){
  console.log("🔍 Get file record endpoint hit");

  try {
    const whitelabelId = getParam(req, "whitelabelId");
    const objectKey = req.query.objectKey as string;

    if(!objectKey) {
      throw new HTTP_ERRORS_WITH_STACK.BAD_FORM("objectKey query parameter is required");
    }

    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });

    const fileRecord = await RagVectorFileModel.findOne({
      target,
      rawFileKey: objectKey
    });

    if(!fileRecord) {
      return res.json({ status: "success", data: null });
    }

    console.log("✅ File record found:", fileRecord);
    res.json({ status: "success", data: fileRecord });
  }catch(error) {
    console.error("❌ Error getting file record:", error);
    next(error);
  }
};
