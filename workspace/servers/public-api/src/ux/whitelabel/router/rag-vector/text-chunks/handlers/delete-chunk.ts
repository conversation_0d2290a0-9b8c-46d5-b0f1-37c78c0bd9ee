import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { getParam } from "@divinci-ai/server-utils";

import { TextChunksConfig } from "../types";


export function deleteChunk(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      const chunkId = getParam(req, "chunkId");
      const textChunks = await config.resolveTextChunks(req);

      await textChunks.deleteChunk(chunkId);

      res.status(200).json({ status: "ok" });
    }catch(error){
      next(error);
    }
  };
}
