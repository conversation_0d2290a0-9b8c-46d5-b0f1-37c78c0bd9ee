import { Router } from "express";

import {
  addChunk, updateChunk, moveChunk,
  deleteChunk, finalizeChunks, addChunks,
} from "./handlers";
import { TextChunksConfig } from "./types";

export function createTextChunksRouter(
  config: TextChunksConfig,
  router = Router({ mergeParams: true })
){

  router.post("/finalize", finalizeChunks(config));

  router.post("/", addChunk(config));
  router.post("/bulk", addChunks(config));
  router.post("/:chunkId", updateChunk(config));
  router.post("/:chunkId/move", moveChunk(config));
  router.delete("/:chunkId", deleteChunk(config));

  return router;
}
