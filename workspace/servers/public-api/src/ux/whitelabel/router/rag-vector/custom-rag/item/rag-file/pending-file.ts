import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonB<PERSON> } from "@divinci-ai/server-utils";
import { castShallowObject } from "@divinci-ai/utils";
import { RagVectorModel } from "@divinci-ai/server-models";
import { getItemFromIdRequest } from "../../../../util";
import { updatePendingAtomic } from "@divinci-ai/server-models";

export const pendingFileToRag: RequestHandler = async function(req, res, next){
  try {
    const [{ item }, body] = await Promise.all([
      getItemFromIdRequest(req, "ragId", RagVectorModel),
      jsonBody(req),
    ]);

    const { fileId } = castShallowObject(body, {
      fileId: "string"
    });

    console.log("Adding file to pending:", { ragId: item._id, fileId });
    await updatePendingAtomic(item, fileId);

    res.status(200).json({ status: "ok" });
  }catch(e) {
    next(e);
  }
};