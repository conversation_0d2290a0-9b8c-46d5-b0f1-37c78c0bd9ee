import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import {
  WhiteLabelModel,
} from "@divinci-ai/server-models";
import {
  getParam, HTTP_ERRORS, CastedBusBoy,
  HTTP_ERRORS_WITH_CONTEXT,
} from "@divinci-ai/server-utils";

import { fileHandler } from "../file-handler";
import { condenseTarget, WHITE_LABEL_LOCATION } from "@divinci-ai/models";

import { DIVINCI_TEST_PROCESS_CONFIG } from "@divinci-ai/server-globals";

import { createFiles } from "./createFiles";
// Update CastedBusBoy schema with more debugging and relaxed file handling
const busboyHandleBody = CastedBusBoy.create(
  {
    title: "string",
    description: "string",
    chunkingTool: "string",
    chunkingToolConfig: "string?",
    file: "file[]"
  },
  fileHandler,
);

export const directCreateRagVectorFile: RequestHandler = async function(req, res, next){
  try {
    // Log incoming headers for debugging purposes
    console.log("📥 Incoming request headers:", req.headers);

    const whitelabelId = getParam(req, "whitelabelId");

    // Consume the request body using busboy and log the results for debugging
    const [whitelabel, body] = await Promise.all([
      WhiteLabelModel.findById(whitelabelId),
      busboyHandleBody.consumeRequest(req),
    ]);

    if(body.file.length === 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Expecting at least one file");
    }
    const chunkingToolConfig = (function(){
      if(!body.chunkingToolConfig) return void 0;
      try {
        return JSON.parse(body.chunkingToolConfig);
      }catch(e){
        throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid chunkingTool Config");
      }
    })();

    // Log the parsed body after busboy has consumed it
    console.log("ℹ️ Parsed body after busboy:", body);

    // Check if the WhiteLabel object exists
    if(whitelabel === null){
      console.error("❌ Whitelabel not found");
      throw HTTP_ERRORS.NOT_FOUND;
    }

    // Log the target condense location
    const target = condenseTarget({
      ...WHITE_LABEL_LOCATION,
      id: whitelabelId,
    });
    console.log("🗜️ Condensed target:", target);

    const savedFiles = await createFiles(
      target,
      { toolId: body.chunkingTool, config: chunkingToolConfig },
      { title: body.title, description: body.description },
      body.file.map((file)=>({
        bucket: file.bucket, objectKey: file.objectKey, filename: file.info.filename
      })),
      DIVINCI_TEST_PROCESS_CONFIG.getHeader(req.headers)
    );

    // Send the response with the array of saved files
    res.status(202).json(savedFiles);
  }catch(e){
    console.error("❌ Error in addRagVectorFile handler:", e);
    next(e);
  }
};
