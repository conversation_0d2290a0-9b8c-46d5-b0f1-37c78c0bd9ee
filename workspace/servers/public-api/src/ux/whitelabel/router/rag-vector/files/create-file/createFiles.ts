import { RagVectorFileModel } from "@divinci-ai/server-models";

import { DivinciTestProcessConfig } from "@divinci-ai/server-globals";
import { JSON_Object } from "@divinci-ai/utils";


export async function createFiles(
  target: string,
  chunkingConfig: { toolId: string, config?: JSON_Object },
  userInfo: { title: string, description: string },
  files: Array<{ bucket: string, objectKey: string, filename: string }>,
  testConfig: null | DivinciTestProcessConfig
){

  return Promise.all(files.map(async function(file, index){
    // Skip AppleDouble files (._ files)
    if(file.filename.startsWith("._")) {
      console.log(`Skipping resource fork file: ${file.filename}`);
      return null; // Skip this file
    }

    const { doc: savedFile } = await RagVectorFileModel.addNewFile(
      target,
      {
        bucket: file.bucket,
        objectKey: file.objectKey,
        originalName: file.filename,
      },
      chunkingConfig,
      files.length > 1 ? {
        title: `${userInfo.title} ${file.filename}`, // Assuming the title is the filename
        description: userInfo.description, // Assuming the description is also the filename
      } : { title: userInfo.title, description: userInfo.description },
      testConfig,
    );

    console.log(`💾 Saved file ${index}:`, savedFile);

    return savedFile;
  }));
}
