import { Re<PERSON><PERSON><PERSON><PERSON> } from "express";
import { jsonBody } from "@divinci-ai/server-utils";
import { castToObject, castToArray, JSON_Unknown } from "@divinci-ai/utils";
import { RagVectorTextChunk } from "@divinci-ai/models";

import { TextChunksConfig } from "../types";

interface BulkChunksBody {
  chunks: Array<Omit<RagVectorTextChunk, "_id">>,
}

function validateChunksBody(body: unknown): BulkChunksBody{
  const obj = castToObject(body as JSON_Unknown);
  if(!obj.chunks){
    throw new Error("Missing chunks array");
  }

  const chunks = castToArray(obj.chunks);
  return {
    chunks: chunks.map((chunk)=>{
      const chunkObj = castToObject(chunk);
      if(typeof chunkObj.text !== "string") {
        throw new Error("Chunk text must be a string");
      }
      if(!Array.isArray(chunkObj.tags)) {
        throw new Error("Chunk tags must be an array");
      }
      if(!chunkObj.tags.every((tag)=>(typeof tag === "string"))) {
        throw new Error("All tags must be strings");
      }
      return {
        text: chunkObj.text,
        tags: chunkObj.tags as string[]
      };
    })
  };
}

export function addChunks(config: TextChunksConfig): RequestHandler{
  return async function(req, res, next){
    try {
      const [textChunks, body] = await Promise.all([
        config.resolveTextChunks(req),
        jsonBody(req).then(validateChunksBody)
      ]);

      const newChunks = await Promise.all(
        body.chunks.map((chunk)=>(textChunks.addChunk(chunk)))
      );

      res.json({
        status: "ok",
        chunks: newChunks
      });

    }catch(error){
      next(error);
    }
  };
}
