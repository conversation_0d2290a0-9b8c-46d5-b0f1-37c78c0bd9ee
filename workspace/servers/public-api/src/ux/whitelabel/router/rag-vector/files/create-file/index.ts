
export * from "./direct";
export * from "./presigned";

import { Router } from "express";

export const router = Router({ mergeParams: true });

import { directCreateRagVectorFile } from "./direct";
router.post("/", directCreateRagVectorFile);

import { presignedPrepare, presignedFinalized } from "./presigned";
router.post("/presigned/prepare", presignedPrepare);
router.post("/presigned/run", presignedFinalized);

