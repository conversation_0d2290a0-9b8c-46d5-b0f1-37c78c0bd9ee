import { RequestHand<PERSON> } from "express";
import { RagVectorModel } from "@divinci-ai/server-models";
import { getItemFromIdRequest } from "../../../../util";

export const clearCustomRag: RequestHandler = async function(req, res, next){
  try {
    const { item } = await getItemFromIdRequest(
      req,
      "ragId",
      RagVectorModel,
    );

    const newDoc = await item.clearVectorIndex();

    res.statusCode = 200;
    res.json(newDoc);
  }catch(e){
    next(e);
  }
};
