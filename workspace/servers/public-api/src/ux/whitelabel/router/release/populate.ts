

import { WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { JSON_Unknown } from "@divinci-ai/utils";
export function populateReleases(query: Record<string, JSON_Unknown>){
  return {
    $lookup: {
      from: WhiteLabelReleaseModel.collection.name,
      let: { docId: { $toString: "$_id" } },
      pipeline: [
        {
          $match: query
        },
        // 2) Project only the fields you need
        {
          $project: {
            _id: 1,
            title: 1,
            category: 1,
            status: 1
          }
        }
      ],
      as: "releases"
    }
  };
}
