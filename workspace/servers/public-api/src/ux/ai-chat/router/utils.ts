import {
  WEB_CLIENT_IS_SECURE,
  WEB_CLIENT_HOST,
} from "@divinci-ai/server-globals";
import { ChatModel } from "@divinci-ai/server-models";
import { TranscriptMessage } from "@divinci-ai/models";


export function messageToURL(
  chat: InstanceType<typeof ChatModel>,
  message: TranscriptMessage,
){
  // Example url: http://localhost:8080/ai-chat/abc123#message-xyz789
  const url = new URL(
    `http://${WEB_CLIENT_HOST}/ai-chat/${chat._id}#message-${message._id}`,
  );
  if(WEB_CLIENT_IS_SECURE) url.protocol = "https:";
  return url.href;
}
