

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";

import { getUserId } from "@divinci-ai/server-globals";
import { AssistantMap, ChatModel, ChatPreferenceModel, WhiteLabelReleaseModel } from "@divinci-ai/server-models";
import { getParam, HTTP_ERRORS } from "@divinci-ai/server-utils";
import { logDebug } from "@divinci-ai/utils";

import { CHAT_AI_ASSISTANT } from "@divinci-ai/server-tools";

const ASSISTANT_INFO = CHAT_AI_ASSISTANT.getAssistantInfo();
type AssistantMapWithDefault = typeof ASSISTANT_INFO;

export const getChatMessageAssistants: RequestHandler = async (req, res, next)=>{
  try {

    const userId = getUserId(req);
    const chatId = getParam(req, "chatId");
    const chat = await ChatModel.findById(chatId);
    if(!chat) throw HTTP_ERRORS.NOT_FOUND;

    const config = await (async ()=>{
      const preferences = await ChatPreferenceModel.findOrCreate(userId);
      console.log("Release Ids:", chat.releases, preferences.savedReleases);
      const releaseIds = Array.from(new Set([
        ...chat.releases,
        ...preferences.savedReleases
      ]));

      logDebug("💬getMessageAssistants chat: ", chat, releaseIds);
      if(releaseIds.length === 0) return ASSISTANT_INFO;

      const info = await WhiteLabelReleaseModel.getAssistantInfo(releaseIds);

      const categories = new Set(Object.keys(info).concat(Object.keys(ASSISTANT_INFO)));
      const combined: AssistantMapWithDefault = {};
      for(const category of categories){
        combined[category] = {
          available: [
            ...getAvailable(ASSISTANT_INFO, category),
            ...getAvailable(info, category),
          ],
          default: ASSISTANT_INFO[category].default,
        };
      }

      return combined;
    })();

    res.statusCode = 200;
    res.json(config);
  }catch(e){
    next(e);
  }
};

function getAvailable(map: AssistantMap, category: string){
  if(!map[category]) return [];
  return map[category].available;
}

