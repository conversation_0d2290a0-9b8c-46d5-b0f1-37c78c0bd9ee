import { S3, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";

import { S3Location, PresignedResult } from "./types";
import { encryptLocation, decryptLocation } from "./encrypt-location";
import { signValues, validateSigniture } from "./sign-values";
import { castShallowObject, JSON_Unknown, uniqueId } from "@divinci-ai/utils";
import { parse as pathParse } from "node:path";

export { PresignedResult };

const FILEINFO_ERROR = "files are expected to be an array of byteSize and filename";
export function castFileInfo(fileInfo: JSON_Unknown){
  if(!Array.isArray(fileInfo)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(FILEINFO_ERROR);
  }
  return fileInfo.map((info)=>{
    const casted = castShallowObject(
      info, { byteSize: "number", filename: "string" },
      HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(FILEINFO_ERROR)
    );
    if(casted.byteSize <= 0){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Each byteSize should be greater than 0");
    }
    if(casted.byteSize !== Math.floor(casted.byteSize)){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Each byteSize should not have a decimal");
    }
    return casted;
  });
}
export const PRIVATE_UPLOADS_BUCKET = "private-temporary-uploads";

export async function createPresignedURL(
  r2: S3,
  info: { purpose: string, userId: string },
  fileInfo: { byteSize: number, filename: string },
  locationConfig: { Bucket: string } = { Bucket: PRIVATE_UPLOADS_BUCKET },
): Promise<PresignedResult>{
  const { ext } = pathParse(fileInfo.filename);
  const location = {
    Bucket: locationConfig.Bucket,
    Key: `${uniqueId()}${ext}`
  };
  const expirationInSeconds = 3600;
  const url = await getSignedUrl(
    r2,
    new PutObjectCommand({
      Bucket: location.Bucket,
      Key: location.Key,
    }),
    { expiresIn: expirationInSeconds },
  );
  const value = {
    url,
    expiration: Date.now() + (expirationInSeconds * 1000),
    location: encryptLocation(location),
    ...info,
    ...fileInfo,
  };
  const signiture = signValues(value);
  return { ...value, signiture };
}

export function castPresignedConfig(configRaw: string): PresignedResult{
  const config = (function(){
    try {
      return JSON.parse(configRaw);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Couldn't parse presigned config");
    }
  })();

  return castShallowObject(config, {
    url: "string",
    purpose: "string",
    userId: "string",
    expiration: "number",
    location: "string",
    filename: "string",
    byteSize: "number",
    signiture: "string",
  }, HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Couldn't cast presigned config"));
}

export async function handlePresignedURL(
  r2: S3,
  info: { purpose: string, userId: string },
  result: PresignedResult,
): Promise<S3Location & { filename: string }>{
  const decryptedLocation = decryptLocation(result.location);
  const head = await (async function(){
    try {
      return r2.headObject(decryptedLocation);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Object Not Completed");
    }
  })();
  if(result.userId !== info.userId){
    throw HTTP_ERRORS_WITH_CONTEXT.FORBIDDEN("Uploader not current user");
  }

  const contentLength = head.ContentLength;
  if(contentLength !== result.byteSize){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Different Bytesizes");
  }

  if(info.purpose !== result.purpose){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Bad Purpose");
  }
  if(result.expiration < Date.now()){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Expired URL");
  }
  if(!validateSigniture(result, result.signiture)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Invalid Presigned URL");
  }

  return { ... decryptedLocation, filename: result.filename };
}

