import { createHmac, timingSafeEqual } from "node:crypto";
import { PRESIGNED_SECRET_SIGNITURE } from "@divinci-ai/server-globals";
import { PresignedConfig } from "./types";

export function signValues(config: PresignedConfig): string{
  const signable = configToSignable(config);
  const hmac = createHmac("sha256", PRESIGNED_SECRET_SIGNITURE);
  hmac.update(signable);
  return hmac.digest("hex");
}

export function validateSigniture(config: PresignedConfig, signature: string): boolean{
  const signable = configToSignable(config);
  const hmac = createHmac("sha256", PRESIGNED_SECRET_SIGNITURE);
  hmac.update(signable);
  const expected = hmac.digest("hex");

  // Compare in constant time, to prevent timing attacks
  return timingSafeEqual(Buffer.from(signature), Buffer.from(expected));
}

const KEYS: Array<keyof PresignedConfig> = [
  "url",
  "purpose",
  "expiration",
  "location",
  "filename",
  "byteSize",
  "userId",
];

function configToSignable(config: PresignedConfig){
  return (`{${KEYS.map((key)=>(`"${key}":"${config[key]}"`)).join(",")}}`);
}
