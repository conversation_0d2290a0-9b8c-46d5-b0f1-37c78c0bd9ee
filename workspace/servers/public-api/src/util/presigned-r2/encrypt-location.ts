
import { S3Location } from "./types";
// const key = crypto.randomBytes(32);
import { PRESIGNED_SECRET_LOCATION } from "@divinci-ai/server-globals";
import { randomBytes, createCipheriv, createDecipheriv } from "node:crypto";
import { HTTP_ERRORS_WITH_CONTEXT } from "@divinci-ai/server-utils";


export function encryptLocation(location: S3Location): string{
  const iv = randomBytes(16);
  const stringified = JSON.stringify(location);
  const cipher = createCipheriv(
    "aes-256-cbc", PRESIGNED_SECRET_LOCATION, iv
  );
  let encrypted = cipher.update(stringified, "utf8", "hex");
  encrypted += cipher.final("hex");
  return `${iv.toString("hex")}:${encrypted}`;
}

export function decryptLocation(encrypted: string): S3Location{
  const [ivHex, encryptedHex] = (function(){
    const encryptedParts = encrypted.split(":");
    if(encryptedParts.length !== 2) throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      "Expecting 2 parts for encrypted location"
    );
    return encryptedParts;
  })();

  const decrypted = (function(){
    try {
      const decipher = createDecipheriv(
        "aes-256-cbc", PRESIGNED_SECRET_LOCATION, Buffer.from(ivHex, "hex")
      );
      let decrypted = decipher.update(encryptedHex, "hex", "utf8");
      decrypted += decipher.final("utf8");
      return decrypted;
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Could not decrypt the location"
      );
    }
  })();

  const locationUncasted = (function(){
    try {
      return JSON.parse(decrypted);
    }catch(e){
      throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
        "Encrypted Location is invalid JSON"
      );
    }
  })();

  if(
    typeof locationUncasted !== "object" ||
    Array.isArray(locationUncasted) ||
    locationUncasted === null
  ){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      "Encrypted Location is not an object"
    );
  }

  if(Object.keys(locationUncasted).length !== 2){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      "Encrypted Location is expected to have 2 keys"
    );
  }
  if(typeof locationUncasted.Key !== "string"){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      "Encrypted Location is expected to have a Key string"
    );
  }
  if(typeof locationUncasted.Bucket !== "string"){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM(
      "Encrypted Location is expected to have a Bucket string"
    );
  }

  return {
    Key: locationUncasted.Key,
    Bucket: locationUncasted.Bucket
  };
}
