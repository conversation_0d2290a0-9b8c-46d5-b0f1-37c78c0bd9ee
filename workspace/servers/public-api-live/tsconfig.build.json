{"compilerOptions": {"target": "es2022", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node", "jest"], "lib": ["es2022", "dom"], "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "removeComments": true, "noImplicitAny": true}, "files": ["src/index.ts"], "include": ["./typings", "./types", "./src/types"], "exclude": ["node_modules", "**/*.spec.ts", "**/*.test.ts", "tests"]}