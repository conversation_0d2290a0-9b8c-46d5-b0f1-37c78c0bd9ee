{"/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/build-ignore-errors.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/build-ignore-errors.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 44}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 45}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 30}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 24}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 1}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 38}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 57}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 52}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 0}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 70}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 5}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 87}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 78}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 17}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 81}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 1}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 0}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 68}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 68}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 35}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 50}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 1}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 0}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 56}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 26}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 23}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 22}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 23}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 45}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 67}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 47}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 47}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 11}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 21}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 46}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 6}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 3}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 1}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 32}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 23}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 32}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 13}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 21}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 1}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 0}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 33}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 24}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 22}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 13}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 15}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 18}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 13}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 15}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 21}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 1}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 0}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 26}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 28}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 2}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 0}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 74}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 0}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 52}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1943}, "end": {"line": 69, "column": 52}}, "locations": [{"start": {"line": 1, "column": 1943}, "end": {"line": 69, "column": 52}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1943}, "end": {"line": 69, "column": 52}}, "loc": {"start": {"line": 1, "column": 1943}, "end": {"line": 69, "column": 52}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/jest.setup.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/jest.setup.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 29}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 33}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 22}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 18}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 37}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 71}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 47}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 87}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 57}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 105}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 10}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 75}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 57}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 63}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 3}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 54}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 1}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 0}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 27}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 0}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 49}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 74}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 0}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 69}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 78}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 0}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 22}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 26}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 42}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 66}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 35}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1319}, "end": {"line": 35, "column": 3}}, "locations": [{"start": {"line": 1, "column": 1319}, "end": {"line": 35, "column": 3}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1319}, "end": {"line": 35, "column": 3}}, "loc": {"start": {"line": 1, "column": 1319}, "end": {"line": 35, "column": 3}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/skipTests.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/skipTests.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 40}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 33}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 95}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 105}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 11}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 5}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 53}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 162}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 20}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 1}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 38}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 0}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 27}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 64}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 56}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 242}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 154}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 0}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 68}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 56}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 57}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 66}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 0}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 47}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 72}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 14}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 86}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 38}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 0}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 23}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 43}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 84}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 51}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 101}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 60}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 0}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 56}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 67}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 99}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 86}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 21}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 65}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 84}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 33}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 102}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 17}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 13}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 11}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 5}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 5}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 2763}, "end": {"line": 57, "column": 5}}, "locations": [{"start": {"line": 1, "column": 2763}, "end": {"line": 57, "column": 5}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 2763}, "end": {"line": 57, "column": 5}}, "loc": {"start": {"line": 1, "column": 2763}, "end": {"line": 57, "column": 5}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/esbuild.config.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/esbuild.config.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 37}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 76}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 108}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 0}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 54}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 109}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 114}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 55}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 85}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 96}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 42}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 44}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 44}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 44}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 46}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 0}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 2}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 29}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 34}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 29}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 2}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 7}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 18}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 43}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 63}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 40}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 62}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 40}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 39}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 88}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 43}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 13}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 36}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 24}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 20}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 21}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 4}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 83}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 17}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 19}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 18}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 0}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 2}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 29}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 17}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 29}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 2}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 7}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 18}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 43}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 63}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 40}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 62}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 40}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 39}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 88}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 43}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 13}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 36}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 24}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 20}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 21}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 4}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 83}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 17}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 19}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 18}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 2604}, "end": {"line": 74, "column": 3}}, "locations": [{"start": {"line": 1, "column": 2604}, "end": {"line": 74, "column": 3}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 2604}, "end": {"line": 74, "column": 3}}, "loc": {"start": {"line": 1, "column": 2604}, "end": {"line": 74, "column": 3}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/skip-modules.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/skip-modules.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 65}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 0}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 53}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 62}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 107}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 49}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 69}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 85}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 69}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 79}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 49}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 61}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 49}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 32}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 5}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 910}, "end": {"line": 21, "column": 2}}, "locations": [{"start": {"line": 1, "column": 910}, "end": {"line": 21, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 910}, "end": {"line": 21, "column": 2}}, "loc": {"start": {"line": 1, "column": 910}, "end": {"line": 21, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/validate.js": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/bundle/validate.js", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 29}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 54}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 47}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 55}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 3}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 46}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 12}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 73}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 3}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 55}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 30}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 60}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 3}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 55}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 30}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 60}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 0}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 44}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 2}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 55}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 47}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 30}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 56}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 3}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 58}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 29}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 62}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 3}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 58}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 29}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 62}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 3}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 0}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 38}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 2}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 35}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 32}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 17}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 44}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 17}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 3}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 0}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 14}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 1}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 0}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 0}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 0}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 41}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 59}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 47}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 65}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 67}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 42}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 29}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 48}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 44}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 19}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1946}, "end": {"line": 65, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1946}, "end": {"line": 65, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1946}, "end": {"line": 65, "column": 1}}, "loc": {"start": {"line": 1, "column": 1946}, "end": {"line": 65, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/app.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/app.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 63}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 44}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 62}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 66}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 36}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 19}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 39}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 35}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 30}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 0}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 59}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 75}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 37}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 94}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 68}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 66}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 22}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 5}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 0}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 68}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 9}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 63}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 72}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 46}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 37}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 9}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 21}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 64}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 5}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 5}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 0}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 78}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 29}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 63}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 5}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 19}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 59}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -245}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -245}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -245}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -245}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/env.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/env.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 52}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 87}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 13}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 14}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 29}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 45}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 51}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 34}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 56}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 47}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 26}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 30}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 53}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 53}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 48}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 63}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 34}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 43}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 65}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 7}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 38}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 6}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 40}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 14}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 4}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 1}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 0}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 21}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 18}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 41}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 70}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 35}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 3}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 0}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 4}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 43}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 26}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 41}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 28}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 78}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 3}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 4}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 0}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 59}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 15}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1539}, "end": {"line": 51, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1539}, "end": {"line": 51, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1539}, "end": {"line": 51, "column": 1}}, "loc": {"start": {"line": 1, "column": 1539}, "end": {"line": 51, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 15}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 15}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -17}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -17}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -17}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -17}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/database.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/database.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 8}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 14}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 11}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 21}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 0}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 34}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 74}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 23}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 21}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 5}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 10}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 13}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 16}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 20}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 4}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 1}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 35}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 7}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 51}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 67}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 30}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 59}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 20}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 17}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 61}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 14}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 3}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 33}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 7}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 47}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 39}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 53}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 74}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 57}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 44}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 17}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 59}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 14}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 3}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 1}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 0}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 61}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -148}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -148}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -148}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 47, "column": -148}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/websocket.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/websocket.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 65}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 82}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 41}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 26}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 27}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 33}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 59}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 36}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 67}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 25}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 80}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 15}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 7}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 6}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 5}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 0}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 69}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 66}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 0}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 18}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 1}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 42}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 22}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 14}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 1}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 0}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 73}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 64}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 19}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 34}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 15}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 4}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 74}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 2}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 71}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 71}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 27}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 23}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 37}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 22}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 52}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 54}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 61}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 7}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 20}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 52}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 5}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 24}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 35}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 5}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 53}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 56}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 21}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 55}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 5}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 57}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 18}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 40}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 5}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 35}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 7}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 0}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 46}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 70}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 72, "column": -279}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 72, "column": -279}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 72, "column": -279}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 72, "column": -279}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/http/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/setup/http/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 28}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 63}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 64}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 37}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 24}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 42}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 29}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 0}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 52}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 68}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 83}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 0}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 34}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 69}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 14}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 23}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 38}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 39}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 7}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 5}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 0}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 0}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 74}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 9}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 46}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 42}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 16}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 57}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 9}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 9}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 14}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 14}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 5}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 5}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 0}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 13}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 44, "column": -331}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 44, "column": -331}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 44, "column": -331}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 44, "column": -331}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/permission/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/permission/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 32}}}, "s": {"0": 0, "1": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/permission/ws-middleware.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/permission/ws-middleware.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 81}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 63}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 58}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 73}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 65}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 77}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 36}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 46}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 4}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 1}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 0}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 106}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 71}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 83}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 3}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 37}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 9}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 63}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 65}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 51}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 51}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 89}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 43}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 74}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 34}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 18}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 23}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 0}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 34}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 14}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 14}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 5}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 37, "column": -311}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 37, "column": -311}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 37, "column": -311}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 37, "column": -311}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/JobTracker.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/JobTracker.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 50}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 77}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 32}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 27}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 41}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 36}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 36}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 40}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 14}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 0}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 16}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 9}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 37}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 30}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 23}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 44}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 7}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 44}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 43}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 28}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 61}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 15}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 14}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 56}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 32}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 39}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 7}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 30}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 28}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 50}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 5}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 3}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 0}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 12}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 42}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 26}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 17}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 3}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 0}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 9}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 28}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 24}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 42}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 26}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 3}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -285}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -285}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -285}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -285}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/globalJob.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/globalJob.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 106}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 51}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 0}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 59}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 85}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 33}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 40}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 48}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 34}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 25}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 7}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 22}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 16}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 19}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 24}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 17}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 6}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 35}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 10}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 59}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 3}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 40}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 48}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 18}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 26}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 46}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 39}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 1}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 0}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 31}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 77}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 26}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 49}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 45}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 30}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 17}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 38}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 12}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 5}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 0}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 10}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 27}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 20}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 17}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 7}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 17}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 35}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 45}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 47}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 34}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 32}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 9}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 4}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 1}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 0}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 36}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 47}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 18}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 45}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 31}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 31}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 1}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 0}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 40}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 47}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 18}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 47}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 3}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 0}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 25}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 38}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 29}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 24}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 3}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 38}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 1}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 0}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 0}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 71}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 20}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 17}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 46}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 41}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 14}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 29}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 5}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 16}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 32}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 5}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 5}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 0}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 55}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0, "100": 0, "101": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -278}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -278}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -278}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 102, "column": -278}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 27}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -29}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -29}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -29}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -29}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/localJob.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/localJob.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 42}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 46}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 85}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 36}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 1}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 71}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 20}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 17}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 46}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 14}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 29}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 5}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 16}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 32}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 5}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 5}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 0}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 55}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 24, "column": -263}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 24, "column": -263}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 24, "column": -263}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 24, "column": -263}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/types.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/poll-job/types.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 78}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 59}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 46}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 39}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 88}, "end": {"line": 8, "column": -42}}, "locations": [{"start": {"line": 1, "column": 88}, "end": {"line": 8, "column": -42}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 88}, "end": {"line": 8, "column": -42}}, "loc": {"start": {"line": 1, "column": 88}, "end": {"line": 8, "column": -42}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/constants.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/constants.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 57}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 64}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 36}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 5, "column": -25}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 5, "column": -25}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 5, "column": -25}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 5, "column": -25}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/http-router.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/http-router.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 55}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 33}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 70}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 75}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 55}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 31}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 0}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 37}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 50}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 7}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 33}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 0}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 34}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 41}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 0}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 30}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 0}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 25}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 31}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 12}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 12}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 3}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 3}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 0}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 54}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 7}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 49}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 0}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 35}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 34}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 5}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 0}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 41}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 25}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 31}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 12}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 12}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 3}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 48, "column": -500}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 48, "column": -500}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 48, "column": -500}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 48, "column": -500}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 23}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -32}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -32}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -32}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -32}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/util.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/util/user-cookie/util.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 3}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 67}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 2}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 25}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 82}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 3}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 40}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 36}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 47}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 32}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 0}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 55}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 60}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 3}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 54}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 40}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 4}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 73}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 63}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 83}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 0}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 35}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 20}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 54}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 22}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 6}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 0}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 50}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 2}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 0}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 66}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 3}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 64}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 83}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 65}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 4}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 85}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 65}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 0}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 57}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 38}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 0}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 37}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 56}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 12}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 4}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 0}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 8}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 51}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 0}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 38}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 83}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 57}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 6}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 0}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 65}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 27}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 0}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 25}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 69}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 17}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 4}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 2177}, "end": {"line": 69, "column": 2}}, "locations": [{"start": {"line": 1, "column": 2177}, "end": {"line": 69, "column": 2}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 2177}, "end": {"line": 69, "column": 2}}, "loc": {"start": {"line": 1, "column": 2177}, "end": {"line": 69, "column": 2}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/permission.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/permission.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 0}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 68}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 72}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 78}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 223}, "end": {"line": 6, "column": 78}}, "locations": [{"start": {"line": 1, "column": 223}, "end": {"line": 6, "column": 78}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 223}, "end": {"line": 6, "column": 78}}, "loc": {"start": {"line": 1, "column": 223}, "end": {"line": 6, "column": 78}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/ws-router/chat.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/ws-router/chat.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 81}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 83}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 54}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 69}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 49}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 62}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 73}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 10}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 9}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 7}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 3}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 7}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 65}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 0}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 33}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 50}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 24}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 5}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 0}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 18}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 36}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 43}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 31}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 12}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 27}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 5}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 0}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 57}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 0}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 45}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 40}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 40}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 34}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 6}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 0}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 61}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 42}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 39}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 7}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 15}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 35}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 12}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 3}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -277}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -277}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -277}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 50, "column": -277}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/ws-router/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/ai-chat/ws-router/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 41}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 0}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 67}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 0}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 44}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 118}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 385}, "end": {"line": 8, "column": 118}}, "locations": [{"start": {"line": 1, "column": 385}, "end": {"line": 8, "column": 118}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 385}, "end": {"line": 8, "column": 118}}, "loc": {"start": {"line": 1, "column": 385}, "end": {"line": 8, "column": 118}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/permission.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/permission.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 68}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 76}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 0}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 82}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 231}, "end": {"line": 6, "column": 82}}, "locations": [{"start": {"line": 1, "column": 231}, "end": {"line": 6, "column": 82}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 231}, "end": {"line": 6, "column": 82}}, "loc": {"start": {"line": 1, "column": 231}, "end": {"line": 6, "column": 82}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/fine-tune.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/fine-tune.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 93}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 77}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 69}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 64}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 74}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 0}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 102}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 7}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 0}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 28}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 0}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 50}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 0}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 48}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 60}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 45}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 43}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 7}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 0}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 31}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 5}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 29}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 34}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 5}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 30}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 22}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 7}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 38}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 34}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 5}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 0}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 89}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 0}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 0}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 45}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 0}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 43}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 74}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 7}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 0}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 35}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 37}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 7}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 35}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 41}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 7}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 34}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 24}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 7}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 0}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 42}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 27}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 7}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 42}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 24}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 7}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 0}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 23}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 0}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 36}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 12}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 12}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 3}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 2}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 0}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 91}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 40}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 54}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 43}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 0}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 34}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 32}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 45}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 15}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -555}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -555}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -555}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 79, "column": -555}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/index.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 59}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 0}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 67}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 53}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 44}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 53}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 74}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 55}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 23}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 3}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 0}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 53}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 75}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 59}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 55}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 24}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 717}, "end": {"line": 18, "column": 3}}, "locations": [{"start": {"line": 1, "column": 717}, "end": {"line": 18, "column": 3}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 717}, "end": {"line": 18, "column": 3}}, "loc": {"start": {"line": 1, "column": 717}, "end": {"line": 18, "column": 3}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/whitelabel.ts": {"path": "/Users/<USER>/Documents/Divinci/server3/server/workspace/servers/public-api-live/src/ux/whitelabel/ws-router/whitelabel.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 94}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 83}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 77}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 69}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 0}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 49}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 0}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 62}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 0}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 79}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 10}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 9}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 7}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 3}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 7}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 65}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 0}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 50}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 68}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 0}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 20}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 70}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 5}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 0}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 57}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 94}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 5}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 0}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 68}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 0}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 20}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 70}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 5}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 0}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 24}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 36}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 47}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 31}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 12}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 27}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 5}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 45}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 46}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 71}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 40}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 6}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 16}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 23}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 36}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 6}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 56}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 42}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 59}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 7}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 12}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 12}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 3}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 2}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 58, "column": -363}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 58, "column": -363}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 58, "column": -363}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 58, "column": -363}}, "line": 1}}, "f": {"0": 0}}}