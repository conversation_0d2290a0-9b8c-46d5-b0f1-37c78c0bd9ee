import { defineConfig } from "vitest/config";
import { resolve } from "path";

export default defineConfig({
  test: {
    exclude: [
      "**/node_modules/**",
      "**/dist/**",
      "**/*.load.test.ts",
      "**/*.stress.test.ts",
      "**/*.perf.test.ts",
    ],
    pool: "forks", // Use forks instead of threads to avoid memory issues
    poolOptions: {
      forks: {
        isolate: true,
      },
    },
    testTimeout: 10000, // Increase timeout to 10 seconds
    setupFiles: ["./tests/setup.ts"], // Include the setup file
    globals: true, // Allow global test functions
    environmentOptions: {
      // Increase memory limit
      "--max-old-space-size": "8192",
    },
    // Handle unhandled errors better
    onConsoleLog(log){
      if(log.includes("Unhandled Error")) {
        console.warn("Warning: Unhandled error detected in test");
        // Don't fail test for console errors from libraries
        return false;
      }
      return true;
    },
    // Better memory handling
    maxConcurrency: 1, // Run tests sequentially to avoid memory issues
    maxThreads: 1,
    minThreads: 1,
    // Cleanup between tests
    restoreMocks: true,
    // Close open handles between tests
    teardownTimeout: 5000,
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
      "~": resolve(__dirname, "./tests")
    }
  },
});
