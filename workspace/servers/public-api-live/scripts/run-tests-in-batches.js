#!/usr/bin/env node

/**
 * <PERSON>ript to run tests in smaller batches to avoid memory issues.
 *
 * Usage:
 * node scripts/run-tests-in-batches.js
 */

const { execSync } = require("child_process");
const { globSync } = require("glob");
const path = require("path");

// Configuration
const BATCH_SIZE = 2;  // Number of test files to run at once
const TEST_GLOB = "tests/**/*.test.ts";
const NODE_OPTIONS = "--max_old_space_size=8192";
const EXCLUDE_PATTERNS = [
  "**/node_modules/**",
  "**/dist/**",
  "**/*.load.test.ts",
  "**/*.stress.test.ts",
  "**/*.perf.test.ts",
];

// Find all test files
console.log("🔎 Finding test files...");
const testFiles = globSync(TEST_GLOB, {
  ignore: EXCLUDE_PATTERNS,
  cwd: process.cwd(),
  absolute: false
});

console.log(`📋 Found ${testFiles.length} test files`);

// Group files into batches
const batches = [];
for(let i = 0; i < testFiles.length; i += BATCH_SIZE) {
  batches.push(testFiles.slice(i, i + BATCH_SIZE));
}

console.log(`🧩 Created ${batches.length} batches`);

// Run each batch
const failedBatches = [];
const successfulBatches = [];

batches.forEach((batch, index)=>{
  console.log(`\n🚀 Running batch ${index + 1}/${batches.length} with ${batch.length} test files:`);
  batch.forEach(file=>console.log(`   - ${file}`));

  try {
    const command = `NODE_OPTIONS="${NODE_OPTIONS}" npx vitest run ${batch.join(" ")} --run`;
    console.log(`\n📌 Executing: ${command}\n`);

    execSync(command, { stdio: "inherit" });
    console.log(`\n✅ Batch ${index + 1} completed successfully`);
    successfulBatches.push(index + 1);
  }catch(error) {
    console.error(`\n❌ Batch ${index + 1} failed`);
    failedBatches.push(index + 1);
  }

  // Force garbage collection between batches
  try {
    execSync("node --expose-gc -e \"global.gc && console.log('🧹 Garbage collection performed')\"", { stdio: "inherit" });
  }catch(error) {
    console.warn("⚠️ Unable to force garbage collection between batches");
  }
});

// Summary
console.log("\n📊 Test Summary:");
console.log(`   - Total batches: ${batches.length}`);
console.log(`   - Successful batches: ${successfulBatches.length}`);
console.log(`   - Failed batches: ${failedBatches.length}`);

if(failedBatches.length > 0) {
  console.log("\n❌ Failed batches:", failedBatches.join(", "));
  process.exit(1);
} else {
  console.log("\n🎉 All batches completed successfully!");
  process.exit(0);
}