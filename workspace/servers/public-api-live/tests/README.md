# Public API Live Server Tests

This directory contains tests for the public-api-live server, which handles WebSocket connections and real-time updates for the Divinci platform.

## Test Structure

The tests are organized into the following categories:

1. **Unit Tests**: Testing individual functions and components in isolation
2. **Integration Tests**: Testing the interaction between components
3. **WebSocket Tests**: Testing WebSocket connections and message handling
4. **HTTP API Tests**: Testing the HTTP endpoints
5. **Authentication Tests**: Testing user authentication and cookie handling

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/util/foo` resolves to `src/util/foo`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { setupWebsocket } from '@/websocket/setup';

// Import from tests directory
import { mockWebsocket } from '~/helpers/mocks';
```

## Running Tests

To run all tests:

```bash
pnpm test
```

To run a specific test file:

```bash
pnpm test -- tests/unit/user-cookie.test.ts
```

### Memory Management for Tests

If you encounter "JavaScript heap out of memory" errors when running tests, you can increase the memory limit and enable garbage collection:

```bash
# Increase Node.js memory limit to 8GB
NODE_OPTIONS="--max_old_space_size=8192" pnpm test

# Enable manual garbage collection during tests
NODE_OPTIONS="--expose-gc --max_old_space_size=8192" pnpm test
```

When using `--expose-gc`, you can call `global.gc()` in your tests to manually trigger garbage collection after memory-intensive operations:

```typescript
// At the end of a memory-intensive test
test('memory-intensive operation', async () => {
  // Test code here

  // Manually trigger garbage collection if available
  if (global.gc) {
    global.gc();
  }
});
```

You can also run tests in smaller batches to reduce memory pressure:

```bash
# Run specific test groups separately
pnpm test tests/unit/database-setup.test.ts tests/unit/http-setup.test.ts
pnpm test tests/unit/websocket-setup.test.ts tests/unit/user-cookie.test.ts
```

## Test Coverage

The test suite aims to cover:

- WebSocket connection handling
- Real-time updates for AI chat transcripts
- Real-time updates for whitelabel transcripts
- Fine-tune job polling
- User cookie authentication
- Error handling
- Edge cases and error conditions
