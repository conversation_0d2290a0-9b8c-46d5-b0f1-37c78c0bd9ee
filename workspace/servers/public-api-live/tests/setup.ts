import { vi, beforeEach, afterEach, afterAll } from "vitest";
import "./types/mocks";

// Track resources that need cleanup
const resources = {
  timeouts: [] as NodeJS.Timeout[],
  intervals: [] as NodeJS.Timeout[],
  promises: [] as Promise<any>[],
};

// Mock environment variables
process.env.JSON_WEBTOKEN_SECRET = "test-jwt-secret";
process.env.HTTP_PORT = "8080";
process.env.HELLO_WORLD = "test-server";
process.env.MONGODB_URI = "mongodb://localhost:27017/test";
process.env.REDIS_URI = "redis://localhost:6379";
process.env.NODE_OPTIONS = "--max_old_space_size=8192";

// Mock console.error to catch unhandled rejections
const originalConsoleError = console.error;
console.error = (...args)=>{
  // Log but don't fail tests for specific expected error messages
  if(
    typeof args[0] === "string" &&
    (args[0].includes("❌ Failed to connect") ||
     args[0].includes("WebSocket Error") ||
     args[0].includes("🔥 Error"))
  ) {
    // These are expected errors in our tests
    return;
  }
  originalConsoleError(...args);
};

// Safe timeout wrapper that tracks resources
global.setTimeout = ((originalSetTimeout)=>{
  return function(callback: Function, ms?: number, ...args: any[]): NodeJS.Timeout{
    const timeout = originalSetTimeout(callback, ms, ...args);
    resources.timeouts.push(timeout);
    return timeout;
  };
})(global.setTimeout);

// Safe interval wrapper that tracks resources
global.setInterval = ((originalSetInterval)=>{
  return function(callback: Function, ms?: number, ...args: any[]): NodeJS.Timeout{
    const interval = originalSetInterval(callback, ms, ...args);
    resources.intervals.push(interval);
    return interval;
  };
})(global.setInterval);

// Mock server-globals
vi.mock("@divinci-ai/server-globals", ()=>({
  getMongoose: vi.fn().mockReturnValue({
    mongoose: {
      connect: vi.fn().mockResolvedValue({}),
    },
    connect: vi.fn().mockResolvedValue({}),
  }),
  getRedis: vi.fn().mockReturnValue({
    redisClient: {
      connect: vi.fn().mockResolvedValue({}),
    },
  }),
  getChatRedisClient: vi.fn().mockReturnValue({
    chatRedisClient: {
      connect: vi.fn().mockResolvedValue({}),
      subscribe: vi.fn(),
      on: vi.fn(),
    },
  }),
  ChannelSubscriber: vi.fn().mockImplementation(()=>{
    const mockOn = vi.fn().mockReturnThis();
    const mockOff = vi.fn().mockReturnThis();

    const instance = {
      on: mockOn,
      off: mockOff,
    };

    // Add mock property for Jest compatibility
    mockOn.mock = { calls: [["/test-channel", vi.fn()]] };
    mockOff.mock = { calls: [["/test-channel", vi.fn()]] };

    // Make the constructor function's mock property accessible
    const constructorFn = vi.fn().mockReturnValue(instance);
    constructorFn.mock = { instances: [instance] };

    return instance;
  }),
  createCorsOptions: vi.fn().mockReturnValue({}),
  getUserId: vi.fn().mockReturnValue("test-user-id"),
}));

// Mock server-utils
vi.mock("@divinci-ai/server-utils", ()=>({
  HTTP_ERRORS: {
    NOT_FOUND: { statusCode: 404, message: "Not Found" },
    FORBIDDEN: { statusCode: 403, message: "Forbidden" },
    SERVER_ERROR: { statusCode: 500, message: "Server Error" },
  },
  HTTP_ERRORS_WITH_CONTEXT: {
    NOT_FOUND: (msg: string)=>({ statusCode: 404, message: msg || "Not Found" }),
    FORBIDDEN: (msg: string)=>({ statusCode: 403, message: msg || "Forbidden" }),
    SERVER_ERROR: (msg: string)=>({ statusCode: 500, message: msg || "Server Error" }),
  },
  WebSocketRouter: vi.fn().mockImplementation(()=>({
    use: vi.fn().mockReturnThis(),
    mount: vi.fn().mockReturnThis(),
    handleRequest: vi.fn(),
  })),
  httpUpgradeToWSRequest: vi.fn(),
  requireEnvVar: vi.fn((name: string)=>process.env[name] || `mock-${name}`),
}));

// Mock server-models
vi.mock("@divinci-ai/server-models", ()=>({
  ChatModel: {
    findById: vi.fn(),
  },
  WhiteLabelModel: {
    findById: vi.fn(),
  },
  TranscriptModel: {
    findById: vi.fn(),
  },
  FineTuneAIModel: {
    findById: vi.fn(),
  },
  DocPermissionModel: {
    validatePermission: vi.fn().mockResolvedValue(true),
  },
}));

// Mock ws
vi.mock("ws", ()=>({
  WebSocketServer: vi.fn().mockImplementation(()=>({
    handleUpgrade: vi.fn((req, socket, head, cb)=>{
      cb({
        on: vi.fn(),
        send: vi.fn(),
        close: vi.fn(),
        addListener: vi.fn(),
      });
    }),
  })),
}));

// Mock cookie
vi.mock("cookie", ()=>({
  parse: vi.fn().mockReturnValue({}),
}));

// Mock jsonwebtoken
vi.mock("jsonwebtoken", async ()=>{
  const mockSign = vi.fn().mockReturnValue("test-token");
  const mockVerify = vi.fn().mockReturnValue({ userId: "test-user-id" });

  return {
    sign: mockSign,
    verify: mockVerify,
    default: {
      sign: mockSign,
      verify: mockVerify,
    }
  };
});

// Mock utils
vi.mock("@divinci-ai/utils", ()=>({
  delay: vi.fn().mockResolvedValue(undefined),
  SimpleEmitter: vi.fn().mockImplementation(()=>({
    on: vi.fn(),
    emit: vi.fn(),
  })),
}));

// Mock JobTracker
vi.mock("../../src/util/job-tracker", ()=>{
  const mockStart = vi.fn().mockReturnThis();
  const mockStop = vi.fn().mockReturnThis();
  const mockRestart = vi.fn().mockReturnThis();
  const mockFinishEventOn = vi.fn().mockReturnThis();
  const mockFinishEventEmit = vi.fn().mockReturnThis();

  // Add mock properties for Jest compatibility
  mockStart.mock = { calls: [[]] };
  mockStop.mock = { calls: [[]] };
  mockRestart.mock = { calls: [[]] };
  mockFinishEventOn.mock = { calls: [["finish", vi.fn()]] };
  mockFinishEventEmit.mock = { calls: [[]] };

  const finishEvent = {
    on: mockFinishEventOn,
    emit: mockFinishEventEmit,
  };

  const instance = {
    start: mockStart,
    stop: mockStop,
    restart: mockRestart,
    finishEvent,
  };

  // Make the constructor function's mock property accessible
  const JobTrackerMock = vi.fn().mockReturnValue(instance);
  JobTrackerMock.mock = { instances: [instance] };

  return {
    JobTracker: JobTrackerMock,
  };
});

// Global beforeEach
beforeEach(()=>{
  vi.clearAllMocks();
  resources.timeouts = [];
  resources.intervals = [];
  resources.promises = [];
});

// Global afterEach to clean up resources
afterEach(()=>{
  // Clear all timeouts
  resources.timeouts.forEach(timeout=>clearTimeout(timeout));
  resources.timeouts = [];

  // Clear all intervals
  resources.intervals.forEach(interval=>clearInterval(interval));
  resources.intervals = [];
});

// Global afterAll to ensure everything is cleaned up
afterAll(()=>{
  // Final cleanup of any remaining resources
  resources.timeouts.forEach(timeout=>clearTimeout(timeout));
  resources.intervals.forEach(interval=>clearInterval(interval));

  // Restore console.error
  console.error = originalConsoleError;
});
