import { watchChatUpdate } from '../../../../../../../src/ux/ai-chat/ws-router/chat';

import { HTTP_ERRORS, getParam } from "@divinci-ai/server-utils";
import { ChannelSubscriber, auth0websocket, getRedis } from "@divinci-ai/server-globals";
import { ChatModel } from "@divinci-ai/server-models";
import * as websocket from "websocket";

jest.mock("@divinci-ai/server-utils", () => {
  return {
    getParam: jest.fn(),
    HTTP_ERRORS: { NOT_FOUND: 'Error' }
  };
});

jest.mock("@divinci-ai/server-globals", () => {
  return {
    ChannelSubscriber: jest.fn(),
    auth0websocket: { getUserIdFromWebSocketRequest: jest.fn() },
    getRedis: jest.fn()
  };
});

jest.mock("@divinci-ai/server-models", () => {
  return {
    ChatModel: { findById: jest.fn() }
  };
});

jest.mock('websocket', () => {
  return jest.fn();
});

describe('watchChatUpdate', () => {
  it('should handle NOT_FOUND error', async () => {
    ChatModel.findById.mockResolvedValue(null);
    const nextMock = jest.fn();
    await watchChatUpdate({}, { chatId: '123' }, nextMock);
    expect(nextMock).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('should handle anonymous user', async () => {
    auth0websocket.getUserIdFromWebSocketRequest.mockReturnValue(null);
    ChatModel.findById.mockResolvedValue({ ownerUser: '456' });
    const consoleSpy = jest.spyOn(console, 'log');
    await watchChatUpdate({}, { chatId: '123' }, jest.fn());
    expect(consoleSpy).toHaveBeenCalledWith('anonymous user');
  });

  it('should handle chat owner', async () => {
    auth0websocket.getUserIdFromWebSocketRequest.mockReturnValue('456');
    ChatModel.findById.mockResolvedValue({ ownerUser: '456' });
    const consoleSpy = jest.spyOn(console, 'log');
    await watchChatUpdate({}, { chatId: '123' }, jest.fn());
    expect(consoleSpy).toHaveBeenCalledWith('owner');
  });

  it('should handle non-owner', async () => {
    auth0websocket.getUserIdFromWebSocketRequest.mockReturnValue('789');
    ChatModel.findById.mockResolvedValue({ ownerUser: '456' });
    const consoleSpy = jest.spyOn(console, 'log');
    await watchChatUpdate({}, { chatId: '123' }, jest.fn());
    expect(consoleSpy).toHaveBeenCalledWith('not owner');
  });
});
