const { getMongoose, getRedis, MONGO_CONNECTION_URL } = require("@divinci-ai/server-globals");
const { setupDBs } = require("../../../../../src/setup/database.ts");
const mongoose = require("mongoose");
const redis = require("redis");

jest.mock("@divinci-ai/server-globals", () => ({
  getMongoose: jest.fn(),
  getRedis: jest.fn(),
  MONGO_CONNECTION_URL: "mongodb://localhost:27017/test"
}));

mongoose.connect = jest.fn();
redis.createClient = jest.fn();

describe('setupDBs', () => {

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Successfull connect to Mongo and Redis', async () => {
    getMongoose.mockImplementationOnce(() => ({
      mongoose,
      mongooseConnection: { asPromise: jest.fn().mockResolvedValue() },
    }));

    getRedis.mockImplementationOnce(() => ({
      redisClient: { connect: jest.fn().mockResolvedValue() },
      chatRedisClient: { connect: jest.fn().mockResolvedValue() },
    }));

    const result = await setupDBs();

    expect(getMongoose).toHaveBeenCalledTimes(1);
    expect(getRedis).toHaveBeenCalledTimes(1);
    expect(result).toHaveProperty('mongoose');
    expect(result).toHaveProperty('redisClient');
    expect(result).toHaveProperty('chatRedisClient');
  });

  test('Fail connect to Mongo', async () => {
    getMongoose.mockImplementationOnce(() => ({
      mongoose,
      mongooseConnection: { asPromise: jest.fn().mockRejectedValue(new Error('MongoDB Connect Error')) },
    }));

    expect(setupDBs()).rejects.toThrow('MongoDB Connect Error');
  });

  test('Fail connect to Redis', async () => {
    getMongoose.mockImplementationOnce(() => ({
      mongoose,
      mongooseConnection: { asPromise: jest.fn().mockResolvedValue() },
    }));

    getRedis.mockImplementationOnce(() => ({
      redisClient: { connect: jest.fn().mockRejectedValue(new Error('Redis Connect Error')) },
      chatRedisClient: { connect: jest.fn().mockRejectedValue(new Error('Redis Connect Error')) },
    }));

    expect(setupDBs()).rejects.toThrow('Redis Connect Error');
  });

});
