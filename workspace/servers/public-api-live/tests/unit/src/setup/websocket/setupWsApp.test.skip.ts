import { setupWsApp } from '../../../../../src/setup/websocket';

import { WebSocketRouter } from "@divinci-ai/server-utils";
import { router as aIChatRouter } from "../../../../../src/ux/ai-chat/ws-router";

jest.mock('@divinci-ai/server-utils');
jest.mock('../../../../../src/ux/ai-chat/ws-router');

describe('setupWsApp', () => {
  let mockWsRouter;

  beforeEach(() => {
    mockWsRouter = {
      mount: jest.fn(),
    };

    WebSocketRouter.mockImplementation(() => mockWsRouter);
    console.log = jest.fn();
  });

  it('should create a WebSocketRouter', async () => {
    await setupWsApp();
    expect(WebSocketRouter).toBeCalled();
  });

  it('should mount universal route', async () => {
    await setupWsApp();
    expect(mockWsRouter.mount.mock.calls[0][0]).toEqual(/.*/);
    expect(mockWsRouter.mount.mock.calls[0][1]).toEqual('*');
  });

  it('should mount ai chat route', async () => {
    await setupWsApp();
    expect(mockWsRouter.mount.mock.calls[1][0]).toEqual('/ai-chat/:chatId');
    expect(mockWsRouter.mount.mock.calls[1][1]).toEqual('*');
    expect(mockWsRouter.mount.mock.calls[1][2][0]).toEqual(aIChatRouter);
  });

  it('should reject connection from not allowed origin', async () => {
    const mockRequest = {
      origin: 'not allowed origin',
      reject: jest.fn(),
    };

    await setupWsApp();

    const middleware = mockWsRouter.mount.mock.calls[0][2][0];
    await middleware(mockRequest, null, jest.fn());

    expect(mockRequest.reject).toBeCalled();
    expect(console.log).toBeCalledWith(expect.stringContaining('Connection from origin not allowed origin rejected'));
  });

  it('should not reject connection from allowed origin', async () => {
    const mockRequest = {
      origin: 'allowed origin',
      reject: jest.fn(),
    };

    await setupWsApp();

    const middleware = mockWsRouter.mount.mock.calls[0][2][0];
    await middleware(mockRequest, null, jest.fn());

    expect(mockRequest.reject).not.toBeCalled();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});
