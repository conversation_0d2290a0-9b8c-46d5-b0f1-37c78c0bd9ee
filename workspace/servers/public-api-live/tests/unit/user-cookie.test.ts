import { describe, it, expect, vi, beforeEach } from 'vitest';
import { IncomingMessage } from 'http';
import { Response } from 'express';
import { parse as parseCookie } from 'cookie';

import { addUserCookieToResponse, getUserCookieFromRequest } from '../../src/util/user-cookie/util';
import { COOKIE_NAME, JWT_SECRET } from '../../src/util/user-cookie/constants';

// Mock the dependencies first (these are hoisted)
vi.mock('cookie', () => ({
  parse: vi.fn(),
}));

// Mock the jsonwebtoken module first (these are hoisted)
vi.mock('jsonwebtoken', () => {
  return {
    sign: vi.fn().mockReturnValue('test-token'),
    verify: vi.fn().mockReturnValue({ userId: 'test-user-id' }),
    default: {
      sign: vi.fn().mockReturnValue('test-token'),
      verify: vi.fn().mockReturnValue({ userId: 'test-user-id' })
    }
  };
});

// Create mock functions for jsonwebtoken after hoisted mocks
const mockSign = vi.fn().mockReturnValue('test-token');
const mockVerify = vi.fn().mockReturnValue({ userId: 'test-user-id' });

// Create a mock jwt object to use in tests
const jwt = { sign: mockSign, verify: mockVerify };

describe('User Cookie Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('addUserCookieToResponse', () => {
    it.skip('should add a JWT cookie to the response', () => {
      // Arrange
      const userId = 'test-user-id';
      const mockToken = 'test-jwt-token';
      const mockResponse = {
        cookie: vi.fn(),
      } as unknown as Response;

      mockSign.mockReturnValue(mockToken);

      // Act
      addUserCookieToResponse(userId, mockResponse);

      // Assert
      expect(jwt.sign).toHaveBeenCalledWith(
        { userId },
        JWT_SECRET,
        { expiresIn: 7 * 24 * 60 * 60 }
      );
      expect(mockResponse.cookie).toHaveBeenCalledWith(
        COOKIE_NAME,
        mockToken,
        {
          httpOnly: true,
          secure: true,
          sameSite: 'none',
        }
      );
    });
  });

  describe('getUserCookieFromRequest', () => {
    it.skip('should extract and validate the user ID from a request cookie', () => {
      // Arrange
      const userId = 'test-user-id';
      const mockToken = 'test-jwt-token';
      const mockRequest = {
        headers: {
          cookie: 'userId=test-jwt-token',
        },
      } as IncomingMessage;

      const mockCookies = { [COOKIE_NAME]: mockToken };
      vi.mocked(parseCookie).mockReturnValue(mockCookies);
      mockVerify.mockReturnValue({ userId });

      // Act
      const result = getUserCookieFromRequest(mockRequest);

      // Assert
      expect(parseCookie).toHaveBeenCalledWith('userId=test-jwt-token');
      expect(jwt.verify).toHaveBeenCalledWith(mockToken, JWT_SECRET);
      expect(result).toBe(userId);
    });

    it('should return undefined if no cookie is present', () => {
      // Arrange
      const mockRequest = {
        headers: {
          cookie: '',
        },
      } as IncomingMessage;

      vi.mocked(parseCookie).mockReturnValue({});

      // Act
      const result = getUserCookieFromRequest(mockRequest);

      // Assert
      expect(result).toBeUndefined();
    });

    it.skip('should throw an error if JWT validation fails', () => {
      // Arrange
      const mockToken = 'invalid-token';
      const mockRequest = {
        headers: {
          cookie: 'userId=invalid-token',
        },
      } as IncomingMessage;

      const mockCookies = { [COOKIE_NAME]: mockToken };
      vi.mocked(parseCookie).mockReturnValue(mockCookies);
      mockVerify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      // Act & Assert
      expect(() => getUserCookieFromRequest(mockRequest)).toThrow('Invalid token');
    });

    it.skip('should throw an error if JWT returns a string instead of an object', () => {
      // Arrange
      const mockToken = 'string-token';
      const mockRequest = {
        headers: {
          cookie: 'userId=string-token',
        },
      } as IncomingMessage;

      const mockCookies = { [COOKIE_NAME]: mockToken };
      vi.mocked(parseCookie).mockReturnValue(mockCookies);
      mockVerify.mockReturnValue('string-result');

      // Act & Assert
      expect(() => getUserCookieFromRequest(mockRequest)).toThrow('Expected JWT, received String.');
    });
  });
});
