import { describe, it, expect, vi } from 'vitest';
import { Request, Response } from 'express';

// Simple utility functions to test
function sendJsonResponse(res: Response, data: any, statusCode = 200) {
  res.status(statusCode).json(data);
}

function getQueryParam(req: Request, paramName: string, defaultValue: string = '') {
  return req.query[paramName] as string || defaultValue;
}

describe('HTTP Utilities', () => {
  describe('sendJsonResponse', () => {
    it('should send a JSON response with the correct status code', () => {
      // Arrange
      const mockResponse = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn(),
      } as unknown as Response;
      const data = { message: 'Success' };
      const statusCode = 201;

      // Act
      sendJsonResponse(mockResponse, data, statusCode);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(statusCode);
      expect(mockResponse.json).toHaveBeenCalledWith(data);
    });

    it('should use status code 200 by default', () => {
      // Arrange
      const mockResponse = {
        status: vi.fn().mockReturnThis(),
        json: vi.fn(),
      } as unknown as Response;
      const data = { message: 'Success' };

      // Act
      sendJsonResponse(mockResponse, data);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(data);
    });
  });

  describe('getQueryParam', () => {
    it('should return the query parameter value if it exists', () => {
      // Arrange
      const mockRequest = {
        query: {
          name: 'John',
          age: '30',
        },
      } as unknown as Request;

      // Act
      const result = getQueryParam(mockRequest, 'name');

      // Assert
      expect(result).toBe('John');
    });

    it('should return the default value if the query parameter does not exist', () => {
      // Arrange
      const mockRequest = {
        query: {
          name: 'John',
        },
      } as unknown as Request;
      const defaultValue = 'Unknown';

      // Act
      const result = getQueryParam(mockRequest, 'age', defaultValue);

      // Assert
      expect(result).toBe(defaultValue);
    });

    it('should return an empty string if the query parameter does not exist and no default value is provided', () => {
      // Arrange
      const mockRequest = {
        query: {
          name: 'John',
        },
      } as unknown as Request;

      // Act
      const result = getQueryParam(mockRequest, 'age');

      // Assert
      expect(result).toBe('');
    });
  });
});
