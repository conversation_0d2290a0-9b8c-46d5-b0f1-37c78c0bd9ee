import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import ws from 'k6/ws';

// Custom metrics
const errorRate = new Rate('error_rate');
const wsMessageReceived = new Counter('ws_message_received');
const transcriptUpdateTime = new Trend('transcript_update_time');
const wsConnectionTime = new Trend('ws_connection_time');

// Test configuration
export const options = {
  stages: [
    { duration: '30s', target: 20 },   // Ramp up to 20 users over 30 seconds
    { duration: '1m', target: 20 },    // Stay at 20 users for 1 minute
    { duration: '30s', target: 50 },   // Ramp up to 50 users over 30 seconds
    { duration: '1m', target: 50 },    // Stay at 50 users for 1 minute
    { duration: '30s', target: 0 },    // Ramp down to 0 users over 30 seconds
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'],     // 95% of HTTP requests should be below 200ms
    ws_connecting: ['p(95)<500'],         // 95% of WebSocket connections should be below 500ms
    ws_session_duration: ['p(95)>10000'], // 95% of WebSocket sessions should last at least 10s
    error_rate: ['rate<0.01'],            // Error rate should be below 1%
    ws_message_received: ['count>100'],   // Should receive at least 100 messages in total
  },
};

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Sample data for testing
const testData = [
  { chatId: 'test-chat-1', transcriptId: 'test-transcript-1' },
  { chatId: 'test-chat-2', transcriptId: 'test-transcript-2' },
  { chatId: 'test-chat-3', transcriptId: 'test-transcript-3' },
  { chatId: 'test-chat-4', transcriptId: 'test-transcript-4' },
  { chatId: 'test-chat-5', transcriptId: 'test-transcript-5' },
];

// Main test function
export default function () {
  // First, get a cookie for authentication
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  
  check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  const cookies = cookieResponse.headers['Set-Cookie'];
  
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];
  
  // Connect to the WebSocket for AI chat transcript updates
  const connectStartTime = new Date();
  const chatUrl = `ws://localhost:8080/ai-chat/${testItem.chatId}/transcript`;
  
  const chatRes = ws.connect(chatUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    wsConnectionTime.add((new Date() - connectStartTime));
    
    socket.on('open', () => {
      console.log('Connected to AI chat WebSocket');
    });
    
    socket.on('message', (data) => {
      wsMessageReceived.add(1);
      const startTime = new Date();
      
      // Process the message (in a real test, you'd validate the message content)
      try {
        const message = JSON.parse(data);
        transcriptUpdateTime.add((new Date() - startTime));
      } catch (e) {
        console.error('Failed to parse message:', e);
        errorRate.add(1);
      }
    });
    
    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      errorRate.add(1);
    });
    
    // Keep the connection open for a random time between 10-20 seconds
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 10000) + 10000);
  });
  
  check(chatRes, {
    'AI chat WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
  
  // Connect to the WebSocket for WhiteLabel transcript updates
  const whitelabelId = 'test-whitelabel-1';
  const whitelabelUrl = `ws://localhost:8080/white-label/${whitelabelId}/transcript/${testItem.transcriptId}`;
  
  const whitelabelRes = ws.connect(whitelabelUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    socket.on('open', () => {
      console.log('Connected to WhiteLabel WebSocket');
    });
    
    socket.on('message', (data) => {
      wsMessageReceived.add(1);
      const startTime = new Date();
      
      // Process the message
      try {
        const message = JSON.parse(data);
        transcriptUpdateTime.add((new Date() - startTime));
      } catch (e) {
        console.error('Failed to parse message:', e);
        errorRate.add(1);
      }
    });
    
    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      errorRate.add(1);
    });
    
    // Keep the connection open for a random time between 10-20 seconds
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 10000) + 10000);
  });
  
  check(whitelabelRes, {
    'WhiteLabel WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
  
  // Add a random sleep between 1-5 seconds before the next iteration
  sleep(Math.random() * 4 + 1);
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('WebSocket test completed with the following results:');
  console.log(`- Total WebSocket connections: ${data.metrics.ws_sessions.values.count}`);
  console.log(`- Average WebSocket connection time: ${data.metrics.ws_connecting.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile WebSocket connection time: ${data.metrics.ws_connecting.values.p(95).toFixed(2)}ms`);
  console.log(`- Average WebSocket session duration: ${(data.metrics.ws_session_duration.values.avg / 1000).toFixed(2)}s`);
  console.log(`- Total WebSocket messages received: ${data.metrics.ws_message_received.values.count}`);
  console.log(`- Average transcript update processing time: ${data.metrics.transcript_update_time.values.avg.toFixed(2)}ms`);
  console.log(`- Error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  return {
    'stdout': JSON.stringify(data),
  };
}
