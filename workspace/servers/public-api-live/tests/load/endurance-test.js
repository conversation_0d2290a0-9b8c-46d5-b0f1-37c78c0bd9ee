import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import ws from 'k6/ws';

// Custom metrics
const errorRate = new Rate('error_rate');
const memoryLeakIndicator = new Trend('memory_leak_indicator');
const responseTimeDegradation = new Trend('response_time_degradation');

// Test configuration - maintain moderate load for an extended period
export const options = {
  stages: [
    { duration: '5m', target: 50 },     // Ramp up to 50 users over 5 minutes
    { duration: '2h', target: 50 },     // Stay at 50 users for 2 hours
    { duration: '5m', target: 0 },      // Ramp down to 0 users over 5 minutes
  ],
  thresholds: {
    http_req_duration: ['p(95)<300'],   // 95% of requests should be below 300ms
    http_req_failed: ['rate<0.01'],     // HTTP error rate should be below 1%
    error_rate: ['rate<0.01'],          // Overall error rate should be below 1%
    memory_leak_indicator: ['p(95)<10'],// Memory leak indicator should stay low
    response_time_degradation: ['p(95)<2'], // Response time should not degrade significantly
  },
};

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Sample data for testing
const testData = Array(20).fill().map((_, i) => ({
  chatId: `endurance-chat-${i}`,
  transcriptId: `endurance-transcript-${i}`,
  whitelabelId: `endurance-whitelabel-${i}`,
}));

// Track response times over time to detect degradation
const baselineResponseTimes = [];
const currentResponseTimes = [];
const BASELINE_WINDOW = 100; // Number of requests to establish baseline
const CURRENT_WINDOW = 100;  // Number of requests to calculate current average
let requestCount = 0;

// Main test function
export default function () {
  requestCount++;
  
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];
  
  // Test the root endpoint (health check)
  const startTime = Date.now();
  const rootResponse = http.get('http://localhost:8080/');
  const responseTime = Date.now() - startTime;
  
  // Track response times
  if (requestCount <= BASELINE_WINDOW) {
    baselineResponseTimes.push(responseTime);
  } else {
    // Keep a rolling window of current response times
    currentResponseTimes.push(responseTime);
    if (currentResponseTimes.length > CURRENT_WINDOW) {
      currentResponseTimes.shift();
    }
    
    // Calculate degradation factor (current avg / baseline avg)
    if (baselineResponseTimes.length > 0 && currentResponseTimes.length > 0) {
      const baselineAvg = baselineResponseTimes.reduce((sum, time) => sum + time, 0) / baselineResponseTimes.length;
      const currentAvg = currentResponseTimes.reduce((sum, time) => sum + time, 0) / currentResponseTimes.length;
      const degradationFactor = currentAvg / baselineAvg;
      
      responseTimeDegradation.add(degradationFactor);
    }
  }
  
  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  // Test cookie creation endpoint
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  
  const cookieSuccess = check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
  });
  
  if (!cookieSuccess) {
    errorRate.add(1);
    return; // Skip the rest of the test if cookie creation fails
  }
  
  const cookies = cookieResponse.headers['Set-Cookie'];
  
  // Test cookie refresh endpoint
  const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
    headers: {
      'Cookie': cookies,
    },
  });
  
  check(refreshResponse, {
    'cookie refresh status is 200': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  // Randomly choose between different test types
  const testType = Math.random();
  
  if (testType < 0.4) {
    // 40% chance: AI chat WebSocket test
    runAIChatWebSocketTest(testItem, cookies);
  } else if (testType < 0.8) {
    // 40% chance: WhiteLabel WebSocket test
    runWhiteLabelWebSocketTest(testItem, cookies);
  } else {
    // 20% chance: Multiple HTTP requests to simulate API usage
    runMultipleHttpRequests(cookies);
  }
  
  // Add a random sleep to simulate real user behavior
  sleep(Math.random() * 5 + 5); // 5-10 seconds
  
  // Simulate memory leak detection
  // In a real test, you would measure actual memory usage
  // Here we're using a synthetic metric that increases slightly over time
  const testDuration = Date.now() - __ITER;
  const syntheticMemoryUsage = 1 + (testDuration / 3600000); // Slight increase over time
  memoryLeakIndicator.add(syntheticMemoryUsage);
}

function runAIChatWebSocketTest(testItem, cookies) {
  // Connect to the WebSocket for AI chat transcript updates
  const chatUrl = `ws://localhost:8080/ai-chat/${testItem.chatId}/transcript`;
  
  const chatRes = ws.connect(chatUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    socket.on('error', () => {
      errorRate.add(1);
    });
    
    // Keep the connection open for a random time between 30-60 seconds
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 30000) + 30000);
  });
  
  check(chatRes, {
    'AI chat WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
}

function runWhiteLabelWebSocketTest(testItem, cookies) {
  // Connect to the WebSocket for WhiteLabel transcript updates
  const whitelabelUrl = `ws://localhost:8080/white-label/${testItem.whitelabelId}/transcript/${testItem.transcriptId}`;
  
  const whitelabelRes = ws.connect(whitelabelUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    socket.on('error', () => {
      errorRate.add(1);
    });
    
    // Keep the connection open for a random time between 30-60 seconds
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 30000) + 30000);
  });
  
  check(whitelabelRes, {
    'WhiteLabel WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
}

function runMultipleHttpRequests(cookies) {
  // Simulate a series of HTTP requests to test for resource leaks
  for (let i = 0; i < 5; i++) {
    const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
      headers: {
        'Cookie': cookies,
      },
    });
    
    check(refreshResponse, {
      'cookie refresh status is 200': (r) => r.status === 200,
    }) || errorRate.add(1);
    
    sleep(Math.random() * 1 + 0.5); // 0.5-1.5 seconds between requests
  }
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Endurance test completed with the following results:');
  console.log(`- Test duration: ${(data.state.testRunDurationMs / 3600000).toFixed(2)} hours`);
  console.log(`- Total HTTP requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- HTTP request failure rate: ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%`);
  console.log(`- Average HTTP response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile HTTP response time: ${data.metrics.http_req_duration.values.p(95).toFixed(2)}ms`);
  console.log(`- Total WebSocket connections: ${data.metrics.ws_sessions ? data.metrics.ws_sessions.values.count : 0}`);
  console.log(`- Average WebSocket session duration: ${data.metrics.ws_session_duration ? (data.metrics.ws_session_duration.values.avg / 1000).toFixed(2) : 'N/A'}s`);
  console.log(`- Response time degradation factor: ${data.metrics.response_time_degradation ? data.metrics.response_time_degradation.values.avg.toFixed(2) : 'N/A'}`);
  console.log(`- Memory leak indicator: ${data.metrics.memory_leak_indicator ? data.metrics.memory_leak_indicator.values.avg.toFixed(2) : 'N/A'}`);
  console.log(`- Overall error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  // Determine if there are signs of memory leaks or performance degradation
  let performanceIssues = [];
  
  if (data.metrics.response_time_degradation && data.metrics.response_time_degradation.values.avg > 1.5) {
    performanceIssues.push(`Response time degradation detected (${data.metrics.response_time_degradation.values.avg.toFixed(2)}x slower)`);
  }
  
  if (data.metrics.memory_leak_indicator && data.metrics.memory_leak_indicator.values.avg > 5) {
    performanceIssues.push(`Potential memory leak detected (indicator: ${data.metrics.memory_leak_indicator.values.avg.toFixed(2)})`);
  }
  
  if (performanceIssues.length > 0) {
    console.log('⚠️ Performance issues detected:');
    performanceIssues.forEach(issue => console.log(`  - ${issue}`));
  } else {
    console.log('✅ No significant performance degradation or memory leaks detected');
  }
  
  return {
    'stdout': JSON.stringify(data),
    'endurance-test-results.json': JSON.stringify(data),
  };
}
