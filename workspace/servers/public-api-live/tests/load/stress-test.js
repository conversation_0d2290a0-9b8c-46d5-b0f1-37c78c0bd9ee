import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';
import ws from 'k6/ws';

// Custom metrics
const errorRate = new Rate('error_rate');
const httpErrorRate = new Rate('http_error_rate');
const wsErrorRate = new Rate('ws_error_rate');

// Test configuration - gradually increase load until the system breaks
export const options = {
  stages: [
    { duration: '2m', target: 100 },    // Ramp up to 100 users over 2 minutes
    { duration: '5m', target: 100 },    // Stay at 100 users for 5 minutes
    { duration: '2m', target: 200 },    // Ramp up to 200 users over 2 minutes
    { duration: '5m', target: 200 },    // Stay at 200 users for 5 minutes
    { duration: '2m', target: 300 },    // Ramp up to 300 users over 2 minutes
    { duration: '5m', target: 300 },    // Stay at 300 users for 5 minutes
    { duration: '2m', target: 400 },    // Ramp up to 400 users over 2 minutes
    { duration: '5m', target: 400 },    // Stay at 400 users for 5 minutes
    { duration: '10m', target: 0 },     // Ramp down to 0 users over 10 minutes
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'],   // 95% of requests should be below 500ms
    http_req_failed: ['rate<0.05'],     // HTTP error rate should be below 5%
    ws_connecting: ['p(95)<1000'],      // 95% of WebSocket connections should be below 1s
    error_rate: ['rate<0.05'],          // Overall error rate should be below 5%
    http_error_rate: ['rate<0.05'],     // HTTP error rate should be below 5%
    ws_error_rate: ['rate<0.05'],       // WebSocket error rate should be below 5%
  },
};

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Sample data for testing
const testData = Array(50).fill().map((_, i) => ({
  chatId: `stress-chat-${i}`,
  transcriptId: `stress-transcript-${i}`,
  whitelabelId: `stress-whitelabel-${i}`,
}));

// Main test function
export default function () {
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];
  
  // Randomly choose between HTTP and WebSocket tests
  const testType = Math.random() < 0.5 ? 'http' : 'ws';
  
  if (testType === 'http') {
    // HTTP Test
    runHttpTest(testItem);
  } else {
    // WebSocket Test
    runWebSocketTest(testItem);
  }
  
  // Add a short sleep to simulate real user behavior
  sleep(Math.random() * 2 + 0.5);
}

function runHttpTest(testItem) {
  // Test the root endpoint (health check)
  const rootResponse = http.get('http://localhost:8080/');
  
  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
  }) || httpErrorRate.add(1);
  
  // Test cookie creation endpoint
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  
  const cookieSuccess = check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
  });
  
  if (!cookieSuccess) {
    httpErrorRate.add(1);
    errorRate.add(1);
    return; // Skip the rest of the test if cookie creation fails
  }
  
  // Test cookie refresh endpoint
  const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
    headers: {
      'Cookie': cookieResponse.headers['Set-Cookie'],
    },
  });
  
  check(refreshResponse, {
    'cookie refresh status is 200': (r) => r.status === 200,
  }) || httpErrorRate.add(1);
}

function runWebSocketTest(testItem) {
  // First, get a cookie for authentication
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  
  const cookieSuccess = check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
  });
  
  if (!cookieSuccess) {
    httpErrorRate.add(1);
    errorRate.add(1);
    return; // Skip the rest of the test if cookie creation fails
  }
  
  const cookies = cookieResponse.headers['Set-Cookie'];
  
  // Randomly choose between AI chat and WhiteLabel WebSocket
  const wsType = Math.random() < 0.5 ? 'chat' : 'whitelabel';
  
  if (wsType === 'chat') {
    // Connect to the WebSocket for AI chat transcript updates
    const chatUrl = `ws://localhost:8080/ai-chat/${testItem.chatId}/transcript`;
    
    const chatRes = ws.connect(chatUrl, {
      headers: {
        'Cookie': cookies,
      },
    }, function (socket) {
      socket.on('error', () => {
        wsErrorRate.add(1);
        errorRate.add(1);
      });
      
      // Keep the connection open for a random time between 5-15 seconds
      socket.setTimeout(function () {
        socket.close();
      }, (Math.random() * 10000) + 5000);
    });
    
    check(chatRes, {
      'AI chat WebSocket connected successfully': (r) => r && r.status === 101,
    }) || wsErrorRate.add(1);
  } else {
    // Connect to the WebSocket for WhiteLabel transcript updates
    const whitelabelUrl = `ws://localhost:8080/white-label/${testItem.whitelabelId}/transcript/${testItem.transcriptId}`;
    
    const whitelabelRes = ws.connect(whitelabelUrl, {
      headers: {
        'Cookie': cookies,
      },
    }, function (socket) {
      socket.on('error', () => {
        wsErrorRate.add(1);
        errorRate.add(1);
      });
      
      // Keep the connection open for a random time between 5-15 seconds
      socket.setTimeout(function () {
        socket.close();
      }, (Math.random() * 10000) + 5000);
    });
    
    check(whitelabelRes, {
      'WhiteLabel WebSocket connected successfully': (r) => r && r.status === 101,
    }) || wsErrorRate.add(1);
  }
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Stress test completed with the following results:');
  console.log(`- Total HTTP requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- HTTP request failure rate: ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%`);
  console.log(`- Average HTTP response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile HTTP response time: ${data.metrics.http_req_duration.values.p(95).toFixed(2)}ms`);
  console.log(`- Total WebSocket connections: ${data.metrics.ws_sessions.values.count}`);
  console.log(`- Average WebSocket connection time: ${data.metrics.ws_connecting.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile WebSocket connection time: ${data.metrics.ws_connecting.values.p(95).toFixed(2)}ms`);
  console.log(`- HTTP error rate: ${(data.metrics.http_error_rate.values.rate * 100).toFixed(2)}%`);
  console.log(`- WebSocket error rate: ${(data.metrics.ws_error_rate.values.rate * 100).toFixed(2)}%`);
  console.log(`- Overall error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  // Determine the breaking point
  let breakingPoint = 'Not reached';
  const httpErrorRate = data.metrics.http_error_rate.values.rate;
  const wsErrorRate = data.metrics.ws_error_rate.values.rate;
  
  if (httpErrorRate > 0.05 || wsErrorRate > 0.05) {
    // Find the stage where errors started to exceed threshold
    const stages = options.stages;
    let cumulativeDuration = 0;
    let previousTarget = 0;
    
    for (const stage of stages) {
      cumulativeDuration += parseDuration(stage.duration);
      if (cumulativeDuration >= data.state.testRunDurationMs) {
        breakingPoint = `Between ${previousTarget} and ${stage.target} users`;
        break;
      }
      previousTarget = stage.target;
    }
  }
  
  console.log(`- Breaking point: ${breakingPoint}`);
  
  return {
    'stdout': JSON.stringify(data),
    'stress-test-results.json': JSON.stringify(data),
  };
}

// Helper function to parse duration strings like '2m' into milliseconds
function parseDuration(durationStr) {
  const unit = durationStr.slice(-1);
  const value = parseInt(durationStr.slice(0, -1));
  
  switch (unit) {
    case 's':
      return value * 1000;
    case 'm':
      return value * 60 * 1000;
    case 'h':
      return value * 60 * 60 * 1000;
    default:
      return value;
  }
}
