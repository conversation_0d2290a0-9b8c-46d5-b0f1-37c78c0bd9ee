# Load Testing for Public API Live Server

This directory contains load tests for the public-api-live server using k6, a modern load testing tool.

## Prerequisites

1. Install k6: https://k6.io/docs/getting-started/installation/

   ```bash
   # macOS
   brew install k6

   # Linux
   sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
   echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
   sudo apt-get update
   sudo apt-get install k6
   ```

2. Make sure the public-api-live server is running locally:

   ```bash
   npm run start:dev
   ```

## Running the Tests

### Basic Load Test

```bash
k6 run basic-load.js
```

### WebSocket Connection Test

```bash
k6 run websocket-load.js
```

### Stress Test

```bash
k6 run stress-test.js
```

### Spike Test

```bash
k6 run spike-test.js
```

### Endurance Test

```bash
k6 run endurance-test.js
```

## Test Scenarios

1. **Basic Load Test**: Tests the HTTP endpoints under normal load
2. **WebSocket Connection Test**: Tests WebSocket connections and message handling
3. **Stress Test**: Gradually increases load to find the breaking point
4. **Spike Test**: Suddenly increases load to simulate traffic spikes
5. **Endurance Test**: Maintains moderate load for an extended period

## Interpreting Results

After each test, k6 will output metrics including:

- **http_req_duration**: Time taken for HTTP requests
- **http_req_failed**: Rate of failed HTTP requests
- **ws_session_duration**: Duration of WebSocket sessions
- **ws_connecting**: Time taken to establish WebSocket connections
- **vus**: Virtual users (simulated concurrent users)
- **iterations**: Total number of test iterations completed

## Custom Metrics

The tests also track custom metrics:

- **ws_message_received**: Count of WebSocket messages received
- **cookie_creation_time**: Time taken to create user cookies
- **transcript_update_time**: Time taken to receive transcript updates

## Thresholds

Each test defines thresholds that determine pass/fail criteria:

- HTTP response times under 200ms for p95
- WebSocket connection times under 500ms for p95
- Error rates below 1%
- Successful message receipt rate above 95%
