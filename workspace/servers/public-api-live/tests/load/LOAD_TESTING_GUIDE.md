# Load Testing Guide for Public API Live Server

This guide provides information on how to run, interpret, and act on load tests for the public-api-live server.

## Types of Load Tests

### 1. Basic Load Test (`basic-load.js`)
- **Purpose**: Test HTTP endpoints under normal load conditions
- **Duration**: ~5 minutes
- **User Simulation**: Ramps up to 100 users
- **What it Tests**: Root endpoint, cookie creation, cookie refresh
- **When to Run**: After any significant changes to HTTP endpoints

### 2. WebSocket Load Test (`websocket-load.js`)
- **Purpose**: Test WebSocket connections and message handling
- **Duration**: ~5 minutes
- **User Simulation**: Ramps up to 50 users with WebSocket connections
- **What it Tests**: AI chat WebSocket, WhiteLabel WebSocket
- **When to Run**: After any changes to WebSocket handlers

### 3. Stress Test (`stress-test.js`)
- **Purpose**: Find the breaking point of the server
- **Duration**: ~30 minutes
- **User Simulation**: Gradually increases to 400 users
- **What it Tests**: System stability under increasing load
- **When to Run**: Before major releases or when capacity planning

### 4. Spike Test (`spike-test.js`)
- **Purpose**: Test how the server handles sudden traffic spikes
- **Duration**: ~15 minutes
- **User Simulation**: Sudden spikes to 200, 300, and 400 users
- **What it Tests**: System recovery after traffic spikes
- **When to Run**: To validate auto-scaling configurations

### 5. Endurance Test (`endurance-test.js`)
- **Purpose**: Test for memory leaks and performance degradation over time
- **Duration**: 2+ hours
- **User Simulation**: Maintains 50 users for an extended period
- **What it Tests**: Long-term stability and resource usage
- **When to Run**: Before major releases or when suspecting memory leaks

## Running the Tests

### Prerequisites
1. Install k6: https://k6.io/docs/getting-started/installation/
2. Start the public-api-live server: `npm run start:dev`

### Running Individual Tests
```bash
cd tests/load
k6 run basic-load.js
```

### Running All Tests
```bash
cd tests/load
./run-load-tests.sh
```

## Interpreting Results

### Key Metrics to Monitor

1. **Response Time**
   - **http_req_duration**: Time taken for HTTP requests
   - **Good**: p95 < 200ms
   - **Acceptable**: p95 < 500ms
   - **Poor**: p95 > 500ms

2. **Error Rate**
   - **http_req_failed**: Rate of failed HTTP requests
   - **Good**: < 1%
   - **Acceptable**: < 5%
   - **Poor**: > 5%

3. **WebSocket Performance**
   - **ws_connecting**: Time to establish WebSocket connections
   - **Good**: p95 < 500ms
   - **Acceptable**: p95 < 1000ms
   - **Poor**: p95 > 1000ms

4. **System Stability**
   - **memory_leak_indicator**: Synthetic metric for memory usage
   - **Good**: < 5
   - **Acceptable**: < 10
   - **Poor**: > 10

5. **Recovery Time**
   - **recovery_time**: Time to recover after a spike
   - **Good**: < 10s
   - **Acceptable**: < 30s
   - **Poor**: > 30s

### Breaking Point Analysis

The stress test will help identify the breaking point of your system. Look for:

1. **When Error Rates Increase**: The point where error rates exceed 5%
2. **When Response Times Spike**: The point where response times increase dramatically
3. **When WebSocket Connections Fail**: The point where WebSocket connections start failing

This breaking point represents your system's maximum capacity under the test conditions.

## Common Issues and Solutions

### 1. High Response Times

**Symptoms**:
- p95 response times > 500ms
- Increasing response times during stress tests

**Possible Causes**:
- Database queries not optimized
- Insufficient server resources
- Blocking operations in request handlers

**Solutions**:
- Add database indexes
- Optimize query patterns
- Increase server resources
- Use async/non-blocking operations

### 2. High Error Rates

**Symptoms**:
- Error rates > 5%
- Increasing errors during stress tests

**Possible Causes**:
- Connection limits reached
- Timeouts
- Resource exhaustion

**Solutions**:
- Increase connection pool sizes
- Implement retry mechanisms
- Scale horizontally
- Implement circuit breakers

### 3. WebSocket Connection Issues

**Symptoms**:
- WebSocket connections failing
- High ws_connecting times

**Possible Causes**:
- Too many concurrent connections
- Memory leaks in connection handlers
- Connection limits reached

**Solutions**:
- Implement connection limits
- Ensure proper cleanup of closed connections
- Scale horizontally
- Use WebSocket clustering

### 4. Memory Leaks

**Symptoms**:
- Increasing memory usage over time
- Degrading performance in endurance tests

**Possible Causes**:
- Unclosed connections
- Event listeners not removed
- Objects not garbage collected

**Solutions**:
- Ensure all connections are properly closed
- Remove event listeners when no longer needed
- Use WeakMap/WeakSet for caches
- Implement periodic cleanup routines

## Scaling Strategies

Based on load test results, consider these scaling strategies:

### 1. Vertical Scaling
- Increase CPU/memory on existing servers
- Good for: CPU-bound operations, memory-intensive operations
- When to use: Breaking point is CPU or memory related

### 2. Horizontal Scaling
- Add more server instances
- Good for: Connection-bound operations, I/O-bound operations
- When to use: Breaking point is connection limits or throughput

### 3. Database Scaling
- Optimize database queries
- Add read replicas
- Implement sharding
- When to use: Breaking point is database performance

### 4. Caching
- Implement Redis caching
- Add in-memory caches
- When to use: High read-to-write ratio operations

## Continuous Load Testing

For best results, integrate load testing into your CI/CD pipeline:

1. Run basic load tests on every PR
2. Run comprehensive tests before major releases
3. Schedule periodic endurance tests to catch memory leaks
4. Compare results over time to identify performance regressions

## Additional Resources

- [k6 Documentation](https://k6.io/docs/)
- [WebSocket Performance Tuning](https://k6.io/docs/using-k6/protocols/websockets/)
- [Node.js Performance Tuning](https://nodejs.org/en/docs/guides/dont-block-the-event-loop/)
- [Redis Performance Best Practices](https://redis.io/topics/latency)
- [MongoDB Performance Best Practices](https://docs.mongodb.com/manual/core/query-optimization/)
