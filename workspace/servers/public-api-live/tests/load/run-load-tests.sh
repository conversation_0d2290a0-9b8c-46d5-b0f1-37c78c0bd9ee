#!/bin/bash

# Run load tests for the public-api-live server
# This script runs all the load tests in sequence

# Set the output directory for test results
RESULTS_DIR="./results"
mkdir -p $RESULTS_DIR

# Function to run a test and save results
run_test() {
  TEST_NAME=$1
  TEST_FILE=$2
  DURATION=$3
  
  echo "========================================================"
  echo "Running $TEST_NAME (estimated duration: $DURATION)"
  echo "========================================================"
  
  # Run the test and save results
  k6 run $TEST_FILE --out json=$RESULTS_DIR/$TEST_NAME-results.json
  
  echo "Test completed. Results saved to $RESULTS_DIR/$TEST_NAME-results.json"
  echo ""
}

# Check if k6 is installed
if ! command -v k6 &> /dev/null; then
  echo "k6 is not installed. Please install it first:"
  echo "  macOS: brew install k6"
  echo "  Linux: follow instructions at https://k6.io/docs/getting-started/installation/"
  exit 1
fi

# Check if the server is running
if ! curl -s http://localhost:8080/ > /dev/null; then
  echo "The public-api-live server is not running. Please start it first:"
  echo "  npm run start:dev"
  exit 1
fi

# Run the tests
echo "Starting load tests for public-api-live server"
echo "Results will be saved to $RESULTS_DIR"
echo ""

# Basic load test (short duration)
run_test "basic-load" "basic-load.js" "5 minutes"

# WebSocket load test (short duration)
run_test "websocket-load" "websocket-load.js" "5 minutes"

# Ask if the user wants to run the longer tests
read -p "Do you want to run the longer tests (stress, spike, endurance)? (y/n) " RUN_LONG_TESTS

if [[ $RUN_LONG_TESTS == "y" || $RUN_LONG_TESTS == "Y" ]]; then
  # Stress test (longer duration)
  run_test "stress-test" "stress-test.js" "30+ minutes"
  
  # Spike test (medium duration)
  run_test "spike-test" "spike-test.js" "15+ minutes"
  
  # Ask if the user wants to run the endurance test (very long)
  read -p "Do you want to run the endurance test (2+ hours)? (y/n) " RUN_ENDURANCE
  
  if [[ $RUN_ENDURANCE == "y" || $RUN_ENDURANCE == "Y" ]]; then
    # Endurance test (very long duration)
    run_test "endurance-test" "endurance-test.js" "2+ hours"
  fi
fi

echo "All tests completed. Results are available in $RESULTS_DIR"

# Generate a summary report
echo "Generating summary report..."
echo "# Load Test Results Summary" > $RESULTS_DIR/summary.md
echo "" >> $RESULTS_DIR/summary.md
echo "Generated on: $(date)" >> $RESULTS_DIR/summary.md
echo "" >> $RESULTS_DIR/summary.md

echo "## Test Results" >> $RESULTS_DIR/summary.md
echo "" >> $RESULTS_DIR/summary.md
echo "| Test | HTTP Requests | Avg Response Time | 95th Percentile | Error Rate |" >> $RESULTS_DIR/summary.md
echo "|------|--------------|-------------------|-----------------|------------|" >> $RESULTS_DIR/summary.md

# Extract key metrics from each test result
for TEST_NAME in "basic-load" "websocket-load" "stress-test" "spike-test" "endurance-test"; do
  if [ -f "$RESULTS_DIR/$TEST_NAME-results.json" ]; then
    # Use jq to extract metrics (if available)
    if command -v jq &> /dev/null; then
      HTTP_REQS=$(jq '.metrics.http_reqs.values.count' $RESULTS_DIR/$TEST_NAME-results.json 2>/dev/null || echo "N/A")
      AVG_RESP=$(jq '.metrics.http_req_duration.values.avg' $RESULTS_DIR/$TEST_NAME-results.json 2>/dev/null || echo "N/A")
      if [ "$AVG_RESP" != "N/A" ]; then AVG_RESP=$(printf "%.2f ms" $AVG_RESP); fi
      P95_RESP=$(jq '.metrics.http_req_duration.values["p(95)"]' $RESULTS_DIR/$TEST_NAME-results.json 2>/dev/null || echo "N/A")
      if [ "$P95_RESP" != "N/A" ]; then P95_RESP=$(printf "%.2f ms" $P95_RESP); fi
      ERROR_RATE=$(jq '.metrics.error_rate.values.rate' $RESULTS_DIR/$TEST_NAME-results.json 2>/dev/null || echo "N/A")
      if [ "$ERROR_RATE" != "N/A" ]; then ERROR_RATE=$(printf "%.2f%%" $(echo "$ERROR_RATE * 100" | bc)); fi
    else
      HTTP_REQS="N/A (jq not installed)"
      AVG_RESP="N/A (jq not installed)"
      P95_RESP="N/A (jq not installed)"
      ERROR_RATE="N/A (jq not installed)"
    fi
    
    echo "| $TEST_NAME | $HTTP_REQS | $AVG_RESP | $P95_RESP | $ERROR_RATE |" >> $RESULTS_DIR/summary.md
  fi
done

echo "" >> $RESULTS_DIR/summary.md
echo "## Recommendations" >> $RESULTS_DIR/summary.md
echo "" >> $RESULTS_DIR/summary.md
echo "Based on the test results, consider the following optimizations:" >> $RESULTS_DIR/summary.md
echo "" >> $RESULTS_DIR/summary.md
echo "1. Review any tests with error rates above 1%" >> $RESULTS_DIR/summary.md
echo "2. Optimize endpoints with response times above 200ms" >> $RESULTS_DIR/summary.md
echo "3. Check for memory leaks if endurance test shows degradation" >> $RESULTS_DIR/summary.md
echo "4. Consider scaling horizontally if stress test shows early breaking points" >> $RESULTS_DIR/summary.md

echo "Summary report generated: $RESULTS_DIR/summary.md"
