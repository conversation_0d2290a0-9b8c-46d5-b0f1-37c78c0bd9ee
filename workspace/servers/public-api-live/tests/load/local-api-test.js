import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';
import ws from 'k6/ws';

// Custom metrics
const errorRate = new Rate('error_rate');
const cookieCreationTime = new Trend('cookie_creation_time');
const wsConnectionTime = new Trend('ws_connection_time');

// Test configuration
export const options = {
  stages: [
    { duration: '30s', target: 10 },   // Ramp up to 10 users over 30 seconds
    { duration: '1m', target: 10 },    // Stay at 10 users for 1 minute
    { duration: '30s', target: 20 },   // Ramp up to 20 users over 30 seconds
    { duration: '1m', target: 20 },    // Stay at 20 users for 1 minute
    { duration: '30s', target: 0 },    // Ramp down to 0 users over 30 seconds
  ],
  thresholds: {
    http_req_duration: ['p(95)<500'], // 95% of requests should be below 500ms
    error_rate: ['rate<0.05'],        // Error rate should be below 5%
  },
};

// Base URL for the local API Live server
const BASE_URL = 'http://localhost:9081';

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Sample data for testing
const testData = [
  { chatId: 'test-chat-1', transcriptId: 'test-transcript-1', whitelabelId: 'test-whitelabel-1' },
  { chatId: 'test-chat-2', transcriptId: 'test-transcript-2', whitelabelId: 'test-whitelabel-2' },
  { chatId: 'test-chat-3', transcriptId: 'test-transcript-3', whitelabelId: 'test-whitelabel-3' },
];

// Main test function
export default function () {
  // Test the root endpoint (health check)
  const rootResponse = http.get(`${BASE_URL}/`);

  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
    'root response has correct structure': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.hello === 'world' && body.whoami === 'an api live server!';
      } catch (e) {
        console.error('Failed to parse root response:', e);
        return false;
      }
    },
  }) || errorRate.add(1);

  // Test cookie creation endpoint
  const cookieStartTime = new Date();
  const cookieResponse = http.get(`${BASE_URL}/cookie/new`, {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  cookieCreationTime.add((new Date() - cookieStartTime));

  const cookieSuccess = check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
    'cookie creation response is correct': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.status === 'ok';
      } catch (e) {
        console.error('Failed to parse cookie response:', e);
        return false;
      }
    },
  });

  if (!cookieSuccess) {
    errorRate.add(1);
    console.log('Cookie creation failed with status:', cookieResponse.status);
    console.log('Response body:', cookieResponse.body);
    // Skip the rest of the test if cookie creation fails
    return;
  }

  const cookies = cookieResponse.headers['Set-Cookie'];

  // Test cookie refresh endpoint
  const refreshResponse = http.get(`${BASE_URL}/cookie/refresh`, {
    headers: {
      'Cookie': cookies,
    },
  });

  check(refreshResponse, {
    'cookie refresh status is 200': (r) => r.status === 200,
    'cookie refresh response is correct': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.status === 'ok';
      } catch (e) {
        console.error('Failed to parse refresh response:', e);
        return false;
      }
    },
  }) || errorRate.add(1);

  // Randomly choose between HTTP and WebSocket tests
  const testType = Math.random();

  if (testType < 0.3) {
    // 30% chance: Test WebSocket connection for AI chat
    testAIChatWebSocket(cookies);
  } else if (testType < 0.6) {
    // 30% chance: Test WebSocket connection for WhiteLabel
    testWhiteLabelWebSocket(cookies);
  } else {
    // 40% chance: Just do HTTP tests
    // Already done above
  }

  // Add a random sleep between 1-5 seconds to simulate real user behavior
  sleep(Math.random() * 4 + 1);
}

function testAIChatWebSocket(cookies) {
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];

  // Connect to the WebSocket for AI chat transcript updates
  const connectStartTime = new Date();
  const chatUrl = `ws://localhost:9081/ai-chat/${testItem.chatId}/transcript`;

  const chatRes = ws.connect(chatUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    wsConnectionTime.add((new Date() - connectStartTime));

    socket.on('open', () => {
      console.log('Connected to AI chat WebSocket');
    });

    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      errorRate.add(1);
    });

    // Keep the connection open for a short time (3-8 seconds)
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 5000) + 3000);
  });

  check(chatRes, {
    'AI chat WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
}

function testWhiteLabelWebSocket(cookies) {
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];

  // Connect to the WebSocket for WhiteLabel transcript updates
  const whitelabelUrl = `ws://localhost:9081/white-label/${testItem.whitelabelId}/transcript/${testItem.transcriptId}`;

  const whitelabelRes = ws.connect(whitelabelUrl, {
    headers: {
      'Cookie': cookies,
    },
  }, function (socket) {
    socket.on('open', () => {
      console.log('Connected to WhiteLabel WebSocket');
    });

    socket.on('error', (e) => {
      console.error('WebSocket error:', e);
      errorRate.add(1);
    });

    // Keep the connection open for a short time (3-8 seconds)
    socket.setTimeout(function () {
      socket.close();
    }, (Math.random() * 5000) + 3000);
  });

  check(whitelabelRes, {
    'WhiteLabel WebSocket connected successfully': (r) => r && r.status === 101,
  }) || errorRate.add(1);
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Test completed with the following results:');
  console.log(`- Total HTTP requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- Failed HTTP requests: ${data.metrics.http_req_failed.values.passes}`);
  console.log(`- Average HTTP response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);

  // Check if WebSocket metrics exist (they might not if no WebSocket connections were made)
  if (data.metrics.ws_sessions) {
    console.log(`- Total WebSocket connections: ${data.metrics.ws_sessions.values.count}`);
    console.log(`- WebSocket connection success rate: ${(1 - data.metrics.ws_connecting_failed.values.rate) * 100}%`);
  }

  console.log(`- Overall error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);

  return {
    'stdout': JSON.stringify(data),
  };
}
