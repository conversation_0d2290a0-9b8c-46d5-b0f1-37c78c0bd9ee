import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';
import ws from 'k6/ws';

// Custom metrics
const errorRate = new Rate('error_rate');
const recoveryTime = new Rate('recovery_time');

// Test configuration - simulate sudden traffic spikes
export const options = {
  stages: [
    { duration: '1m', target: 10 },     // Baseline: 10 users
    { duration: '10s', target: 200 },   // Spike to 200 users in 10 seconds
    { duration: '3m', target: 200 },    // Stay at 200 users for 3 minutes
    { duration: '30s', target: 10 },    // Scale back to baseline
    { duration: '2m', target: 10 },     // Stay at baseline for 2 minutes
    { duration: '10s', target: 300 },   // Spike to 300 users in 10 seconds
    { duration: '3m', target: 300 },    // Stay at 300 users for 3 minutes
    { duration: '30s', target: 10 },    // Scale back to baseline
    { duration: '2m', target: 10 },     // Stay at baseline for 2 minutes
    { duration: '10s', target: 400 },   // Spike to 400 users in 10 seconds
    { duration: '3m', target: 400 },    // Stay at 400 users for 3 minutes
    { duration: '1m', target: 0 },      // Ramp down to 0 users
  ],
  thresholds: {
    http_req_duration: ['p(95)<1000'],  // 95% of requests should be below 1s during spikes
    http_req_failed: ['rate<0.1'],      // HTTP error rate should be below 10% during spikes
    error_rate: ['rate<0.1'],           // Overall error rate should be below 10% during spikes
    recovery_time: ['p(95)<30000'],     // System should recover within 30 seconds after spike
  },
};

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Sample data for testing
const testData = Array(100).fill().map((_, i) => ({
  chatId: `spike-chat-${i}`,
  transcriptId: `spike-transcript-${i}`,
  whitelabelId: `spike-whitelabel-${i}`,
}));

// Track if we're in a spike period
let inSpike = false;
let spikeStartTime = 0;
let lastResponseTime = 0;

// Main test function
export default function () {
  // Determine if we're in a spike based on VU count
  const currentVUs = __VU;
  
  if (currentVUs > 50 && !inSpike) {
    // We've entered a spike
    inSpike = true;
    spikeStartTime = Date.now();
    console.log(`Spike started at ${new Date().toISOString()} with ${currentVUs} VUs`);
  } else if (currentVUs <= 50 && inSpike) {
    // We've exited a spike
    inSpike = false;
    const spikeDuration = Date.now() - spikeStartTime;
    console.log(`Spike ended at ${new Date().toISOString()}, lasted ${spikeDuration / 1000} seconds`);
    
    // Measure recovery time (time until response times return to normal)
    const recoveryStartTime = Date.now();
    
    // We'll consider the system recovered when response times are below 200ms
    if (lastResponseTime > 200) {
      recoveryTime.add(Date.now() - recoveryStartTime);
    }
  }
  
  // Select a random test data item
  const testItem = testData[Math.floor(Math.random() * testData.length)];
  
  // Test the root endpoint (health check)
  const startTime = Date.now();
  const rootResponse = http.get('http://localhost:8080/');
  lastResponseTime = Date.now() - startTime;
  
  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
  }) || errorRate.add(1);
  
  // Test cookie creation endpoint
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  
  const cookieSuccess = check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
  });
  
  if (!cookieSuccess) {
    errorRate.add(1);
    return; // Skip the rest of the test if cookie creation fails
  }
  
  const cookies = cookieResponse.headers['Set-Cookie'];
  
  // During spikes, focus on HTTP endpoints to generate more load
  if (inSpike) {
    // Test cookie refresh endpoint multiple times
    for (let i = 0; i < 3; i++) {
      const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
        headers: {
          'Cookie': cookies,
        },
      });
      
      check(refreshResponse, {
        'cookie refresh status is 200': (r) => r.status === 200,
      }) || errorRate.add(1);
    }
  } else {
    // During normal periods, mix HTTP and WebSocket tests
    
    // Test cookie refresh endpoint
    const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
      headers: {
        'Cookie': cookies,
      },
    });
    
    check(refreshResponse, {
      'cookie refresh status is 200': (r) => r.status === 200,
    }) || errorRate.add(1);
    
    // Randomly choose between AI chat and WhiteLabel WebSocket
    const wsType = Math.random() < 0.5 ? 'chat' : 'whitelabel';
    
    if (wsType === 'chat') {
      // Connect to the WebSocket for AI chat transcript updates
      const chatUrl = `ws://localhost:8080/ai-chat/${testItem.chatId}/transcript`;
      
      const chatRes = ws.connect(chatUrl, {
        headers: {
          'Cookie': cookies,
        },
      }, function (socket) {
        socket.on('error', () => {
          errorRate.add(1);
        });
        
        // Keep the connection open for a short time
        socket.setTimeout(function () {
          socket.close();
        }, 5000);
      });
      
      check(chatRes, {
        'AI chat WebSocket connected successfully': (r) => r && r.status === 101,
      }) || errorRate.add(1);
    } else {
      // Connect to the WebSocket for WhiteLabel transcript updates
      const whitelabelUrl = `ws://localhost:8080/white-label/${testItem.whitelabelId}/transcript/${testItem.transcriptId}`;
      
      const whitelabelRes = ws.connect(whitelabelUrl, {
        headers: {
          'Cookie': cookies,
        },
      }, function (socket) {
        socket.on('error', () => {
          errorRate.add(1);
        });
        
        // Keep the connection open for a short time
        socket.setTimeout(function () {
          socket.close();
        }, 5000);
      });
      
      check(whitelabelRes, {
        'WhiteLabel WebSocket connected successfully': (r) => r && r.status === 101,
      }) || errorRate.add(1);
    }
  }
  
  // Add a short sleep to simulate real user behavior
  // During spikes, use shorter sleeps to generate more load
  if (inSpike) {
    sleep(Math.random() * 0.5 + 0.1); // 0.1-0.6 seconds
  } else {
    sleep(Math.random() * 2 + 1); // 1-3 seconds
  }
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Spike test completed with the following results:');
  console.log(`- Total HTTP requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- HTTP request failure rate: ${(data.metrics.http_req_failed.values.rate * 100).toFixed(2)}%`);
  console.log(`- Average HTTP response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile HTTP response time: ${data.metrics.http_req_duration.values.p(95).toFixed(2)}ms`);
  console.log(`- Total WebSocket connections: ${data.metrics.ws_sessions ? data.metrics.ws_sessions.values.count : 0}`);
  console.log(`- Average recovery time: ${data.metrics.recovery_time ? data.metrics.recovery_time.values.avg.toFixed(2) : 'N/A'}ms`);
  console.log(`- 95th percentile recovery time: ${data.metrics.recovery_time && data.metrics.recovery_time.values.count > 0 ? data.metrics.recovery_time.values.p(95).toFixed(2) : 'N/A'}ms`);
  console.log(`- Overall error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  return {
    'stdout': JSON.stringify(data),
    'spike-test-results.json': JSON.stringify(data),
  };
}
