import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('error_rate');

// Test configuration
export const options = {
  stages: [
    { duration: '10s', target: 10 },   // Ramp up to 10 users over 10 seconds
    { duration: '20s', target: 10 },   // Stay at 10 users for 20 seconds
    { duration: '10s', target: 0 },    // Ramp down to 0 users over 10 seconds
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% of requests should be below 200ms
    error_rate: ['rate<0.01'],        // Error rate should be below 1%
  },
};

// Main test function
export default function () {
  // Test the root endpoint (health check)
  const rootResponse = http.get('http://localhost:9081/');
  
  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
    'root response has correct structure': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.hello === 'world' && body.whoami === 'an api live server!';
      } catch (e) {
        console.error('Failed to parse root response:', e);
        return false;
      }
    },
  }) || errorRate.add(1);

  // Add a random sleep between 1-3 seconds to simulate real user behavior
  sleep(Math.random() * 2 + 1);
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Test completed with the following results:');
  console.log(`- Total requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- Failed requests: ${data.metrics.http_req_failed.values.passes}`);
  console.log(`- Average response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile response time: ${data.metrics.http_req_duration.values.p(95).toFixed(2)}ms`);
  console.log(`- Error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  return {
    'stdout': JSON.stringify(data),
  };
}
