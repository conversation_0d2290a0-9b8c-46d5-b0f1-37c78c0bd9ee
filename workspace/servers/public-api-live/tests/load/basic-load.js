import http from 'k6/http';
import { check, sleep } from 'k6';
import { Rate, Trend } from 'k6/metrics';

// Custom metrics
const errorRate = new Rate('error_rate');
const cookieCreationTime = new Trend('cookie_creation_time');

// Test configuration
export const options = {
  stages: [
    { duration: '30s', target: 50 },   // Ramp up to 50 users over 30 seconds
    { duration: '1m', target: 50 },    // Stay at 50 users for 1 minute
    { duration: '30s', target: 100 },  // Ramp up to 100 users over 30 seconds
    { duration: '1m', target: 100 },   // Stay at 100 users for 1 minute
    { duration: '30s', target: 0 },    // Ramp down to 0 users over 30 seconds
  ],
  thresholds: {
    http_req_duration: ['p(95)<200'], // 95% of requests should be below 200ms
    error_rate: ['rate<0.01'],        // Error rate should be below 1%
    cookie_creation_time: ['p(95)<300'], // 95% of cookie creations should be below 300ms
  },
};

// Simulated Auth0 token (for testing purposes only)
const mockAuthToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

// Main test function
export default function () {
  // Test the root endpoint (health check)
  const rootResponse = http.get('http://localhost:8080/');
  
  check(rootResponse, {
    'root status is 200': (r) => r.status === 200,
    'root response has correct structure': (r) => {
      const body = JSON.parse(r.body);
      return body.hello === 'world' && body.whoami === 'an api live server!';
    },
  }) || errorRate.add(1);

  // Test cookie creation endpoint
  const cookieStartTime = new Date();
  const cookieResponse = http.get('http://localhost:8080/cookie/new', {
    headers: {
      'Authorization': `Bearer ${mockAuthToken}`,
    },
  });
  cookieCreationTime.add((new Date() - cookieStartTime));
  
  check(cookieResponse, {
    'cookie creation status is 200': (r) => r.status === 200,
    'cookie creation response is correct': (r) => {
      const body = JSON.parse(r.body);
      return body.status === 'ok';
    },
    'cookie is set in response': (r) => r.headers['Set-Cookie'] && r.headers['Set-Cookie'].includes('userId='),
  }) || errorRate.add(1);

  // Test cookie refresh endpoint
  const refreshResponse = http.get('http://localhost:8080/cookie/refresh', {
    headers: {
      'Cookie': cookieResponse.headers['Set-Cookie'],
    },
  });
  
  check(refreshResponse, {
    'cookie refresh status is 200': (r) => r.status === 200,
    'cookie refresh response is correct': (r) => {
      const body = JSON.parse(r.body);
      return body.status === 'ok';
    },
  }) || errorRate.add(1);

  // Add a random sleep between 1-5 seconds to simulate real user behavior
  sleep(Math.random() * 4 + 1);
}

// Function to run after the test completes
export function handleSummary(data) {
  console.log('Test completed with the following results:');
  console.log(`- Total requests: ${data.metrics.http_reqs.values.count}`);
  console.log(`- Failed requests: ${data.metrics.http_req_failed.values.passes}`);
  console.log(`- Average response time: ${data.metrics.http_req_duration.values.avg.toFixed(2)}ms`);
  console.log(`- 95th percentile response time: ${data.metrics.http_req_duration.values.p(95).toFixed(2)}ms`);
  console.log(`- Error rate: ${(data.metrics.error_rate.values.rate * 100).toFixed(2)}%`);
  
  return {
    'stdout': JSON.stringify(data),
  };
}
