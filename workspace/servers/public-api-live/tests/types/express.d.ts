import { IncomingMessage } from 'http';
import { Request, Response } from 'express';
import { AuthResult } from 'express-oauth2-jwt-bearer';

// Extend Express Request for testing
declare global {
  namespace Express {
    interface Request extends IncomingMessage {
      auth?: AuthResult;
      [key: string]: any;
    }
  }
}

// Mock request type for testing
export interface MockRequest extends Partial<Request> {
  headers?: {
    cookie?: string;
    [key: string]: any;
  };
  auth?: AuthResult;
}

// Mock response type for testing
export interface MockResponse extends Partial<Response> {
  statusCode?: number;
  json?: jest.Mock;
  cookie?: jest.Mock;
  setHeader?: jest.Mock;
  send?: jest.Mock;
  status?: jest.Mock;
}
