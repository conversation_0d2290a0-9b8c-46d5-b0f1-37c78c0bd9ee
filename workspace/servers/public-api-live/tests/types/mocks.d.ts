import { Request, Response, NextFunction } from 'express';
import { Mock } from 'vitest';

// Extend the global namespace to include vitest types
declare global {
  namespace Vi {
    interface Mocked<T> {
      mockReturnValue: (val: any) => Mocked<T>;
      mockImplementation: (fn: (...args: any[]) => any) => Mocked<T>;
      mockResolvedValue: (val: any) => Mocked<T>;
      mockRejectedValue: (val: any) => Mocked<T>;
      mock: {
        calls: any[][];
        results: { type: string; value: any }[];
      };
    }
  }
}

// Mock request type for testing
export interface MockRequest extends Partial<Request> {
  headers?: {
    cookie?: string;
    [key: string]: any;
  };
  auth?: any;
  [key: string]: any;
}

// Mock response type for testing
export interface MockResponse extends Partial<Response> {
  statusCode?: number;
  json?: Mock;
  cookie?: Mock;
  setHeader?: Mock;
  send?: Mock;
  status?: Mock;
  [key: string]: any;
}

// Mock next function type
export type MockNextFunction = Mock<any[], any>;

// Declare module augmentations for mocked modules
declare module '@divinci-ai/server-globals' {
  export const getUserId: Vi.Mocked<(req: Request) => string>;
  export const getMongoose: Vi.Mocked<() => any>;
  export const getRedis: Vi.Mocked<() => any>;
  export const getChatRedisClient: Vi.Mocked<() => any>;
  export const ChannelSubscriber: Vi.Mocked<new () => any>;
  export const createCorsOptions: Vi.Mocked<() => any>;
}

declare module '@divinci-ai/server-utils' {
  export const HTTP_ERRORS: {
    NOT_FOUND: { statusCode: number; message: string };
    FORBIDDEN: { statusCode: number; message: string };
    SERVER_ERROR: { statusCode: number; message: string };
    [key: string]: { statusCode: number; message: string };
  };
  export const HTTP_ERRORS_WITH_CONTEXT: {
    NOT_FOUND: (msg?: string) => { statusCode: number; message: string };
    FORBIDDEN: (msg?: string) => { statusCode: number; message: string };
    SERVER_ERROR: (msg?: string) => { statusCode: number; message: string };
    [key: string]: (msg?: string) => { statusCode: number; message: string };
  };
  export const WebSocketRouter: Vi.Mocked<new () => any>;
  export const httpUpgradeToWSRequest: Vi.Mocked<(...args: any[]) => any>;
  export const requireEnvVar: Vi.Mocked<(name: string) => string>;
}

declare module '@divinci-ai/utils' {
  export const delay: Vi.Mocked<(ms: number) => Promise<void>>;
  export const SimpleEmitter: Vi.Mocked<new () => any>;
}

declare module 'ws' {
  export const WebSocketServer: Vi.Mocked<new (options: any) => any>;
}

declare module 'cookie' {
  export const parse: Vi.Mocked<(cookieString: string) => Record<string, string>>;
}

declare module 'jsonwebtoken' {
  export const sign: Vi.Mocked<(...args: any[]) => string>;
  export const verify: Vi.Mocked<(...args: any[]) => any>;
}

declare module 'express' {
  interface Request {
    auth?: any;
    [key: string]: any;
  }
}
