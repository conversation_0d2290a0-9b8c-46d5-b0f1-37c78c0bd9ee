// Mock types for Express Request, Response, and NextFunction
export interface MockRequest {
  headers?: Record<string, any>;
  cookies?: Record<string, any>;
  query?: Record<string, any>;
  params?: Record<string, any>;
  body?: any;
  [key: string]: any;
}

export interface MockResponse {
  status?: (code: number) => MockResponse;
  send?: (body: any) => MockResponse;
  json?: (body: any) => MockResponse;
  cookie?: (name: string, value: string, options?: any) => MockResponse;
  clearCookie?: (name: string, options?: any) => MockResponse;
  setHeader?: (name: string, value: string) => MockResponse;
  getHeader?: (name: string) => string | undefined;
  locals?: Record<string, any>;
  [key: string]: any;
}

export type MockNextFunction = (error?: any) => void;
