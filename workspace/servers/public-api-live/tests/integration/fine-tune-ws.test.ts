import { describe, it, expect, vi, beforeEach } from 'vitest';
import { watchFineTuneJobUpdate } from '../../src/ux/whitelabel/ws-router/fine-tune';
import { WhiteLabelModel, FineTuneAIModel } from '@divinci-ai/server-models';
import { HTTP_ERRORS } from '@divinci-ai/server-utils';
import { getUserCookieFromRequest } from '../../src/util/user-cookie';
import { addPollJob, JobTracker } from '../../src/util/poll-job';
import { condenseTarget, WHITE_LABEL_LOCATION } from '@divinci-ai/models';

// Mock dependencies
vi.mock('@divinci-ai/server-models', () => ({
  WhiteLabelModel: {
    findById: vi.fn(),
  },
  FineTuneAIModel: {
    findById: vi.fn(),
  },
}));

vi.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS: {
    NOT_FOUND: { statusCode: 404, message: 'Not Found' },
  },
  refreshDoc: vi.fn(),
}));

vi.mock('../../src/util/user-cookie', () => ({
  getUserCookieFromRequest: vi.fn(),
}));

vi.mock('../../src/util/poll-job', () => ({
  addPollJob: vi.fn(),
  JobTracker: vi.fn().mockImplementation(() => ({
    finishEvent: { on: vi.fn() },
    cancelEvent: { on: vi.fn() },
    errorEvent: { on: vi.fn() },
    start: vi.fn(),
    restart: vi.fn(),
    stop: vi.fn(),
  })),
}));

vi.mock('@divinci-ai/models', () => ({
  condenseTarget: vi.fn(),
  WHITE_LABEL_LOCATION: { model: 'whitelabel', database: 'default' },
}));

describe('Fine Tune WebSocket Handlers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('watchFineTuneJobUpdate', () => {
    it.skip('should set up a WebSocket connection for a valid fine tune job', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      const mockTarget = 'default-whitelabel-test-whitelabel-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockFineTune = {
        _id: mockFinetuneId,
        target: mockTarget,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
        close: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(mockFineTune);
      (condenseTarget as jest.Mock).mockReturnValue(mockTarget);
      (addPollJob as jest.Mock).mockReturnValue({
        promise: Promise.resolve(),
        cancel: vi.fn(),
      });
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(FineTuneAIModel.findById).toHaveBeenCalledWith(mockFinetuneId);
      expect(condenseTarget).toHaveBeenCalledWith({
        ...WHITE_LABEL_LOCATION,
        id: mockWhitelabelId,
      });
      expect(mockRequest.accept).toHaveBeenCalled();
      
      // Check that the JobTracker was set up correctly
      expect(JobTracker).toHaveBeenCalled();
      const jobTracker = (JobTracker as jest.Mock).mock.instances[0];
      expect(jobTracker.start).toHaveBeenCalled();
      
      // Check that the WebSocket event listeners were set up
      expect(mockWebSocket.addListener).toHaveBeenCalledWith('message', expect.any(Function));
      expect(mockWebSocket.addListener).toHaveBeenCalledWith('close', expect.any(Function));
    });

    it.skip('should handle whitelabel not found error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'non-existent-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(null);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
    });

    it.skip('should handle fine tune not found error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'non-existent-finetune-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(null);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(FineTuneAIModel.findById).toHaveBeenCalledWith(mockFinetuneId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
    });

    it.skip('should handle fine tune target mismatch error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      const mockTarget = 'default-whitelabel-test-whitelabel-id';
      const mockDifferentTarget = 'default-whitelabel-different-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockFineTune = {
        _id: mockFinetuneId,
        target: mockDifferentTarget, // Different from the expected target
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(mockFineTune);
      (condenseTarget as jest.Mock).mockReturnValue(mockTarget);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(FineTuneAIModel.findById).toHaveBeenCalledWith(mockFinetuneId);
      expect(condenseTarget).toHaveBeenCalledWith({
        ...WHITE_LABEL_LOCATION,
        id: mockWhitelabelId,
      });
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
    });

    it.skip('should handle errors during setup', async () => {
      // Arrange
      const mockError = new Error('Database error');
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockRejectedValue(mockError);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it.skip('should handle job completion correctly', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      const mockTarget = 'default-whitelabel-test-whitelabel-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockFineTune = {
        _id: mockFinetuneId,
        target: mockTarget,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
        close: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(mockFineTune);
      (condenseTarget as jest.Mock).mockReturnValue(mockTarget);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Get the JobTracker instance
      const jobTracker = (JobTracker as jest.Mock).mock.instances[0];
      
      // Get the finish event handler
      const finishHandler = jobTracker.finishEvent.on.mock.calls[0][0];
      
      // Simulate job completion
      finishHandler();
      
      // Assert
      expect(mockWebSocket.send).toHaveBeenCalledWith('job finished');
    });

    it.skip('should handle WebSocket message events correctly', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      const mockTarget = 'default-whitelabel-test-whitelabel-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockFineTune = {
        _id: mockFinetuneId,
        target: mockTarget,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
        close: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(mockFineTune);
      (condenseTarget as jest.Mock).mockReturnValue(mockTarget);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Get the message handler function
      const messageHandler = mockWebSocket.addListener.mock.calls.find(
        call => call[0] === 'message'
      )[1];
      
      // Get the JobTracker instance
      const jobTracker = (JobTracker as jest.Mock).mock.instances[0];
      
      // Simulate a message event
      messageHandler();
      
      // Assert
      expect(jobTracker.restart).toHaveBeenCalled();
    });

    it.skip('should clean up job on WebSocket close', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockFinetuneId = 'test-finetune-id';
      const mockTarget = 'default-whitelabel-test-whitelabel-id';
      
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
      };
      
      const mockFineTune = {
        _id: mockFinetuneId,
        target: mockTarget,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
        close: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        finetuneId: mockFinetuneId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (FineTuneAIModel.findById as jest.Mock).mockResolvedValue(mockFineTune);
      (condenseTarget as jest.Mock).mockReturnValue(mockTarget);
      
      // Act
      await watchFineTuneJobUpdate(mockRequest as any, mockParams, mockNext);
      
      // Get the close handler function
      const closeHandler = mockWebSocket.addListener.mock.calls.find(
        call => call[0] === 'close'
      )[1];
      
      // Get the JobTracker instance
      const jobTracker = (JobTracker as jest.Mock).mock.instances[0];
      
      // Simulate WebSocket close
      closeHandler();
      
      // Assert
      expect(jobTracker.stop).toHaveBeenCalled();
    });
  });
});
