import { describe, it, expect, vi, beforeEach } from 'vitest';
import { watchTranscriptUpdate } from '../../src/ux/whitelabel/ws-router/whitelabel';
import { WhiteLabelModel, TranscriptModel } from '@divinci-ai/server-models';
import { ChannelSubscriber, getChatRedisClient } from '@divinci-ai/server-globals';
import { HTTP_ERRORS_WITH_CONTEXT } from '@divinci-ai/server-utils';
import { getUserCookieFromRequest } from '../../src/util/user-cookie';

// Mock dependencies
vi.mock('@divinci-ai/server-models', () => ({
  WhiteLabelModel: {
    findById: vi.fn(),
  },
  TranscriptModel: {
    findById: vi.fn(),
  },
}));

vi.mock('@divinci-ai/server-globals', () => ({
  getChatRedisClient: vi.fn().mockReturnValue({
    chatRedisClient: {},
  }),
  ChannelSubscriber: vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    off: vi.fn(),
  })),
}));

vi.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS_WITH_CONTEXT: {
    NOT_FOUND: vi.fn().mockImplementation((msg) => ({ 
      statusCode: 404, 
      message: msg || 'Not Found' 
    })),
  },
}));

vi.mock('../../src/util/user-cookie', () => ({
  getUserCookieFromRequest: vi.fn(),
}));

describe('WhiteLabel WebSocket Handlers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('watchTranscriptUpdate', () => {
    it.skip('should set up a WebSocket connection for a valid whitelabel transcript', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
        transcriptIds: [mockTranscriptId],
      };
      const mockTranscript = {
        _id: mockTranscriptId,
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue({
          send: vi.fn(),
          addListener: vi.fn(),
        }),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(mockTranscript);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(TranscriptModel.findById).toHaveBeenCalledWith(mockTranscriptId);
      expect(mockRequest.accept).toHaveBeenCalled();
      
      // Check that the WebSocket was set up correctly
      const websocket = await mockRequest.accept();
      expect(ChannelSubscriber).toHaveBeenCalled();
      
      const subscriber = (ChannelSubscriber as jest.Mock).mock.instances[0];
      expect(subscriber.on).toHaveBeenCalledWith(
        `/transcript/${mockTranscriptId}`,
        expect.any(Function)
      );
      
      expect(websocket.addListener).toHaveBeenCalledWith(
        'close',
        expect.any(Function)
      );
    });

    it.skip('should handle non-owner users correctly', async () => {
      // Arrange
      const mockUserId = 'non-owner-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: 'owner-user-id', // Different from mockUserId
        transcriptIds: [mockTranscriptId],
      };
      const mockTranscript = {
        _id: mockTranscriptId,
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue({
          send: vi.fn(),
          addListener: vi.fn(),
        }),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(mockTranscript);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(TranscriptModel.findById).toHaveBeenCalledWith(mockTranscriptId);
      expect(mockRequest.accept).toHaveBeenCalled();
      
      // The connection should still be established even for non-owners
      // (permission checking is handled by middleware)
      const websocket = await mockRequest.accept();
      expect(ChannelSubscriber).toHaveBeenCalled();
    });

    it.skip('should handle anonymous users correctly', async () => {
      // Arrange
      const mockUserId = null; // Anonymous user
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: 'owner-user-id',
        transcriptIds: [mockTranscriptId],
      };
      const mockTranscript = {
        _id: mockTranscriptId,
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue({
          send: vi.fn(),
          addListener: vi.fn(),
        }),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(mockTranscript);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(TranscriptModel.findById).toHaveBeenCalledWith(mockTranscriptId);
      expect(mockRequest.accept).toHaveBeenCalled();
      
      // The connection should still be established even for anonymous users
      // (permission checking is handled by middleware)
      const websocket = await mockRequest.accept();
      expect(ChannelSubscriber).toHaveBeenCalled();
    });

    it.skip('should handle whitelabel not found error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'non-existent-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(null);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND).toHaveBeenCalledWith('No whitelabel found');
      expect(mockNext).toHaveBeenCalled();
    });

    it.skip('should handle transcript not in whitelabel error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'non-whitelabel-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
        transcriptIds: ['other-transcript-id'], // Does not include mockTranscriptId
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND).toHaveBeenCalledWith('No transcript belonging to whitelabel found');
      expect(mockNext).toHaveBeenCalled();
    });

    it.skip('should handle transcript not found error', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
        transcriptIds: [mockTranscriptId],
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(null);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(TranscriptModel.findById).toHaveBeenCalledWith(mockTranscriptId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(HTTP_ERRORS_WITH_CONTEXT.NOT_FOUND).toHaveBeenCalledWith('No transcript found');
      expect(mockNext).toHaveBeenCalled();
    });

    it.skip('should handle errors during setup', async () => {
      // Arrange
      const mockError = new Error('Database error');
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn(),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockRejectedValue(mockError);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Assert
      expect(getUserCookieFromRequest).toHaveBeenCalledWith(mockRequest.httpRequest);
      expect(WhiteLabelModel.findById).toHaveBeenCalledWith(mockWhitelabelId);
      expect(mockRequest.accept).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalledWith(mockError);
    });

    it.skip('should handle WebSocket message events correctly', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
        transcriptIds: [mockTranscriptId],
      };
      const mockTranscript = {
        _id: mockTranscriptId,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(mockTranscript);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Get the listener function
      const subscriber = (ChannelSubscriber as jest.Mock).mock.instances[0];
      const onFunction = subscriber.on.mock.calls[0][1];
      
      // Simulate a message event
      onFunction('{"message":"test message"}');
      
      // Assert
      expect(mockWebSocket.send).toHaveBeenCalledWith('{"message":"test message"}');
    });

    it.skip('should clean up subscription on WebSocket close', async () => {
      // Arrange
      const mockUserId = 'test-user-id';
      const mockWhitelabelId = 'test-whitelabel-id';
      const mockTranscriptId = 'test-transcript-id';
      const mockWhitelabel = {
        _id: mockWhitelabelId,
        ownerUser: mockUserId,
        transcriptIds: [mockTranscriptId],
      };
      const mockTranscript = {
        _id: mockTranscriptId,
      };
      
      const mockWebSocket = {
        send: vi.fn(),
        addListener: vi.fn(),
      };
      
      const mockRequest = {
        httpRequest: { headers: {} },
        accept: vi.fn().mockResolvedValue(mockWebSocket),
      };
      
      const mockParams = {
        whitelabelId: mockWhitelabelId,
        transcriptId: mockTranscriptId,
      };
      
      const mockNext = vi.fn();
      
      // Set up mocks
      (getUserCookieFromRequest as jest.Mock).mockReturnValue(mockUserId);
      (WhiteLabelModel.findById as jest.Mock).mockResolvedValue(mockWhitelabel);
      (TranscriptModel.findById as jest.Mock).mockResolvedValue(mockTranscript);
      
      // Act
      await watchTranscriptUpdate(mockRequest as any, mockParams, mockNext);
      
      // Get the close handler function
      const closeHandler = mockWebSocket.addListener.mock.calls.find(
        call => call[0] === 'close'
      )[1];
      
      // Simulate WebSocket close
      closeHandler();
      
      // Assert
      const subscriber = (ChannelSubscriber as jest.Mock).mock.instances[0];
      expect(subscriber.off).toHaveBeenCalledWith(
        `/transcript/${mockTranscriptId}`,
        expect.any(Function)
      );
    });
  });
});
