{"name": "@divinci-ai/public-api-live", "version": "0.3.0", "private": true, "description": "Server for <PERSON><PERSON><PERSON>", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"start": "node dist/index.js", "build": "tsc", "build:ci": "tsc --project tsconfig.ci.json", "build:ignore-errors": "tsc --skipLib<PERSON>heck --noEmit || echo 'TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'", "prepare": "rimraf ./dist && tsc", "start:dev": "ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts", "bundle:build:clean": "rimraf ./dist && mkdir -p ./dist", "bundle:build:js": "node ./bundle/esbuild.config.js ./ ./dist/", "bundle:build": "npm run bundle:build:clean && npm run bundle:build:js", "bundle:start": "env $(node dist/env.bundle.js) node dist/app.bundle.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:integration": "vitest run tests/integration", "test:load": "cd tests/load && ./run-load-tests.sh", "test:load:basic": "cd tests/load && k6 run basic-load.js", "test:load:ws": "cd tests/load && k6 run websocket-load.js", "test:load:stress": "cd tests/load && k6 run stress-test.js", "test:load:spike": "cd tests/load && k6 run spike-test.js", "test:load:endurance": "cd tests/load && k6 run endurance-test.js", "test:memory": "NODE_OPTIONS=\"--max_old_space_size=8192\" vitest run", "test:gc": "NODE_OPTIONS=\"--expose-gc --max_old_space_size=8192\" vitest run", "test:batch": "node scripts/run-tests-in-batches.js"}, "author": "", "license": "JSON", "dependencies": {"@divinci-ai/models": "file:../../resources/models", "@divinci-ai/server-globals": "file:../../resources/server-globals", "@divinci-ai/server-models": "file:../../resources/server-models", "@divinci-ai/server-permissions": "file:../../resources/server-permissions", "@divinci-ai/server-tools": "file:../../resources/server-tools", "@divinci-ai/server-utils": "file:../../resources/server-utils", "@divinci-ai/tools": "file:../../resources/tools", "@divinci-ai/utils": "file:../../resources/utils", "@testing-library/jest-dom": "^6.4.5", "@types/jest": "^29.5.12", "@types/morgan": "^1.9.5", "@types/node": "^22.5.2", "cookie": "^1.0.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "morgan": "^1.10.0", "ws": "^8.18.0"}, "devDependencies": {"@types/cookie": "^1.0.0", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.14", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.5", "@types/node": "^22.5.2", "@types/ws": "^8.5.12", "dotenv": "^16.4.5", "esbuild": "^0.23.1", "glob": "^10.4.1", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vitest": "^3.1.1", "@vitest/coverage-v8": "^3.1.1"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}