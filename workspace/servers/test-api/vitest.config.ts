import { defineConfig } from 'vitest/config';

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    include: ['tests/**/*.{test,spec}.{js,jsx,ts,tsx}'],
    setupFiles: ['./jest.setup.js'],
    coverage: {
      provider: 'istanbul',
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/']
    },
    deps: {
      inline: [/react-markdown/, /vfile/, /unist-util-stringify-position/, /unified/, /bail/]
    }
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.json', '.node']
  }
});
