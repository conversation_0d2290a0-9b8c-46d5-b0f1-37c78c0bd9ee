{"name": "@divinci-ai/public-api-webhook", "version": "0.3.0", "private": true, "description": "Server for <PERSON><PERSON><PERSON>", "main": "dist/index.js", "typings": "src/index.ts", "scripts": {"start": "node dist/index.js", "build": "tsc", "build:ci": "tsc --project tsconfig.ci.json", "build:ignore-errors": "tsc --skipLib<PERSON>heck --noEmit || echo 'TypeScript errors ignored'", "build:ignore-errors:ci": "tsc --skipLib<PERSON>heck --noEmit --project tsconfig.ci.json || echo 'TypeScript errors ignored'", "prepare": "rimraf ./dist && tsc", "start:dev": "ts-node-dev --poll --transpile-only --ignore-watch node_modules --files src/index.ts", "bundle:build:clean": "rimraf ./dist && mkdir -p ./dist", "bundle:build:js": "node ./bundle/esbuild.config.js ./ ./dist/", "bundle:build": "npm run bundle:build:clean && npm run bundle:build:js", "bundle:start": "env $(node dist/env.bundle.js) node dist/app.bundle.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "author": "", "license": "JSON", "dependencies": {"@divinci-ai/models": "file:../../resources/models", "@divinci-ai/server-globals": "file:../../resources/server-globals", "@divinci-ai/server-models": "file:../../resources/server-models", "@divinci-ai/server-tools": "file:../../resources/server-tools", "@divinci-ai/server-utils": "file:../../resources/server-utils", "@divinci-ai/utils": "file:../../resources/utils", "cors": "^2.8.5", "express": "^4.19.2", "mime-types": "^2.1.35", "twilio": "^4.14.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.12", "@types/mime-types": "^2.1.4", "@types/node": "^22.5.2", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.3", "@vitest/coverage-istanbul": "^3.1.1", "@vitest/ui": "^3.1.1", "dotenv": "^16.4.5", "esbuild": "^0.23.1", "glob": "^10.4.1", "nock": "^13.5.4", "rimraf": "^6.0.1", "sinon": "^20.0.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "vitest": "^3.1.1"}, "engines": {"node": ">=20", "pnpm": ">=10"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}