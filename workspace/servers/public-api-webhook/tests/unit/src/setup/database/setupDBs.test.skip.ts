/**
 * This test is currently skipped due to TypeScript errors with the Redis mock.
 * There are issues with mocking the Redis client's connect method properly.
 *
 * To fix this test:
 * 1. Update the mocking approach for the Redis client
 * 2. Fix the TypeScript typing for the redisClient.connect mock
 * 3. Ensure the error handling tests work correctly
 */

import { setupDBs } from "../../../../../src/setup/database";
import { getMongoose, MONGO_CONNECTION_URL, getRedis, getChatRedisClient } from "@divinci-ai/server-globals";

jest.mock("@divinci-ai/server-globals");

describe("setupDBs", ()=>{
  beforeEach(()=>{
    console.error = jest.fn();
    console.log = jest.fn();
  });

  afterEach(()=>{
    jest.clearAllMocks();
  });

  test("should connect to MongoDB and Redis successfully", async ()=>{
    // Mock successful connections
    const mockMongoose = {};
    const mockConnect = jest.fn().mockResolvedValue(undefined);

    const mockRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined)
    };

    const mockChatRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined)
    };

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: mockMongoose,
      connect: mockConnect
    });

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: mockRedisClient
    });

    (getChatRedisClient as jest.Mock).mockReturnValue({
      chatRedisClient: mockChatRedisClient
    });

    const result = await setupDBs();

    // Check connections were attempted
    expect(mockConnect).toHaveBeenCalled();
    expect(mockRedisClient.connect).toHaveBeenCalled();
    expect(mockChatRedisClient.connect).toHaveBeenCalled();

    // Check result structure
    expect(result).toEqual({
      mongoose: mockMongoose,
      redisClient: mockRedisClient,
      chatRedisClient: mockChatRedisClient
    });

    // Check success messages were logged
    expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Successfully connected to MongoDB"));
    expect(console.log).toHaveBeenCalledWith(expect.stringContaining("Successfully connected to Redis"));
  });

  test("should throw error if MongoDB connection fails", async ()=>{
    const mockError = new Error("MongoDB connection failed");
    const mockConnect = jest.fn().mockRejectedValue(mockError);

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: {},
      connect: mockConnect
    });

    // Mock successful Redis connections
    const mockRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined)
    };

    const mockChatRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined)
    };

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: mockRedisClient
    });

    (getChatRedisClient as jest.Mock).mockReturnValue({
      chatRedisClient: mockChatRedisClient
    });

    await expect(setupDBs()).rejects.toThrow(mockError);
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining("Failed to connect to MongoDB"),
      expect.anything()
    );
  });

  test("should throw error if Redis connection fails", async ()=>{
    // Mock successful MongoDB connection
    const mockMongoose = {};
    const mockConnect = jest.fn().mockResolvedValue(undefined);

    (getMongoose as jest.Mock).mockReturnValue({
      mongoose: mockMongoose,
      connect: mockConnect
    });

    // Mock failed Redis connection
    const mockError = new Error("Redis connection failed");
    const mockRedisClient = {
      connect: jest.fn().mockRejectedValue(mockError)
    };

    const mockChatRedisClient = {
      connect: jest.fn().mockResolvedValue(undefined)
    };

    (getRedis as jest.Mock).mockReturnValue({
      redisClient: mockRedisClient
    });

    (getChatRedisClient as jest.Mock).mockReturnValue({
      chatRedisClient: mockChatRedisClient
    });

    await expect(setupDBs()).rejects.toThrow(mockError);
    expect(console.error).toHaveBeenCalledWith(
      expect.stringContaining("Failed to connect to Redis"),
      expect.anything()
    );
  });
});