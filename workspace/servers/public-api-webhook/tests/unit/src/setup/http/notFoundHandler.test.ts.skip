import { notFoundHandler } from '../../../../../src/setup/http/common-handlers';
import { Request, Response, NextFunction } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

describe('notFoundHandler', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {} as Partial<Request>;
    mockResponse = {} as Partial<Response>;
    mockNext = jest.fn();
  });

  test('should call next with HTTP_ERRORS.NOT_FOUND', () => {
    notFoundHandler(mockRequest as Request, mockResponse as Response, mockNext);
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.NOT_FOUND);
  });
});