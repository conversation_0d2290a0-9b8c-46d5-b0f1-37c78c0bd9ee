import { errorHand<PERSON> } from '../../../../../src/setup/http/common-handlers';
import { HTTP_ERRORS } from '@divinci-ai/server-utils';
import { Request, Response, NextFunction } from 'express';

describe('errorHandler snapshot tests', () => {
  // Mock request, response, and next function
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;
  let jsonSpy: jest.SpyInstance;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/test-url',
      method: 'GET'
    };

    mockResponse = {
      statusCode: 0,
      json: jest.fn()
    };

    nextFunction = jest.fn();
    
    // Spy on console.error to keep the test output clean
    jest.spyOn(console, 'error').mockImplementation(() => {});
    
    // Spy on json to capture the response
    jsonSpy = jest.spyOn(mockResponse, 'json');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should format string errors consistently', () => {
    const error = 'This is a string error';
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format Error objects consistently', () => {
    const error = new Error('This is an Error object');
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format HTTP_ERRORS consistently', () => {
    // Use real HTTP_ERRORS mock
    jest.unmock('@divinci-ai/server-utils');
    
    // Create a mock HTTP_ERROR
    const httpError = { 
      statusCode: 404, 
      message: 'Not Found', 
      context: { resource: 'test-resource' } 
    };
    
    errorHandler(httpError, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format null errors consistently', () => {
    const error = null;
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format array errors consistently', () => {
    const error = ['Error item 1', 'Error item 2'];
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format errors with stack traces consistently', () => {
    const error = new Error('Error with stack trace');
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });

  test('should format errors with additional properties consistently', () => {
    const error = {
      statusCode: 400,
      message: 'Bad Request',
      context: { field: 'username' },
      additionalInfo: 'This is extra information',
      code: 'VALIDATION_ERROR'
    };
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);
    
    // Create a snapshot of the response
    expect(jsonSpy.mock.calls[0][0]).toMatchSnapshot();
  });
});