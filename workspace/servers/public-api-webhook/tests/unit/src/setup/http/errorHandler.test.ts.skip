import { errorHandler } from '../../../../../src/setup/http/common-handlers';
import { Request, Response, NextFunction } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

describe('errorHandler', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction = jest.fn();
  let consoleErrorSpy: jest.SpyInstance;

  beforeEach(() => {
    mockRequest = {
      originalUrl: '/test-url',
      method: 'GET'
    } as Partial<Request>;
    
    mockResponse = {
      statusCode: 0,
      json: jest.fn().mockReturnThis()
    } as Partial<Response>;
    
    consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    consoleErrorSpy.mockRestore();
  });

  test('should handle string errors', () => {
    errorHandler('This is an error string', mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.statusCode);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'non-object error',
      context: {}
    });
  });

  test('should handle null errors', () => {
    errorHandler(null, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.statusCode);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'non-object error',
      context: {}
    });
  });

  test('should handle array errors', () => {
    errorHandler(['error1', 'error2'], mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.statusCode);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'non-object error',
      context: {}
    });
  });

  test('should handle object errors with status and message', () => {
    const error = { statusCode: 400, message: 'Bad Request' };
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(400);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Bad Request',
      context: {}
    });
  });

  test('should handle object errors with status 500', () => {
    const error = { status: 500, message: 'Internal server error' };
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(500);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Internal server error',
      context: {}
    });
    expect(consoleErrorSpy).toHaveBeenCalledWith(
      expect.stringContaining('Internal Error. If in production, notify the team:'),
      expect.anything()
    );
  });

  test('should use error.context when available', () => {
    const error = { 
      status: 400, 
      message: 'Bad Request', 
      context: { field: 'email', problem: 'invalid format' } 
    };
    
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Bad Request',
      context: { field: 'email', problem: 'invalid format' }
    });
  });

  test('should handle object errors without status and message', () => {
    const error = { someProperty: 'value' };
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.statusCode);
    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Unknown error',
      context: {}
    });
  });

  test('should log stack trace when available', () => {
    const error = new Error('Test error with stack');
    errorHandler(error, mockRequest as Request, mockResponse as Response, nextFunction);

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      expect.stringContaining('[Error Handler] Stack Trace:'),
      expect.anything()
    );
  });
});