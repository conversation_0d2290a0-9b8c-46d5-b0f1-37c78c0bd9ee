import express from 'express';
import request from 'supertest';
import { setupHttpApp } from '../../../../../src/setup/http';

// Mock the dependencies
jest.mock('cors', () => {
  return jest.fn(() => (_req: any, _res: any, next: any) => next());
});

jest.mock('@divinci-ai/server-globals', () => ({
  createCorsOptions: jest.fn().mockReturnValue({}),
  WEB_CLIENT_IS_SECURE: false,
  WEB_CLIENT_HOST: 'test.host',
}));

jest.mock('../../../../../src/utils/phone-number', () => ({
  handleSMSBody: jest.fn((_req, _res, next) => next()),
  retrieveSMS: jest.fn(),
}));

jest.mock('../../../../../src/ux/user/text-router', () => ({
  router: express.Router(),
}));

jest.mock('../../../../../src/ux/ai-chat/text-router', () => ({
  router: express.Router(),
}));

describe('HTTP Root Handler', () => {
  let app: express.Application;

  beforeAll(async () => {
    process.env.HELLO_WORLD = 'test environment';
    app = await setupHttpApp();
  });

  afterAll(() => {
    jest.resetAllMocks();
  });

  test('should respond to GET / with correct JSON', async () => {
    const response = await request(app).get('/');

    expect(response.status).toBe(200);
    expect(response.headers['content-type']).toMatch(/application\/json/);
    expect(response.body).toEqual({
      hello: 'world',
      whoami: 'an api webhook server!',
      server: 'test environment'
    });
  });

  test('should respond with 404 for undefined routes', async () => {
    const response = await request(app).get('/undefined-route');
    expect(response.status).toBe(404);
    expect(response.body.status).toBe('error');
  });
});