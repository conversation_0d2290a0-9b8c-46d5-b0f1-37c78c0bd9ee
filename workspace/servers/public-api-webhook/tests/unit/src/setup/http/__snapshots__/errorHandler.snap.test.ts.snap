// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`errorHandler snapshot tests should format Error objects consistently 1`] = `
{
  "context": {},
  "message": "This is an Error object",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format HTTP_ERRORS consistently 1`] = `
{
  "context": {
    "resource": "test-resource",
  },
  "message": "Not Found",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format array errors consistently 1`] = `
{
  "context": {},
  "message": "non-object error",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format errors with additional properties consistently 1`] = `
{
  "context": {
    "field": "username",
  },
  "message": "Bad Request",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format errors with stack traces consistently 1`] = `
{
  "context": {},
  "message": "Error with stack trace",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format null errors consistently 1`] = `
{
  "context": {},
  "message": "non-object error",
  "status": "error",
}
`;

exports[`errorHandler snapshot tests should format string errors consistently 1`] = `
{
  "context": {},
  "message": "non-object error",
  "status": "error",
}
`;
