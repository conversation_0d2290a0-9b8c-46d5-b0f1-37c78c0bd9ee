import {errorHandler} from '../../../../../src/setup/http';

import { Request, Response, NextFunction } from "express";
import {HTTP_ERRORS} from "@divinci-ai/server-utils";

describe('errorHandler', () => {
  let mockRequest;
  let mockResponse;
  let nextFunction = () => {};

  beforeEach(() => {
    mockRequest = {} as Request;
    mockResponse = {
      statusCode: 0,
      json: jest.fn()
    } as unknown as Response;
  });

  test('should handle non-object errors', () => {
    errorHandler('This is error string', mockRequest, mockResponse, nextFunction);

    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'non object error',
      context: {value: 'This is error string'},
    });
    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.status);
  });

  test('should handle object errors without status and message', () => {
    errorHandler({test: 'Sample error obj'}, mockRequest, mockResponse, nextFunction);

    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Unknown error',
      context: {test: 'Sample error obj'},
    });
    expect(mockResponse.statusCode).toBe(HTTP_ERRORS.SERVER_ERROR.status);
  });

  test('should handle object errors with status and message', () => {
    errorHandler({status: 400, message: 'Bad Request'}, mockRequest, mockResponse, nextFunction);

    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Bad Request',
      context: {},
    });
    expect(mockResponse.statusCode).toBe(400);
  });

  test('should handle object errors with status 500', () => {
    errorHandler({status: 500, message: 'Internal server error'}, mockRequest, mockResponse, nextFunction);

    expect(mockResponse.json).toHaveBeenCalledWith({
      status: 'error',
      message: 'Internal server error',
      context: {},
    });
    expect(mockResponse.statusCode).toBe(500);
  });
});
