import express from "express";
import cors from "cors";
import { setupHttpApp } from "../../../../../src/setup/http";
import { handleSMSBody } from "../../../../../src/utils/phone-number";
import { router as userTextRouter } from "../../../../../src/ux/user/text-router";
import { router as aiChatTextRouter } from "../../../../../src/ux/ai-chat/text-router";
import { createCorsOptions } from "@divinci-ai/server-globals";
import { notFoundHandler, errorHandler } from "../../../../../src/setup/http/common-handlers";

// Mock express and its methods
jest.mock("express", ()=>{
  const router = {
    use: jest.fn()
  };

  const app = {
    use: jest.fn(),
    get: jest.fn(),
    post: jest.fn()
  };

  const expressFunc = jest.fn().mockReturnValue(app);

  // Add Router property to express function
  expressFunc.Router = jest.fn().mockReturnValue(router);

  return expressFunc;
});

// Mock cors
jest.mock("cors", ()=>{
  return jest.fn().mockReturnValue("corsMiddleware");
});

// Mock the utils and handlers
jest.mock("../../../../../src/utils/phone-number", ()=>({
  handleSMSBody: "handleSMSBodyMiddleware"
}));

jest.mock("../../../../../src/ux/user/text-router", ()=>({
  router: "userTextRouter"
}));

jest.mock("../../../../../src/ux/ai-chat/text-router", ()=>({
  router: "aiChatTextRouter"
}));

jest.mock("@divinci-ai/server-globals", ()=>({
  createCorsOptions: jest.fn().mockReturnValue({ corsOptions: true })
}));

jest.mock("../../../../../src/setup/http/common-handlers", ()=>({
  notFoundHandler: "notFoundHandlerMiddleware",
  errorHandler: "errorHandlerMiddleware"
}));

describe("Setup HTTP App", ()=>{
  let app: any;
  let router: any;

  beforeEach(()=>{
    // Clear all mocks
    jest.clearAllMocks();

    // Get references to the mocked express app and router
    app = express();
    router = express.Router();
  });

  test("should configure the express app correctly", async ()=>{
    // Call the function under test
    await setupHttpApp();

    // Verify CORS middleware was set up
    expect(cors).toHaveBeenCalledWith({ corsOptions: true });
    expect(app.use).toHaveBeenCalledWith("corsMiddleware");

    // Verify root route was set up
    expect(app.get).toHaveBeenCalledWith("/", expect.any(Function));

    // Verify SMS route was set up
    expect(app.post).toHaveBeenCalledWith("/twilio-sms", "handleSMSBodyMiddleware", router);

    // Verify routers were added to SMS router
    expect(router.use).toHaveBeenCalledWith("userTextRouter");
    expect(router.use).toHaveBeenCalledWith("aiChatTextRouter");

    // Verify error handlers were set up
    expect(app.use).toHaveBeenCalledWith("notFoundHandlerMiddleware");
    expect(app.use).toHaveBeenCalledWith("errorHandlerMiddleware");
  });

  test("should return the express app", async ()=>{
    // Call the function under test
    const result = await setupHttpApp();

    // Verify the result is the express app
    expect(result).toBe(app);
  });
});