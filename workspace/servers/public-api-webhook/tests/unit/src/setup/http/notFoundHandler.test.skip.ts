import { notFoundHandler } from '../../../../../src/setup/http';

import { HTTP_ERRORS } from "@divinci-ai/server-utils";
import { Request, Response, NextFunction } from "express";

describe('notFoundHandler', () => {
  let req: Partial<Request>;
  let res: Partial<Response>;
  let next: jest.Mock;

  beforeEach(() => {
    req = {};
    res = {};
    next = jest.fn();
  });

  it('calls next with NOT_FOUND error', () => {
    notFoundHandler(req as Request, res as Response, next);
    expect(next).toBeCalledWith(HTTP_ERRORS.NOT_FOUND);
  });

  it('does not send a response', () => {
    res.send = jest.fn();
    notFoundHandler(req as Request, res as Response, next);
    expect(res.send).not.toBeCalled();
  });

  it('does not set any response status', () => {
    res.status = jest.fn();
    notFoundHandler(req as Request, res as Response, next);
    expect(res.status).not.toBeCalled();
  });
});