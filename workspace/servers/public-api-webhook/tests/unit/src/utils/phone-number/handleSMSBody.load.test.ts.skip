import { describe, it, expect, vi, beforeEach } from "vitest";
import { handleSMSBody } from "../../../../../src/utils/phone-number";
import { HTTP_ERRORS, formBody } from "@divinci-ai/server-utils";
import { validateRequest } from "twilio";
import { TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN } from "@divinci-ai/server-globals";

// Mock the dependencies
vi.mock("@divinci-ai/server-utils", ()=>({
  HTTP_ERRORS: {
    FORBIDDEN: new Error("Forbidden"),
    BAD_FORM: new Error("Bad Form")
  },
  formBody: vi.fn()
}));

vi.mock("twilio", ()=>({
  validateRequest: vi.fn()
}));

vi.mock("@divinci-ai/server-globals", ()=>({
  TWILIO_ACCOUNT_SID: "test-account-sid",
  TWILIO_AUTH_TOKEN: "test-auth-token"
}));

vi.mock("@divinci-ai/utils", ()=>({
  castShallowObject: vi.fn((body, config, error)=>{
    // Simple implementation that returns the body if it has expected fields
    if(body && typeof body === "object" && "AccountSid" in body) {
      return body;
    }
    throw error;
  })
}));

describe("handleSMSBody Load Tests", ()=>{
  let mockRequest: any;
  let mockResponse: any;
  let mockNext: any;

  beforeEach(()=>{
    vi.clearAllMocks();

    // Create mock request, response, and next function
    mockRequest = {
      get: vi.fn().mockReturnValue("test-host"),
      originalUrl: "/test-url",
      headers: {
        "x-twilio-signature": "valid-signature"
      }
    };

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };

    mockNext = vi.fn();

    // Default valid form body
    vi.mocked(formBody).mockResolvedValue({
      From: "+**********",
      To: "+**********",
      Body: "Test message",
      AccountSid: "test-account-sid",
      // ... other required fields
      ToCountry: "US",
      ToState: "CA",
      SmsMessageSid: "SMS123",
      NumMedia: "0",
      ToCity: "San Francisco",
      FromZip: "12345",
      SmsSid: "SM123",
      FromState: "CA",
      FromCity: "Los Angeles",
      FromCountry: "US",
      MessagingServiceSid: "MG123",
      ToZip: "54321",
      NumSegments: "1",
      MessageSid: "MS123",
      ApiVersion: "2010-04-01"
    });

    // Default validation result is valid
    vi.mocked(validateRequest).mockReturnValue(true);
  });

  it("should handle high volume of requests efficiently", async ()=>{
    // Create a large number of concurrent webhook requests
    const requestCount = 100;
    const requests = Array(requestCount).fill(null).map((_, i)=>{
      const req = { ...mockRequest };
      req.headers = { ...mockRequest.headers };
      req.headers["x-twilio-signature"] = `signature-${i}`;
      return req;
    });

    // Configure validation to always pass
    vi.mocked(validateRequest).mockReturnValue(true);

    // Start performance tracking
    const startTime = performance.now();

    // Process all requests concurrently
    await Promise.all(
      requests.map(req=>handleSMSBody(req, mockResponse, mockNext))
    );

    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    const avgDuration = totalDuration / requestCount;

    console.log(`Processed ${requestCount} webhook requests in ${totalDuration.toFixed(2)}ms`);
    console.log(`Average time per request: ${avgDuration.toFixed(2)}ms`);

    // Ensure all requests were validated
    expect(validateRequest).toHaveBeenCalledTimes(requestCount);

    // In a real test, we might:
    // 1. Set an upper bound on average processing time
    // 2. Compare to a baseline measurement
    // 3. Check for memory usage issues
  });

  it("should maintain consistent performance with different payload sizes", async ()=>{
    // Test with different SMS body sizes
    const testCases = [
      { name: "small", bodySize: 10 },
      { name: "medium", bodySize: 100 },
      { name: "large", bodySize: 1000 },
      // SMS messages typically have a limit around 1600 characters
      { name: "maximum", bodySize: 1600 }
    ];

    const results: Record<string, number> = {};

    for(const { name, bodySize } of testCases) {
      // Generate a message of the specified size
      const messageBody = "A".repeat(bodySize);

      // Update mock to return body with the specified size
      vi.mocked(formBody).mockResolvedValue({
        From: "+**********",
        To: "+**********",
        Body: messageBody,
        AccountSid: "test-account-sid",
        // ... other required fields
        ToCountry: "US",
        ToState: "CA",
        SmsMessageSid: "SMS123",
        NumMedia: "0",
        ToCity: "San Francisco",
        FromZip: "12345",
        SmsSid: "SM123",
        FromState: "CA",
        FromCity: "Los Angeles",
        FromCountry: "US",
        MessagingServiceSid: "MG123",
        ToZip: "54321",
        NumSegments: "1",
        MessageSid: "MS123",
        ApiVersion: "2010-04-01"
      });

      vi.clearAllMocks();

      // Measure performance
      const startTime = performance.now();
      await handleSMSBody(mockRequest, mockResponse, mockNext);
      const endTime = performance.now();

      const duration = endTime - startTime;
      results[name] = duration;

      console.log(`${name} payload (${bodySize} chars): ${duration.toFixed(2)}ms`);

      // Ensure the request was processed successfully
      expect(mockNext).toHaveBeenCalled();
    }

    // Log comparison of results
    console.log("Performance comparison:");
    for(const [name, duration] of Object.entries(results)) {
      const ratio = duration / results.small;
      console.log(`${name}: ${duration.toFixed(2)}ms (${ratio.toFixed(2)}x small payload)`);
    }

    // We don't assert on specific times since that would create brittle tests
    // Instead we're logging the results for analysis
  });

  it("should handle concurrent requests with validation failures gracefully", async ()=>{
    // Create a mix of valid and invalid requests
    const validCount = 50;
    const invalidCount = 50;
    const totalCount = validCount + invalidCount;

    const requests = Array(totalCount).fill(null).map((_, i)=>{
      const req = { ...mockRequest };
      req.headers = { ...mockRequest.headers };
      req.headers["x-twilio-signature"] = `signature-${i}`;
      return req;
    });

    // Configure validation to pass for valid requests and fail for invalid ones
    vi.mocked(validateRequest).mockImplementation((authToken, signature)=>{
      const index = parseInt(signature.split("-")[1]);
      return index < validCount; // First validCount requests are valid
    });

    // Start performance tracking
    const startTime = performance.now();

    // Process all requests concurrently
    await Promise.all(
      requests.map(req=>handleSMSBody(req, mockResponse, mockNext))
    );

    const endTime = performance.now();
    const totalDuration = endTime - startTime;

    console.log(`Processed ${totalCount} mixed requests in ${totalDuration.toFixed(2)}ms`);

    // Count successful and failed validations
    const nextCalls = mockNext.mock.calls;
    const successCount = nextCalls.filter((call: unknown[])=>call.length === 0).length;
    const failureCount = nextCalls.filter((call: unknown[])=>call[0] === HTTP_ERRORS.FORBIDDEN).length;

    console.log(`Successful validations: ${successCount}`);
    console.log(`Failed validations: ${failureCount}`);

    // Verify we had the expected number of successes and failures
    expect(successCount).toBe(validCount);
    expect(failureCount).toBe(invalidCount);
  });
});