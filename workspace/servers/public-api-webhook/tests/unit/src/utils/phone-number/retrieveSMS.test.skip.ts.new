import { retrieveSMS } from '../../../../../src/utils/phone-number';
import { HTTP_ERRORS } from '@divinci-ai/server-utils';

// Mocking HTTP_ERRORS
jest.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS: {
    BAD_FORM: new Error('Bad Form')
  }
}));

describe('retrieveSMS function', () => {
  const PHONE_NUMBER_KEY = Symbol('phone number key');
  
  test('should return SMS data when available in request', () => {
    // Create a mock request with SMS data
    const mockSMS = {
      From: '+**********',
      To: '+**********',
      Body: 'Test message',
      SmsStatus: 'received',
      ToCountry: 'US',
      ToState: 'CA',
      SmsMessageSid: 'SM123456',
      NumMedia: '0',
      ToCity: 'San Francisco',
      FromZip: '94107',
      SmsSid: 'SM123456',
      FromState: 'CA',
      FromCity: 'San Francisco',
      FromCountry: 'US',
      MessagingServiceSid: 'MG123456',
      ToZip: '94105',
      NumSegments: '1',
      MessageSid: 'SM123456',
      AccountSid: 'AC123456',
      ApiVersion: '2010-04-01'
    };
    
    // Mock request object with the SMS data using the symbol key
    const mockRequest = {
      [PHONE_NUMBER_KEY]: mockSMS
    } as any;
    
    // Call the function being tested
    try {
      const result = retrieveSMS(mockRequest);
      
      // Assert the result matches the mock SMS data
      expect(result).toEqual(mockSMS);
    } catch (error) {
      // This should not happen, but if it does, the test will fail
      fail('retrieveSMS should not throw an error when SMS data is available');
    }
  });
  
  test('should throw HTTP_ERRORS.BAD_FORM when SMS data is not in request', () => {
    // Create a mock request without SMS data
    const mockRequest = {} as any;
    
    // Expect the function to throw an error matching the mocked BAD_FORM error
    expect(() => retrieveSMS(mockRequest)).toThrow();
  });
});