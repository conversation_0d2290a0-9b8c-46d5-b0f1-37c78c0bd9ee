import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleSMSBody } from '../../../../../src/utils/phone-number';
import { HTTP_ERRORS, formBody } from '@divinci-ai/server-utils';
import { validateRequest } from 'twilio';
import { TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN } from '@divinci-ai/server-globals';

// Mock the dependencies
vi.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS: {
    FORBIDDEN: new Error('Forbidden'),
    BAD_FORM: new Error('Bad Form')
  },
  formBody: vi.fn()
}));

vi.mock('twilio', () => ({
  validateRequest: vi.fn()
}));

vi.mock('@divinci-ai/server-globals', () => ({
  TWILIO_ACCOUNT_SID: 'test-account-sid',
  TWILIO_AUTH_TOKEN: 'test-auth-token'
}));

vi.mock('@divinci-ai/utils', () => ({
  castShallowObject: vi.fn((body: any, config: any, error: any) => {
    if (body && typeof body === 'object' && 'AccountSid' in body) {
      return body;
    }
    throw error;
  })
}));

describe('handleSMSBody Security Tests', () => {
  let mockRequest: any;
  let mockResponse: any;
  let mockNext: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock request, response, and next function
    mockRequest = {
      get: vi.fn().mockReturnValue('test-host'),
      originalUrl: '/test-url',
      headers: {
        'x-twilio-signature': 'valid-signature'
      }
    };
    
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };
    
    mockNext = vi.fn();
    
    // Default valid form body
    vi.mocked(formBody).mockResolvedValue({
      From: '+**********',
      To: '+**********',
      Body: 'Test message',
      AccountSid: 'test-account-sid',
      // ... other required fields
    });
    
    // Default validation result is valid
    vi.mocked(validateRequest).mockReturnValue(true);
  });

  it('should reject requests without Twilio signature', async () => {
    // Remove the signature
    mockRequest.headers = {};
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call next with FORBIDDEN error
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
    // Verify that validateRequest was not called
    expect(validateRequest).not.toHaveBeenCalled();
  });

  it('should validate the Twilio signature against the auth token', async () => {
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call validateRequest with the correct parameters
    expect(validateRequest).toHaveBeenCalledWith(
      TWILIO_AUTH_TOKEN,
      'valid-signature',
      'https://test-host/test-url',
      expect.any(Object)
    );
  });

  it('should reject requests with invalid Twilio signatures', async () => {
    // Make validation return false
    vi.mocked(validateRequest).mockReturnValue(false);
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call next with FORBIDDEN error
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
    // Next middleware should not receive SMS data
    expect(mockRequest.PHONE_NUMBER_KEY).toBeUndefined();
  });

  it('should reject requests from unauthorized Twilio accounts', async () => {
    // Set account SID to an unauthorized value
    vi.mocked(formBody).mockResolvedValue({
      From: '+**********',
      To: '+**********',
      Body: 'Test message',
      AccountSid: 'unauthorized-account-sid',
      // ... other required fields
    });
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call next with FORBIDDEN error
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  it('should sanitize and validate request data', async () => {
    const mockCastShallowObject = vi.mocked(require('@divinci-ai/utils').castShallowObject);
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should use castShallowObject to validate the structure
    expect(mockCastShallowObject).toHaveBeenCalledWith(
      expect.any(Object),
      expect.any(Object),
      HTTP_ERRORS.BAD_FORM
    );
  });

  it('should reject malicious payloads with unexpected fields', async () => {
    // Return body with injection attempt
    vi.mocked(formBody).mockResolvedValue({
      From: '+**********',
      To: '+**********',
      Body: 'Test message',
      AccountSid: 'test-account-sid',
      '__proto__': { 'malicious': true },
      'constructor': { 'prototype': { 'malicious': true } }
    });
    
    // Mock castShallowObject to throw when seeing unexpected fields
    vi.mocked(require('@divinci-ai/utils').castShallowObject).mockImplementation((body: any) => {
      if ('__proto__' in body || 'constructor' in body) {
        throw HTTP_ERRORS.BAD_FORM;
      }
      return body;
    });
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call next with BAD_FORM error
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.BAD_FORM);
  });

  it('should prevent URL manipulation attacks', async () => {
    // Modify the request URL
    mockRequest.get.mockReturnValue('malicious-host');
    mockRequest.originalUrl = '/malicious-url';
    
    // But keep the signature valid for the original URL
    vi.mocked(validateRequest).mockImplementation((authToken: string, signature: string, url: string) => {
      return url === 'https://test-host/test-url';
    });
    
    await handleSMSBody(mockRequest, mockResponse, mockNext);
    
    // Should call next with FORBIDDEN error because signature won't match
    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });
});