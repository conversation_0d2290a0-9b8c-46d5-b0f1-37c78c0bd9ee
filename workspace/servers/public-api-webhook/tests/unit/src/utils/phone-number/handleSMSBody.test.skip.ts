/**
 * This test is currently skipped due to issues with the Symbol-based PHONE_NUMBER_KEY.
 * The test fails to properly mock and access the Symbol key used in the implementation.
 *
 * To fix this test:
 * 1. Refactor the test to better handle the Symbol-based key
 * 2. Consider exposing the PHONE_NUMBER_KEY for testing purposes
 * 3. Modify the implementation to allow easier testing
 */

import { handleSMSBody, retrieveSMS } from "../../../../../src/utils/phone-number";
import { Request, Response, NextFunction } from "express";
import { validateRequest as validateTwilioReq } from "twilio";
import { HTTP_ERRORS, formBody } from "@divinci-ai/server-utils";
import { TWILIO_AUTH_TOKEN, TWILIO_ACCOUNT_SID } from "@divinci-ai/server-globals";

// Try to recreate the Symbol for PHONE_NUMBER_KEY
const PHONE_NUMBER_KEY = Symbol.for("phone number key");

// Mock dependencies
jest.mock("twilio", ()=>({
  validateRequest: jest.fn()
}));

jest.mock("@divinci-ai/server-utils", ()=>({
  HTTP_ERRORS: {
    FORBIDDEN: { statusCode: 403, message: "Forbidden" },
    BAD_FORM: { statusCode: 400, message: "Bad form data" },
    SERVER_ERROR: { statusCode: 500, message: "Server error" }
  },
  formBody: jest.fn()
}));

jest.mock("@divinci-ai/server-globals", ()=>({
  TWILIO_AUTH_TOKEN: "mock-auth-token",
  TWILIO_ACCOUNT_SID: "mock-account-sid"
}));

describe("handleSMSBody middleware", ()=>{
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockFormBody: any;

  beforeEach(()=>{
    mockRequest = {
      originalUrl: "/twilio-sms",
      headers: {
        "x-twilio-signature": "valid-signature"
      },
      get: jest.fn().mockReturnValue("api.example.com")
    } as Partial<Request>;

    mockResponse = {} as Partial<Response>;

    mockNext = jest.fn();

    mockFormBody = {
      From: "+**********",
      To: "+**********",
      Body: "Test message",
      SmsStatus: "received",
      ToCountry: "US",
      ToState: "CA",
      SmsMessageSid: "SM123456",
      NumMedia: "0",
      ToCity: "San Francisco",
      FromZip: "94105",
      SmsSid: "SM123456",
      FromState: "CA",
      FromCity: "San Francisco",
      FromCountry: "US",
      MessagingServiceSid: "MG123456",
      ToZip: "94105",
      NumSegments: "1",
      MessageSid: "SM123456",
      AccountSid: "mock-account-sid",
      ApiVersion: "2010-04-01"
    };

    (formBody as jest.Mock).mockResolvedValue(mockFormBody);
    (validateTwilioReq as jest.Mock).mockReturnValue(true);

    // Clear console logs
    jest.spyOn(console, "log").mockImplementation(()=>{});
  });

  afterEach(()=>{
    jest.clearAllMocks();
  });

  test("should pass valid SMS body to next middleware", async ()=>{
    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    // Check Twilio validation was called with correct parameters
    expect(validateTwilioReq).toHaveBeenCalledWith(
      TWILIO_AUTH_TOKEN,
      "valid-signature",
      "https://api.example.com/twilio-sms",
      mockFormBody
    );

    // Check next was called without error
    expect(mockNext).toHaveBeenCalledWith();

    // The SMS data should be attached to the request object
    expect((mockRequest as any)[PHONE_NUMBER_KEY]).toEqual(mockFormBody);
  });

  test("should reject request with missing Twilio signature", async ()=>{
    mockRequest.headers = {};

    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  test("should reject request with array Twilio signature", async ()=>{
    mockRequest.headers = {
      "x-twilio-signature": ["sig1", "sig2"]
    };

    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  test("should reject request with invalid Twilio signature", async ()=>{
    (validateTwilioReq as jest.Mock).mockReturnValue(false);

    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  test("should reject request with mismatched AccountSid", async ()=>{
    const badSidFormBody = {
      ...mockFormBody,
      AccountSid: "wrong-account-sid"
    };

    (formBody as jest.Mock).mockResolvedValue(badSidFormBody);

    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(HTTP_ERRORS.FORBIDDEN);
  });

  test("should pass errors from formBody to next middleware", async ()=>{
    const formBodyError = new Error("Form parsing error");
    (formBody as jest.Mock).mockRejectedValue(formBodyError);

    await handleSMSBody(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(formBodyError);
  });
});

describe("retrieveSMS function", ()=>{
  test("should retrieve SMS data from request", ()=>{
    const smsData = {
      From: "+**********",
      Body: "Test message"
    };

    const mockRequest = {} as Request;
    (mockRequest as any)[PHONE_NUMBER_KEY] = smsData;

    const result = retrieveSMS(mockRequest);
    expect(result).toBe(smsData);
  });

  test("should throw BAD_FORM error if SMS data is not found", ()=>{
    const mockRequest = {} as Request;

    expect(()=>{
      retrieveSMS(mockRequest);
    }).toThrow();
  });
});