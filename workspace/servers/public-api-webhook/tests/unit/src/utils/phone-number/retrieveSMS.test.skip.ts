import { retrieveSMS } from '../../../../../src/utils/phone-number';

import { Request } from "express";
import { HTTP_ERRORS } from "@divinci-ai/server-utils";

describe('retrieveSMS tests', () => {

  test('retrieveSMS returns the SMS', () => {
    const mockSMS = {
      From: 'from',
      To: 'to',
      Body: 'message',
      SmsStatus: 'status',
      ToCountry: 'country',
      ToState: 'state',
      SmsMessageSid: 'sid',
      NumMedia: '0',
      ToCity: 'city',
      FromZip: 'zip',
      SmsSid: 'sid',
      FromState: 'state',
      FromCity: 'city',
      FromCountry: 'country',
      MessagingServiceSid: 'sid',
      ToZip: 'zip',
      NumSegments: '1',
      MessageSid: 'sid',
      AccountSid: 'sid',
      ApiVersion: 'version'
    };

    const req = {
      [Symbol.for('phone number key')]: mockSMS
    } as unknown as Request;

    expect(retrieveSMS(req)).toMatchObject(mockSMS);
  });

  test('retrieveSMS throws error when SMS is undefined', () => {
    const req = {
      [Symbol.for('phone number key')]: undefined
    } as unknown as Request;

    expect(() => retrieveSMS(req)).toThrow(HTTP_ERRORS.BAD_FORM);
  });
});