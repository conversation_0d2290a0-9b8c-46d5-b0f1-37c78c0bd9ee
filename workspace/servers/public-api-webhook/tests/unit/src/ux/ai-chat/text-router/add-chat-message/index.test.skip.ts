/**
 * This test is currently skipped due to issues with mocking the complex chain of dependencies.
 * There are problems with dependencies for server-models and handling the message processing logic.
 * 
 * To fix this test:
 * 1. Update the mocks to better handle all the model dependencies
 * 2. Fix the issues with retrieving mockPhoneNumber.findOne from the mocks
 * 3. Consider restructuring the test to use more isolated units
 */

import { addChatMessage } from '../../../../../../../src/ux/ai-chat/text-router/add-chat-message';
import { Request, Response, NextFunction } from 'express';
import { retrieveSMS } from '../../../../../../../src/utils/phone-number';
import { HTTP_ERRORS } from '@divinci-ai/server-utils';
import { CHAT_AI_ASSISTANT } from '@divinci-ai/server-tools';
import { WEB_CLIENT_IS_SECURE, WEB_CLIENT_HOST } from '@divinci-ai/server-globals';

// Mock dependencies
jest.mock('../../../../../../../src/utils/phone-number', () => ({
  retrieveSMS: jest.fn()
}));

// Mock ChatModel and TranscriptModel
const mockChatModel = {
  findById: jest.fn()
};

const mockTranscriptModel = {
  findById: jest.fn(),
  categorizeText: jest.fn()
};

// Mock PhoneNumberModel
const mockPhoneNumberModel = {
  findOne: jest.fn()
};

jest.mock('@divinci-ai/server-models', () => {
  const CHAT_CATEGORY_ENUM = {
    TEXT: 'text',
    UNKNOWN: 'unknown',
    CODE: 'code',
    MATH: 'math'
  };
  
  return {
    ChatModel: mockChatModel,
    PhoneNumberModel: mockPhoneNumberModel,
    TranscriptModel: mockTranscriptModel,
    CHAT_CATEGORY_ENUM,
    CHAT_CATEGORY: CHAT_CATEGORY_ENUM
  };
});

// Mock HTTP_ERRORS
const mockHttpErrors = {
  FORBIDDEN: new Error('Forbidden'),
  NOT_FOUND: new Error('Not Found')
};

jest.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS: mockHttpErrors
}));

// Mock CHAT_AI_ASSISTANT
const mockGetAssistant = jest.fn();
jest.mock('@divinci-ai/server-tools', () => ({
  CHAT_AI_ASSISTANT: {
    getAssistant: mockGetAssistant
  }
}));

// Mock web client globals
jest.mock('@divinci-ai/server-globals', () => ({
  WEB_CLIENT_IS_SECURE: false,
  WEB_CLIENT_HOST: 'example.com'
}));

// Mock models/utils
jest.mock('@divinci-ai/models', () => ({
  unravelTarget: jest.fn().mockReturnValue({ id: 'chat123' }),
  TranscriptMessage: class {}
}));

describe('addChatMessage middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockChatInstance: any;
  let phoneNumberFindOneExecMock: jest.Mock;

  beforeEach(() => {
    mockRequest = {} as Partial<Request>;
    
    mockResponse = {
      statusCode: null,
      send: jest.fn()
    } as Partial<Response>;
    
    mockNext = jest.fn();
    
    // Mock retrieveSMS to return sample SMS
    (retrieveSMS as jest.Mock).mockReturnValue({
      From: '+1234567890',
      Body: 'Hello AI assistant'
    });
    
    // Mock PhoneNumberModel.findOne
    phoneNumberFindOneExecMock = jest.fn().mockResolvedValue({
      phoneNumber: '+1234567890',
      ownerUser: 'user123',
      subscription: 'chat/chat123'
    });
    
    mockPhoneNumberModel.findOne.mockReturnValue({
      exec: phoneNumberFindOneExecMock
    });
    
    // Mock categorize text
    mockTranscriptModel.categorizeText.mockResolvedValue({ 
      category: 'text' 
    });
    
    // Mock chat instance
    mockChatInstance = {
      _id: 'chat123',
      transcriptId: 'transcript123',
      addMessage: jest.fn().mockImplementation((context, message, urlFn) => {
        const mockMessage = { _id: 'msg123' };
        urlFn(mockChatInstance, mockMessage);
        return Promise.resolve(mockMessage);
      })
    };
    
    mockChatModel.findById.mockResolvedValue(mockChatInstance);
    
    // Mock transcript
    const mockTranscript = {
      messages: [
        { _id: 'msg1', role: 'user', category: 'text' },
        { _id: 'msg2', role: 'assistant', category: 'text' }
      ]
    };
    
    mockTranscriptModel.findById.mockResolvedValue(mockTranscript);
    
    // Mock assistant
    mockGetAssistant.mockReturnValue({
      info: { assistantName: 'TestAssistant' }
    });
    
    // Clear console logs
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should add message to chat and return success', async () => {
    await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Check phone number was looked up
    expect(mockPhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: '+1234567890'
    });
    
    // Check text was categorized
    expect(mockTranscriptModel.categorizeText).toHaveBeenCalledWith('Hello AI assistant');
    
    // Check chat was found
    expect(mockChatModel.findById).toHaveBeenCalledWith('chat123');
    
    // Check message was added
    expect(mockChatInstance.addMessage).toHaveBeenCalled();
    
    // Check response
    expect(mockResponse.statusCode).toBe(200);
    expect(mockResponse.send).toHaveBeenCalledWith({ status: 'ok' });
  });

  test('should reject if phone number not found', async () => {
    // Mock no phone number found
    phoneNumberFindOneExecMock.mockResolvedValue(null);
    
    await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
    
    expect(mockNext).toHaveBeenCalledWith(mockHttpErrors.FORBIDDEN);
  });

  test('should reject if phone has no subscription', async () => {
    // Mock phone without subscription
    phoneNumberFindOneExecMock.mockResolvedValue({
      phoneNumber: '+1234567890',
      ownerUser: 'user123',
      // No subscription field
    });
    
    await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
    
    expect(mockNext).toHaveBeenCalledWith(mockHttpErrors.NOT_FOUND);
  });

  test('should reject if chat not found', async () => {
    // Mock chat not found
    mockChatModel.findById.mockResolvedValue(null);
    
    await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
    
    expect(mockNext).toHaveBeenCalledWith(mockHttpErrors.NOT_FOUND);
  });

  test('should handle errors and pass to next middleware', async () => {
    const mockError = new Error('Database error');
    phoneNumberFindOneExecMock.mockRejectedValue(mockError);
    
    await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
    
    expect(mockNext).toHaveBeenCalledWith(mockError);
  });
});