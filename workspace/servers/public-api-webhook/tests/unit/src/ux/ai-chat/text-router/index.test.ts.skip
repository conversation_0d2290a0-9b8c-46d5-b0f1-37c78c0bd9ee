import { Router } from 'express';
import { router } from '../../../../../../src/ux/ai-chat/text-router';
import * as addChatMessage from '../../../../../../src/ux/ai-chat/text-router/add-chat-message';

// Mock the dependencies
jest.mock('express', () => ({
  Router: jest.fn().mockReturnValue({
    use: jest.fn()
  })
}));

jest.mock('../../../../../../src/ux/ai-chat/text-router/add-chat-message', () => ({
  addChatMessage: jest.fn()
}));

describe('AI Chat Text Router', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should set up router with addChatMessage middleware', () => {
    // Since the router is created when the module is imported, we need
    // to re-import it to test the setup
    jest.isolateModules(() => {
      // Re-import the module
      const { router } = require('../../../../../../src/ux/ai-chat/text-router');
      
      // Check that router.use was called with addChatMessage
      expect((router as any).use).toHaveBeenCalledWith(addChatMessage.addChatMessage);
    });
  });
});