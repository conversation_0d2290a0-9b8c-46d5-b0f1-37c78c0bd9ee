import { addChatMessage } from '../../../../../../../src/ux/ai-chat/text-router/add-chat-message';
import { retrieveSMS } from '../../../../../../../src/utils/phone-number';
import { PhoneNumberModel, ChatModel, TranscriptModel, CHAT_CATEGORY_ENUM } from '@divinci-ai/server-models';
import { unravelTarget } from '@divinci-ai/models';
import { CHAT_AI_ASSISTANT } from '@divinci-ai/server-tools';
import { HTTP_ERRORS } from '@divinci-ai/server-utils';
import { Request, Response } from 'express';

// Mock the dependencies
jest.mock('../../../../../../../src/utils/phone-number', () => ({
  retrieveSMS: jest.fn()
}));

jest.mock('@divinci-ai/models', () => ({
  unravelTarget: jest.fn().mockReturnValue({ id: 'chat-123' }),
  TranscriptMessage: jest.fn().mockImplementation(() => ({}))
}));

jest.mock('@divinci-ai/server-tools', () => ({
  CHAT_AI_ASSISTANT: {
    getAssistant: jest.fn().mockReturnValue({
      info: { assistantName: 'TestAssistant' }
    })
  }
}));

jest.mock('@divinci-ai/server-utils', () => ({
  HTTP_ERRORS: {
    FORBIDDEN: new Error('Forbidden'),
    NOT_FOUND: new Error('Not Found')
  }
}));

// Mock the MongoDB models
jest.mock('@divinci-ai/server-models', () => {
  const chatModelMock = {
    findById: jest.fn(),
    addMessage: jest.fn()
  };
  
  return {
    PhoneNumberModel: {
      findOne: jest.fn()
    },
    ChatModel: chatModelMock,
    TranscriptModel: {
      findById: jest.fn(),
      categorizeText: jest.fn()
    },
    CHAT_CATEGORY_ENUM: {
      TEXT: 'text',
      UNKNOWN: 'unknown'
    }
  };
});

describe('addChatMessage Stress Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;
  let mockPhoneNumber: any;
  let mockChat: any;
  let mockTranscript: any;
  let findOneExecMock: jest.Mock;
  let addMessageMock: jest.Mock;
  let findByIdMock: jest.Mock;
  let findTranscriptByIdMock: jest.Mock;
  let categorizeTextMock: jest.Mock;
  
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup mock request, response, and next
    mockRequest = {};
    mockResponse = {
      statusCode: 0,
      send: jest.fn()
    };
    mockNext = jest.fn();
    
    // Setup phone number mock
    mockPhoneNumber = {
      phoneNumber: '+1234567890',
      ownerUser: 'user-123',
      subscription: 'chat:chat-123' // Target format
    };
    
    // Setup chat model mock
    mockChat = {
      _id: 'chat-123',
      transcriptId: 'transcript-123',
      addMessage: jest.fn().mockImplementation((context, params, urlFn) => {
        // Simulate message creation and URL generation
        const message = { _id: 'message-123', content: params.content };
        urlFn(mockChat, message);
        return Promise.resolve(message);
      })
    };
    
    // Setup transcript model mock
    mockTranscript = {
      _id: 'transcript-123',
      messages: [
        { _id: 'message-1', role: 'user', category: 'text' },
        { _id: 'message-2', role: 'assistant', category: 'text' }
      ].reverse() // Pre-reverse for the find operation
    };
    
    // Setup mock implementations
    findOneExecMock = jest.fn().mockResolvedValue(mockPhoneNumber);
    (PhoneNumberModel.findOne as jest.Mock).mockReturnValue({
      exec: findOneExecMock
    });
    
    findByIdMock = jest.fn().mockResolvedValue(mockChat);
    (ChatModel.findById as jest.Mock).mockImplementation(findByIdMock);
    
    findTranscriptByIdMock = jest.fn().mockResolvedValue(mockTranscript);
    (TranscriptModel.findById as jest.Mock).mockImplementation(findTranscriptByIdMock);
    
    categorizeTextMock = jest.fn().mockResolvedValue({ category: CHAT_CATEGORY_ENUM.TEXT });
    (TranscriptModel.categorizeText as jest.Mock).mockImplementation(categorizeTextMock);
    
    // Setup retrieveSMS to return valid data
    (retrieveSMS as jest.Mock).mockReturnValue({
      From: '+1234567890',
      Body: 'Test message',
      To: '+0987654321'
    });
  });

  test('should handle rapid concurrent message submissions', async () => {
    // Simulate multiple users sending messages concurrently
    const messageCount = 50;
    const phoneNumbers = Array.from({ length: messageCount }, (_, i) => `+123456${i.toString().padStart(4, '0')}`);
    
    // Create different phone number records for each request
    const phoneNumberMocks = phoneNumbers.map((phoneNumber, i) => ({
      phoneNumber,
      ownerUser: `user-${i}`,
      subscription: `chat:chat-${i}`
    }));
    
    // Create different chat models for each request
    const chatMocks = phoneNumberMocks.map((phone, i) => ({
      _id: `chat-${i}`,
      transcriptId: `transcript-${i}`,
      addMessage: jest.fn().mockResolvedValue({ _id: `message-${i}` })
    }));
    
    // Setup mocks to return different values based on the input
    findOneExecMock.mockImplementation((phoneNumber) => {
      const index = phoneNumbers.indexOf(phoneNumber.phoneNumber);
      return Promise.resolve(index >= 0 ? phoneNumberMocks[index] : null);
    });
    
    findByIdMock.mockImplementation((chatId) => {
      const index = parseInt(chatId.split('-')[1]);
      return Promise.resolve(index >= 0 && index < chatMocks.length ? chatMocks[index] : null);
    });
    
    // Create a request for each phone number
    const requests = phoneNumbers.map(phoneNumber => {
      const req = { ...mockRequest };
      
      // Set up retrieveSMS to return different values for different requests
      (retrieveSMS as jest.Mock).mockImplementationOnce(() => ({
        From: phoneNumber,
        Body: `Message from ${phoneNumber}`,
        To: '+0987654321'
      }));
      
      return req;
    });
    
    // Measure the time to process all requests concurrently
    const startTime = performance.now();
    
    await Promise.all(
      requests.map(req => addChatMessage(req as Request, mockResponse as Response, mockNext))
    );
    
    const endTime = performance.now();
    const totalDuration = endTime - startTime;
    const avgDuration = totalDuration / messageCount;
    
    console.log(`Processed ${messageCount} concurrent message submissions in ${totalDuration.toFixed(2)}ms`);
    console.log(`Average time per message: ${avgDuration.toFixed(2)}ms`);
    
    // Verify all requests were successfully processed
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.send).toHaveBeenCalledTimes(messageCount);
    expect(TranscriptModel.categorizeText).toHaveBeenCalledTimes(messageCount);
  });

  test('should maintain performance with large message history', async () => {
    // Test with different transcript sizes
    const historySizes = [10, 100, 1000, 10000];
    const results: Record<number, number> = {};
    
    for (const size of historySizes) {
      // Clear mocks between test cases
      jest.clearAllMocks();
      
      // Create a transcript with the specified number of messages
      const largeHistory = Array.from({ length: size }, (_, i) => ({
        _id: `hist-msg-${i}`,
        role: i % 2 === 0 ? 'user' : 'assistant',
        category: 'text',
        content: `Message ${i}`
      })).reverse(); // Pre-reverse for the find operation
      
      // Update the transcript mock
      mockTranscript.messages = largeHistory;
      findTranscriptByIdMock.mockResolvedValue(mockTranscript);
      
      // Measure time to process a message with this history size
      const startTime = performance.now();
      await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      results[size] = duration;
      
      console.log(`History size ${size}: ${duration.toFixed(2)}ms`);
    }
    
    // Log performance comparison
    console.log('Performance comparison by history size:');
    for (const [size, duration] of Object.entries(results)) {
      const ratio = duration / results[10]; // Compare to smallest size
      console.log(`${size} messages: ${duration.toFixed(2)}ms (${ratio.toFixed(2)}x baseline)`);
    }
    
    // We don't assert specific timing values, but instead check that
    // all requests were processed successfully
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.statusCode).toBe(200);
  });

  test('should handle message categorization efficiently', async () => {
    // Test different types of messages to categorize
    const messageTypes = [
      { type: 'short', content: 'Hello' },
      { type: 'medium', content: 'This is a medium length message that needs to be categorized properly.' },
      { type: 'long', content: 'A'.repeat(1000) + ' This is a very long message that contains substantial text content that needs to be processed and categorized efficiently without causing performance degradation in the system.' },
      { type: 'complex', content: 'This message contains special characters !@#$%^&*() and multiple\nline\nbreaks as well as   excess   whitespace.' }
    ];
    
    const results: Record<string, number> = {};
    
    for (const { type, content } of messageTypes) {
      // Clear mocks
      jest.clearAllMocks();
      
      // Update SMS body
      (retrieveSMS as jest.Mock).mockReturnValue({
        From: '+1234567890',
        Body: content,
        To: '+0987654321'
      });
      
      // Measure time to categorize and process
      const startTime = performance.now();
      await addChatMessage(mockRequest as Request, mockResponse as Response, mockNext);
      const endTime = performance.now();
      
      const duration = endTime - startTime;
      results[type] = duration;
      
      console.log(`Message type '${type}': ${duration.toFixed(2)}ms`);
      
      // Verify categorization was called
      expect(TranscriptModel.categorizeText).toHaveBeenCalledWith(content);
    }
    
    // Log comparative performance
    console.log('Categorization performance comparison:');
    for (const [type, duration] of Object.entries(results)) {
      const ratio = duration / results.short;
      console.log(`${type}: ${duration.toFixed(2)}ms (${ratio.toFixed(2)}x short message)`);
    }
  });
});