import { isValidPhoneNumber } from '../../../../../../../src/ux/user/util/valid-phonenumber';

import { PhoneNumberModel } from '@divinci-ai/server-models';

describe('isValidPhoneNumber', () => {
  it('returns true when phone number exists in the database', async () => {
    PhoneNumberModel.findOne = jest.fn().mockResolvedValue({});
    expect(await isValidPhoneNumber({phonenumber: '************'})).toBe(true);
  });

  it('returns false when phone number does not exist in the database', async () => {
    PhoneNumberModel.findOne = jest.fn().mockResolvedValue(null);
    expect(await isValidPhoneNumber({phonenumber: '************'})).toBe(false);
  });

  it('queries the database with correct phone number', async () => {
    const findOneSpy = jest.spyOn(PhoneNumberModel, 'findOne').mockResolvedValue({});
    await isValidPhoneNumber({phonenumber: '************'});
    expect(findOneSpy).toHaveBeenCalledWith({phoneNumber: '************'});
  });
});