import { handleVerification } from '../../../../../../src/ux/user/text-router/phone-number';
import { retrieveSMS } from '../../../../../../src/utils/phone-number';
import { PhoneNumberVerificationModel, PhoneNumberModel } from '@divinci-ai/server-models';
import { Request, Response } from 'express';

// Mock the dependencies
jest.mock('../../../../../../src/utils/phone-number', () => ({
  retrieveSMS: jest.fn()
}));

jest.mock('@divinci-ai/server-models', () => ({
  PhoneNumberVerificationModel: {
    find: jest.fn(),
    deleteOne: jest.fn()
  },
  PhoneNumberModel: jest.fn()
}));

jest.mock('@divinci-ai/server-globals', () => ({
  getTwilio: jest.fn().mockReturnValue({
    twilioClient: {
      messages: {
        create: jest.fn()
      }
    }
  }),
  TWILIO_PHONE_NUMBER: '1234567890'
}));

describe.skip('handleVerification Compatibility Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: jest.Mock;
  let findExecMock: jest.Mock;
  let deleteOneMock: jest.Mock;
  let createMessageMock: jest.Mock;
  
  // SMS data formats for different versions
  const smsVersions = [
    // Current version format
    {
      name: 'current',
      data: {
        From: '+1234567890',
        Body: 'verify 12345678',
        To: '+0987654321',
        SmsStatus: 'received'
      }
    },
    // Legacy format with slightly different fields
    {
      name: 'legacy',
      data: {
        From: '+1234567890',
        Body: 'verify 12345678',
        To: '+0987654321',
        SmsStatus: 'received',
        // Legacy might have additional or different fields
        sent_from: 'LEGACY_SYSTEM'
      }
    },
    // Future potential format (just for demonstration)
    {
      name: 'future',
      data: {
        From: '+1234567890',
        Body: 'verify 12345678',
        To: '+0987654321',
        SmsStatus: 'received',
        // Future might have new fields
        mediaUrl: 'https://example.com/media',
        segmentCount: '1'
      }
    }
  ];
  
  // Different verification document formats
  const verificationFormats = [
    // Current format
    {
      name: 'current',
      data: [{
        _id: 'verification123',
        phoneNumber: '+1234567890',
        ownerUser: 'user123',
        verficationCode: '12345678'
      }]
    },
    // Different verification code field name (compatibility issue)
    {
      name: 'alternative-field-name',
      data: [{
        _id: 'verification123',
        phoneNumber: '+1234567890',
        ownerUser: 'user123',
        verificationCode: '12345678' // "verification" spelled correctly but incompatible
      }]
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockRequest = {};
    mockResponse = {
      statusCode: 0,
      send: jest.fn()
    };
    mockNext = jest.fn();
    
    // Setup mocks for MongoDB models
    findExecMock = jest.fn();
    (PhoneNumberVerificationModel.find as jest.Mock).mockReturnValue({
      exec: findExecMock
    });
    
    deleteOneMock = jest.fn().mockResolvedValue({ acknowledged: true, deletedCount: 1 });
    (PhoneNumberVerificationModel.deleteOne as jest.Mock).mockImplementation(deleteOneMock);
    
    // Mock PhoneNumberModel
    (PhoneNumberModel as unknown as jest.Mock).mockImplementation(function() {
      return {
        save: jest.fn().mockResolvedValue(true)
      };
    });
    
    // Mock Twilio create message
    createMessageMock = jest.fn().mockResolvedValue({
      sid: 'message123',
      status: 'sent'
    });
    require('@divinci-ai/server-globals').getTwilio().twilioClient.messages.create = createMessageMock;
    
    // Default SMS data
    (retrieveSMS as jest.Mock).mockReturnValue({
      From: '+1234567890',
      Body: 'verify 12345678',
      To: '+0987654321',
      SmsStatus: 'received'
    });
    
    // Default verification data
    findExecMock.mockResolvedValue([{
      _id: 'verification123',
      phoneNumber: '+1234567890',
      ownerUser: 'user123',
      verficationCode: '12345678'
    }]);
  });

  test.each(smsVersions)('should handle $name SMS format', async ({ data }) => {
    // Set up the SMS data for this version
    (retrieveSMS as jest.Mock).mockReturnValue(data);
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Verify we correctly extracted the verification code regardless of format
    expect(PhoneNumberVerificationModel.find).toHaveBeenCalledWith({
      phoneNumber: '+1234567890'
    });
    
    // Expect successful verification
    expect(mockResponse.statusCode).toBe(200);
    expect(mockResponse.send).toHaveBeenCalledWith({ status: 'ok' });
  });

  test.each(verificationFormats)('should handle $name verification doc format', async ({ name, data }) => {
    findExecMock.mockResolvedValue(data);
    
    if (name === 'current') {
      await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
      
      // Should successfully verify
      expect(mockResponse.statusCode).toBe(200);
      expect(mockResponse.send).toHaveBeenCalledWith({ status: 'ok' });
    } else {
      // For the alternative field name format, we expect it to not find a match
      // because the field name doesn't match - this is a compatibility issue
      await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
      
      // Should pass to next middleware without verifying
      expect(mockNext).toHaveBeenCalled();
      expect(mockResponse.send).not.toHaveBeenCalled();
    }
  });

  test('should handle different verification code patterns', async () => {
    // Test various verification code formats
    const testCases = [
      { body: 'verify 12345678', shouldMatch: true },
      { body: 'VERIFY 12345678', shouldMatch: true }, // uppercase
      { body: 'verify 1234abcd', shouldMatch: true }, // alphanumeric
      { body: 'verify  12345678', shouldMatch: false }, // extra space
      { body: 'verifying 12345678', shouldMatch: false }, // wrong command
      { body: 'verify12345678', shouldMatch: false }, // missing space
      { body: 'verify 123456', shouldMatch: false }, // too short
      { body: 'verify 123456789', shouldMatch: false } // too long
    ];
    
    for (const testCase of testCases) {
      jest.clearAllMocks();
      (retrieveSMS as jest.Mock).mockReturnValue({
        From: '+1234567890',
        Body: testCase.body,
        To: '+0987654321',
        SmsStatus: 'received'
      });
      
      await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
      
      if (testCase.shouldMatch) {
        expect(PhoneNumberVerificationModel.find).toHaveBeenCalled();
      } else {
        expect(mockNext).toHaveBeenCalled();
        expect(PhoneNumberVerificationModel.find).not.toHaveBeenCalled();
      }
    }
  });

  test('should handle different Twilio client versions', async () => {
    // Test compatibility with different client APIs
    const clientVersions = [
      // Current version (as set up in beforeEach)
      {
        setup: () => {
          // Keep default implementation
        },
        expectSuccess: true
      },
      // Legacy version with different method
      {
        setup: () => {
          const twilioMock = require('@divinci-ai/server-globals').getTwilio().twilioClient;
          delete twilioMock.messages.create;
          // Old version used a different method
          twilioMock.sendSms = jest.fn().mockResolvedValue({
            sid: 'message123',
            status: 'sent'
          });
        },
        expectSuccess: false // Should fail with current implementation
      },
      // Future version with async builder pattern
      {
        setup: () => {
          const twilioMock = require('@divinci-ai/server-globals').getTwilio().twilioClient;
          twilioMock.messages.create = jest.fn().mockReturnValue({
            then: (callback: any) => {
              callback({
                sid: 'message123',
                status: 'sent'
              });
              return {
                catch: jest.fn()
              };
            }
          });
        },
        expectSuccess: true
      }
    ];
    
    for (const version of clientVersions) {
      jest.clearAllMocks();
      version.setup();
      
      if (version.expectSuccess) {
        await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
        expect(mockResponse.statusCode).toBe(200);
      } else {
        // This will throw due to incompatibility
        await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
        expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
      }
    }
  });
});