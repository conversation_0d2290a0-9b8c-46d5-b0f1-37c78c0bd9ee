import { isValidPhoneNumber } from "../../../../../../../src/ux/user/util/valid-phonenumber";
import { PhoneNumberModel } from "@divinci-ai/server-models";

// Mock the PhoneNumberModel
jest.mock("@divinci-ai/server-models", ()=>({
  PhoneNumberModel: {
    findOne: jest.fn()
  }
}));

describe.skip("isValidPhoneNumber utility", ()=>{
  let findOneExecMock: jest.Mock;

  beforeEach(()=>{
    findOneExecMock = jest.fn();
    (PhoneNumberModel.findOne as jest.Mock).mockReturnValue({
      exec: findOneExecMock
    });
  });

  afterEach(()=>{
    jest.clearAllMocks();
  });

  test("should return true when phone number exists in database", async ()=>{
    // Mock findOne to return a phone number record
    const mockPhoneNumber = {
      phoneNumber: "+1234567890",
      ownerUser: "user123"
    };

    findOneExecMock.mockResolvedValue(mockPhoneNumber);

    const result = await isValidPhoneNumber({ phonenumber: "+1234567890" });

    // Check that findOne was called with correct parameters
    expect(PhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: "+1234567890"
    });

    // Check that result is true
    expect(result).toBe(true);
  });

  test("should return false when phone number does not exist in database", async ()=>{
    // Mock findOne to return null (no record found)
    findOneExecMock.mockResolvedValue(null);

    const result = await isValidPhoneNumber({ phonenumber: "+1234567890" });

    // Check that findOne was called with correct parameters
    expect(PhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: "+1234567890"
    });

    // Check that result is false
    expect(result).toBe(false);
  });

  test("should handle different phone number formats", async ()=>{
    // Mock findOne to return a phone number record
    findOneExecMock.mockResolvedValue({
      phoneNumber: "1234567890",
      ownerUser: "user123"
    });

    await isValidPhoneNumber({ phonenumber: "1234567890" });

    // Check that findOne was called with correct parameters
    expect(PhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: "1234567890"
    });
  });

  test("should handle database errors properly", async ()=>{
    // Mock database error
    const dbError = new Error("Database connection error");
    findOneExecMock.mockRejectedValue(dbError);

    await expect(isValidPhoneNumber({ phonenumber: "+1234567890" })).rejects.toThrow(dbError);
  });
});