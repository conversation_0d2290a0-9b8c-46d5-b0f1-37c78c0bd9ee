import { describe, it, expect, vi, beforeEach } from 'vitest';
import { handleVerification } from '../../../../../../src/ux/user/text-router/phone-number';
import { retrieveSMS } from '../../../../../../src/utils/phone-number';
import { PhoneNumberVerificationModel, PhoneNumberModel } from '@divinci-ai/server-models';
import { getTwilio, TWILIO_PHONE_NUMBER } from '@divinci-ai/server-globals';
import { Request, Response } from 'express';

// Define mocks before using them
const mockTwilioMessagesCreate = vi.fn();

// Mock the dependencies
vi.mock('../../../../../../src/utils/phone-number', () => ({
  retrieveSMS: vi.fn()
}));

vi.mock('@divinci-ai/server-models', () => ({
  PhoneNumberVerificationModel: {
    find: vi.fn(),
    deleteOne: vi.fn()
  },
  PhoneNumberModel: vi.fn()
}));

// Mock Twilio client directly
vi.mock('@divinci-ai/server-globals', () => ({
  getTwilio: vi.fn().mockReturnValue({
    twilioClient: {
      messages: {
        create: mockTwilioMessagesCreate
      }
    }
  }),
  TWILIO_PHONE_NUMBER: '**********'
}));

describe('Twilio API Integration Tests', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: any;
  let findExecMock: any;
  let deleteOneMock: any;
  let savePhoneNumberMock: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    mockRequest = {};
    mockResponse = {
      statusCode: 0,
      send: vi.fn()
    };
    mockNext = vi.fn();
    
    // Setup mocks for MongoDB models
    findExecMock = vi.fn().mockResolvedValue([{
      _id: 'verification123',
      phoneNumber: '+**********',
      ownerUser: 'user123',
      verficationCode: '********'
    }]);
    
    vi.mocked(PhoneNumberVerificationModel.find).mockReturnValue({
      exec: findExecMock
    });
    
    deleteOneMock = vi.fn().mockResolvedValue({ acknowledged: true, deletedCount: 1 });
    vi.mocked(PhoneNumberVerificationModel.deleteOne).mockImplementation(deleteOneMock);
    
    // Mock PhoneNumberModel constructor and save method
    savePhoneNumberMock = vi.fn().mockResolvedValue(true);
    (PhoneNumberModel as unknown as any).mockImplementation(function() {
      return {
        save: savePhoneNumberMock
      };
    });
    
    // Default SMS data
    vi.mocked(retrieveSMS).mockReturnValue({
      From: '+**********',
      Body: 'verify ********',
      To: '+**********'
    });
    
    // Set up default successful Twilio response
    mockTwilioMessagesCreate.mockResolvedValue({
      sid: 'SM12345',
      status: 'queued',
      date_created: new Date().toISOString(),
      date_sent: null,
      account_sid: 'test-account-sid',
      to: '+**********',
      from: '+**********',
      body: 'Congradulations, you can now choose a thread to subscribe to'
    });
  });

  it('should successfully call Twilio API', async () => {
    // Default setup already includes successful Twilio response
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Verify success response
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.statusCode).toBe(200);
    expect(mockResponse.send).toHaveBeenCalledWith({ status: 'ok' });
    
    // Verify all database operations were performed
    expect(PhoneNumberModel).toHaveBeenCalled();
    expect(savePhoneNumberMock).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalled();
    
    // Verify Twilio API was called with correct parameters
    expect(mockTwilioMessagesCreate).toHaveBeenCalledWith({
      from: '+**********',
      to: '+**********',
      body: 'Congradulations, you can now choose a thread to subscribe to'
    });
  });

  it('should handle Twilio API rate limiting', async () => {
    // Mock a rate limit error from Twilio API
    mockTwilioMessagesCreate.mockRejectedValue({
      status: 429,
      code: 20429,
      message: 'Too Many Requests',
      moreInfo: 'https://www.twilio.com/docs/errors/20429'
    });
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Should pass error to next middleware
    expect(mockNext).toHaveBeenCalledWith(expect.any(Object));
    
    // Database operations should still have succeeded
    expect(PhoneNumberModel).toHaveBeenCalled();
    expect(savePhoneNumberMock).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalled();
  });

  it('should handle Twilio API authentication errors', async () => {
    // Mock an authentication error from Twilio API
    mockTwilioMessagesCreate.mockRejectedValue({
      status: 401,
      code: 20003,
      message: 'Authentication Error',
      moreInfo: 'https://www.twilio.com/docs/errors/20003'
    });
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Should pass error to next middleware
    expect(mockNext).toHaveBeenCalledWith(expect.any(Object));
    
    // Database operations should still have succeeded
    expect(PhoneNumberModel).toHaveBeenCalled();
    expect(savePhoneNumberMock).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalled();
  });
  
  it('should properly handle message delivery errors from Twilio', async () => {
    // Mock a successful API call but with a failed message status
    mockTwilioMessagesCreate.mockResolvedValue({
      sid: 'SM12345',
      status: 'failed',
      errorCode: '30003',
      errorMessage: 'Unreachable destination handset',
      dateCreated: new Date().toISOString(),
      dateSent: new Date().toISOString(),
      accountSid: 'test-account-sid',
      to: '+**********',
      from: '+**********',
      body: 'Congradulations, you can now choose a thread to subscribe to'
    });
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Since the API call itself succeeds (resolves the promise), 
    // but the message fails to deliver, the webhook should still return success
    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.statusCode).toBe(200);
    expect(mockResponse.send).toHaveBeenCalledWith({ status: 'ok' });
    
    // Database operations should have succeeded
    expect(PhoneNumberModel).toHaveBeenCalled();
    expect(savePhoneNumberMock).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalled();
  });
  
  it('should handle Twilio API network timeouts', async () => {
    // Mock a timeout from the Twilio API
    mockTwilioMessagesCreate.mockImplementation(() => {
      return new Promise((resolve, reject) => {
        // Never resolves, simulating a timeout
        setTimeout(() => {
          reject(new Error('Network request timed out'));
        }, 100);
      });
    });
    
    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);
    
    // Should pass error to next middleware
    expect(mockNext).toHaveBeenCalledWith(expect.any(Error));
    expect(mockNext.mock.calls[0][0].message).toBe('Network request timed out');
    
    // Database operations should still have succeeded
    expect(PhoneNumberModel).toHaveBeenCalled();
    expect(savePhoneNumberMock).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalled();
  });
});