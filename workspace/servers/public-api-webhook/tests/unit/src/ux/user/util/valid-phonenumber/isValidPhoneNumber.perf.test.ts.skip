import { isValidPhoneNumber } from '../../../../../../../src/ux/user/util/valid-phonenumber';
import { PhoneNumberModel } from '@divinci-ai/server-models';

// Mock the PhoneNumberModel
jest.mock('@divinci-ai/server-models', () => {
  const findOneMock = jest.fn();
  const execMock = jest.fn();
  findOneMock.mockReturnValue({ exec: execMock });
  
  return {
    PhoneNumberModel: {
      findOne: findOneMock
    }
  };
});

describe.skip('isValidPhoneNumber Performance Tests', () => {
  let findOneExecMock: jest.Mock;
  
  beforeEach(() => {
    findOneExecMock = PhoneNumberModel.findOne().exec;
    jest.clearAllMocks();
  });

  test('should have consistent performance with multiple calls', async () => {
    // Setup: Mock a successful database lookup
    const mockPhoneNumber = { 
      phoneNumber: '+1234567890',
      ownerUser: 'user123'
    };
    findOneExecMock.mockResolvedValue(mockPhoneNumber);
    
    // First call to establish baseline
    const startTime1 = performance.now();
    await isValidPhoneNumber({ phonenumber: '+1234567890' });
    const endTime1 = performance.now();
    const duration1 = endTime1 - startTime1;
    
    // Second call should have similar performance
    const startTime2 = performance.now();
    await isValidPhoneNumber({ phonenumber: '+1234567890' });
    const endTime2 = performance.now();
    const duration2 = endTime2 - startTime2;
    
    // The test is mainly a demonstration - in a real scenario we would:
    // 1. Run multiple iterations to get reliable measurements
    // 2. Set a proper threshold based on baseline measurement
    // 3. Use proper performance measurement tools
    
    // Log the times for reference
    console.log(`First call duration: ${duration1}ms`);
    console.log(`Second call duration: ${duration2}ms`);
    
    // We don't verify timing directly since this would create a brittle test
    // Instead we validate the function was called correctly
    expect(PhoneNumberModel.findOne).toHaveBeenCalledTimes(2);
    expect(PhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: '+1234567890'
    });
  });

  test('should use database indexing effectively', async () => {
    // This test demonstrates how we might verify efficient database access
    // by examining the query that is sent to the database
    
    const mockPhoneNumber = { 
      phoneNumber: '+1234567890',
      ownerUser: 'user123'
    };
    findOneExecMock.mockResolvedValue(mockPhoneNumber);
    
    await isValidPhoneNumber({ phonenumber: '+1234567890' });
    
    // Verify the correct query was created - in this case, checking that
    // we're querying by phoneNumber which should be indexed
    expect(PhoneNumberModel.findOne).toHaveBeenCalledWith({
      phoneNumber: '+1234567890'
    });
    
    // In a real performance test, we might:
    // 1. Mock the mongoose model more deeply to expose query details
    // 2. Examine if the query uses the index (e.g. by checking explain plan)
    // 3. Measure response times with different data volumes
  });

  test('should handle multiple concurrent requests efficiently', async () => {
    // Setup mock to return quickly
    findOneExecMock.mockImplementation(() => {
      return Promise.resolve({ phoneNumber: '+1234567890', ownerUser: 'user123' });
    });
    
    // Create an array of different phone numbers
    const phoneNumbers = Array.from({ length: 20 }, (_, i) => 
      ({ phonenumber: `+1234567${i.toString().padStart(3, '0')}` })
    );
    
    // Execute all validation requests concurrently
    const startTime = performance.now();
    await Promise.all(phoneNumbers.map(params => isValidPhoneNumber(params)));
    const endTime = performance.now();
    
    // Log the total duration
    const totalDuration = endTime - startTime;
    console.log(`Concurrent validation of ${phoneNumbers.length} phone numbers: ${totalDuration}ms`);
    
    // Verify all calls were made
    expect(PhoneNumberModel.findOne).toHaveBeenCalledTimes(phoneNumbers.length);
    
    // In a real test, we might compare this to a baseline of sequential execution
    // or set a max time threshold
  });
});