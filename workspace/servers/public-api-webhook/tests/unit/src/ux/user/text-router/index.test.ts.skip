import { Router } from 'express';
import { router } from '../../../../../../src/ux/user/text-router';
import * as phoneNumber from '../../../../../../src/ux/user/text-router/phone-number';

// Mock the dependencies
jest.mock('express', () => ({
  Router: jest.fn().mockReturnValue({
    use: jest.fn()
  })
}));

jest.mock('../../../../../../src/ux/user/text-router/phone-number', () => ({
  handleVerification: jest.fn()
}));

describe('User Text Router', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should set up router with handleVerification middleware', () => {
    // Since the router is created when the module is imported, we need
    // to re-import it to test the setup
    jest.isolateModules(() => {
      // Re-import the module
      const { router } = require('../../../../../../src/ux/user/text-router');
      
      // Check that router.use was called with handleVerification
      expect((router as any).use).toHaveBeenCalledWith(phoneNumber.handleVerification);
    });
  });
});