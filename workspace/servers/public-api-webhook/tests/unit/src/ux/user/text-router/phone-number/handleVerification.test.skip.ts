/**
 * This test is currently skipped due to issues with TypeScript errors in the test setup.
 * There are problems with mocking the Twilio client and other dependencies.
 *
 * To fix this test:
 * 1. Fix the TypeScript type errors in the mock setup
 * 2. Update the mocks to properly handle retrieveSMS
 * 3. Properly handle the PhoneNumberVerificationModel and PhoneNumberModel mocks
 */

import { handleVerification } from "../../../../../../../src/ux/user/text-router/phone-number";
import { Request, Response, NextFunction } from "express";
import { retrieveSMS } from "../../../../../../../src/utils/phone-number";
import { PhoneNumberVerificationModel, PhoneNumberModel } from "@divinci-ai/server-models";
import { getTwilio, TWILIO_PHONE_NUMBER } from "@divinci-ai/server-globals";

// Mock dependencies
jest.mock("../../../../../../../src/utils/phone-number", ()=>({
  retrieveSMS: jest.fn()
}));

jest.mock("@divinci-ai/server-models", ()=>{
  return {
    PhoneNumberVerificationModel: {
      find: jest.fn(),
      deleteOne: jest.fn().mockResolvedValue(undefined)
    },
    PhoneNumberModel: jest.fn()
  };
});

jest.mock("@divinci-ai/server-globals", ()=>({
  getTwilio: jest.fn(),
  TWILIO_PHONE_NUMBER: "1234567890"
}));

describe("handleVerification", ()=>{
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;
  let mockTwilioClient: any;
  let mockPhoneNumberModelInstance: any;
  let findExecMock: jest.Mock;

  beforeEach(()=>{
    mockRequest = {} as Partial<Request>;

    mockResponse = {
      statusCode: null,
      send: jest.fn()
    } as Partial<Response>;

    mockNext = jest.fn();

    mockTwilioClient = {
      messages: {
        create: jest.fn().mockResolvedValue({
          sid: "SM123456"
        })
      }
    };

    mockPhoneNumberModelInstance = {
      save: jest.fn().mockResolvedValue(undefined)
    };

    // Setup mocks
    (getTwilio as jest.Mock).mockReturnValue({
      twilioClient: mockTwilioClient
    });

    (PhoneNumberModel as jest.Mock).mockImplementation(()=>mockPhoneNumberModelInstance);

    findExecMock = jest.fn();
    (PhoneNumberVerificationModel.find as jest.Mock).mockImplementation(()=>({
      exec: findExecMock
    }));

    // Mock retrieveSMS to return a sample SMS
    (retrieveSMS as jest.Mock).mockReturnValue({
      From: "+1234567890",
      Body: "verify abcd1234"
    });

    // Clear console logs
    jest.spyOn(console, "log").mockImplementation(()=>{});
  });

  afterEach(()=>{
    jest.clearAllMocks();
  });

  test("should pass to next middleware if message is not a verification code", async ()=>{
    // Mock SMS with non-verification message
    (retrieveSMS as jest.Mock).mockReturnValue({
      From: "+1234567890",
      Body: "hello world"
    });

    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(PhoneNumberVerificationModel.find).not.toHaveBeenCalled();
  });

  test("should pass to next middleware if no verification found for phone number", async ()=>{
    // Mock empty verification results
    findExecMock.mockResolvedValue([]);

    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);

    expect(PhoneNumberVerificationModel.find).toHaveBeenCalledWith({
      phoneNumber: "+1234567890"
    });
    expect(mockNext).toHaveBeenCalled();
  });

  test("should pass to next middleware if verification code does not match", async ()=>{
    // Mock verification records with different code
    const mockVerifications = [
      {
        _id: "ver123",
        phoneNumber: "+1234567890",
        ownerUser: "user123",
        verficationCode: "differentcode"
      }
    ];

    findExecMock.mockResolvedValue(mockVerifications);

    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(PhoneNumberModel).not.toHaveBeenCalled();
  });

  test("should create phone number and send confirmation when verification code matches", async ()=>{
    // Mock verification with matching code
    const mockVerification = {
      _id: "ver123",
      phoneNumber: "+1234567890",
      ownerUser: "user123",
      verficationCode: "abcd1234"
    };

    findExecMock.mockResolvedValue([mockVerification]);

    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);

    // Check phone number creation
    expect(PhoneNumberModel).toHaveBeenCalledWith({
      ownerUser: "user123",
      phoneNumber: "+1234567890",
      subscription: undefined
    });
    expect(mockPhoneNumberModelInstance.save).toHaveBeenCalled();

    // Check verification deletion
    expect(PhoneNumberVerificationModel.deleteOne).toHaveBeenCalledWith({
      _id: "ver123"
    });

    // Check confirmation message
    expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
      from: "+1234567890",
      to: "+1234567890",
      body: expect.stringContaining("Congradulations")
    });

    // Check response
    expect(mockResponse.statusCode).toBe(200);
    expect(mockResponse.send).toHaveBeenCalledWith({ status: "ok" });
  });

  test("should pass errors to next middleware", async ()=>{
    const mockError = new Error("Database error");
    findExecMock.mockRejectedValue(mockError);

    await handleVerification(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockNext).toHaveBeenCalledWith(mockError);
  });
});
