# Public API Webhook Tests

This directory contains tests for the Public API Webhook server.

## Test Structure

- `unit/`: Unit tests for individual components
- `integration/`: Integration tests for API endpoints
- `e2e/`: End-to-end tests for complete workflows

## Module Resolution

The tests use Vitest for testing and support path aliases for easier imports:

- `@/*`: Resolves to `src/*` (e.g., `@/util/foo` resolves to `src/util/foo`)
- `~/*`: Resolves to `tests/*` (e.g., `~/helpers` resolves to `tests/helpers`)

### Example

```typescript
// Import using path alias
import { handleWebhook } from '@/ux/webhook/handler';

// Import from tests directory
import { mockRequest } from '~/helpers/mocks';
```

## Running Tests

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## Writing Tests

When writing tests, follow these guidelines:

1. Use Vitest's testing functions (`describe`, `it`, `expect`, etc.)
2. Use path aliases for imports when possible
3. Mock external dependencies
4. Test both success and error cases
5. Keep tests focused and small

### Example Test

```typescript
import { describe, it, expect, vi } from 'vitest';
import { handleWebhook } from '@/ux/webhook/handler';

describe('handleWebhook', () => {
  it('should return a 200 status code', async () => {
    // Arrange
    const req = {};
    const res = {
      end: vi.fn(),
      statusCode: null
    };
    const next = vi.fn();

    // Act
    await handleWebhook(req, res, next);

    // Assert
    expect(res.statusCode).toBe(200);
    expect(res.end).toHaveBeenCalledWith({ status: "ok" });
  });
});
```
