import { vi, beforeEach } from 'vitest';

// Mock environment variables
process.env.JSON_WEBTOKEN_SECRET = 'test-jwt-secret';
process.env.MONGODB_URI = 'mongodb://localhost:27017/test';
process.env.REDIS_URI = 'redis://localhost:6379';
process.env.TWILIO_ACCOUNT_SID = 'test-account-sid';
process.env.TWILIO_AUTH_TOKEN = 'test-auth-token';

// Mock jsonwebtoken
vi.mock('jsonwebtoken', async () => {
  const mockSign = vi.fn().mockReturnValue('test-token');
  const mockVerify = vi.fn().mockReturnValue({ userId: 'test-user-id' });
  
  return {
    sign: mockSign,
    verify: mockVerify,
    default: {
      sign: mockSign,
      verify: mockVerify,
    }
  };
});

// Global beforeEach
beforeEach(() => {
  vi.clearAllMocks();
});
