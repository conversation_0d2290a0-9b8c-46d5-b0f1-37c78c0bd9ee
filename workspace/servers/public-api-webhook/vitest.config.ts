import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    exclude: [
      '**/node_modules/**',
      '**/dist/**',
      '**/*.load.test.ts',
      '**/*.stress.test.ts',
      '**/*.perf.test.ts',
      '**/tests/unit/src/utils/phone-number/handleSMSBody.load.test.ts',
      '**/tests/unit/src/ux/ai-chat/text-router/add-chat-message/addChatMessage.stress.test.ts',
      '**/tests/unit/src/ux/user/util/valid-phonenumber/isValidPhoneNumber.perf.test.ts',
    ],
    pool: 'forks', // Use forks instead of threads to avoid memory issues
    poolOptions: {
      forks: {
        isolate: true,
      },
    },
    testTimeout: 10000, // Increase timeout to 10 seconds
    setupFiles: ['./tests/setup.ts'], // Include the setup file
    globals: true, // Allow global test functions
    environmentOptions: {
      // Increase memory limit
      '--max-old-space-size': '4096',
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './tests')
    }
  },
});
