footer {
  font-size: 11px;
  justify-content: end;
  display: flex;

  a {
    padding: 0 5px;
  }

  nav {
    display: flex;
    align-items: center;
  }

  .footerLinks {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
  }
  span,
  li,
  div {
    padding: 3px 7px;
    font-size: 11px;
  }
  li {
    padding: 0;
    margin: 0;
  }

  .footerLinks li:not(:last-child)::after {
    content: "•";
    margin: 0 0.5rem;
  }

  .copyright {
    margin-left: 0.5rem;
  }
}

.desktopMenuTop{
  display: flex; 
}
.groupMenuBorder {
  border: 2px solid #515273;
  border-radius: 7px;
  padding: 0px 0px 0px 4px;
}

.desktopMenuTop {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.navbarParent {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.navbarBrand {
  display: flex;
  align-items: center;
}

.navbarMenuTitleBox {
  margin-left: 20px;
  flex: 1;
}

.navbarMenuTitle {
  padding: 0 10px;
}

.topMenu {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
}

.pageBody {
  flex-grow: 1;
  overflow: auto;
}

@media (max-width: 768px) {
  *{
    box-sizing: border-box;
  }

  footer {
    justify-content: center;
  }
}
