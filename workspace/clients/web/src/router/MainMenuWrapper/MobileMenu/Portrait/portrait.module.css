@import "@styles/variables.css";

.mobileChatMenu {
  padding: 5px;
  padding-left: 11px;
  border: 1px solid rgb(81, 82, 115);
  border-radius: 7px;
  font-size: 15px;
  cursor: pointer;
}
.dropdown {
  position: relative;
  width: fit-content;
}

.mobileChatDropdown {
  background: var(--main-header-gradient-mid);
  min-width: 90%;
  position: absolute;
  top: 100%;
  left: 10px;
}

.dropdownItem {
  padding: 15px;
  &:hover {
    background: grey;
  }
}

.mobileChatTitle {
  padding: 0 10px;
  cursor: pointer;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 1000;
  background-color: var(--main-header-gradient-mid);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Optional: adds shadow for better visibility */
}

footer {
  position: relative;
  z-index: 2;
  background: var(--main-header-gradient-mid);
  margin-top: auto; /* Push footer to bottom */
  flex-shrink: 0; /* Prevent footer from shrinking */
}