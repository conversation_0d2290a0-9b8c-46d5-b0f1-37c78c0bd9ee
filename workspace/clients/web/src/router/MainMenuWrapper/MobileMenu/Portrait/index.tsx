import React, { useState, useEffect } from "react";
import { useLocation } from "react-router";
import { Link } from "react-router-dom";
import { MenuItem } from "../../menu-items";
import HamBurger from "../../icons8-hamburger-menu.svg";
import XIcon from "../../x-icon.svg";
import styles from "./portrait.module.css";
import moment from "moment";

import { PortraitMainMenu } from "./MainMenu";

interface MenuProps {
  children: React.ReactNode,
  items: MenuItem[],
  profileMenuItems: MenuItem[],
  user: any, // Replace `any` with a proper user type
  isAuthenticated: boolean,
}

export function PortraitMenu({
  children,
  items,
  profileMenuItems,
  user,
  isAuthenticated,
}: MenuProps){
  const [pastLocation, setPastLocation] = useState("");
  const location = useLocation();
  const [menuOff, setMenuOff] = useState(true);
  const { pathname } = useLocation(); // Access the current URL path

   // Only show chat-title if URL contains "ai-chat" or "white-label"
   const isChatPage =
   pathname.includes("ai-chat/") || pathname.includes("white-label/");

  useEffect(()=>{
    const href = location.pathname + location.search + location.hash;
    if(href === pastLocation) return;
    setPastLocation(href);
    setMenuOff(true);
  }, [location]);

  return (
    <div className="mobile-header">
      <div className="main-header">
        {menuOff ? (
          <HamBurger
            className="mobile-menu-button"
            onClick={()=>{
              setMenuOff(!menuOff);
            }}
          />
        ) : (
          <XIcon
            className="mobile-menu-button"
            onClick={()=>{
              setMenuOff(!menuOff);
            }}
          />
        )}
         {/* Conditionally show the Chat Title, aligned with some spacing */}
         <PortraitMainMenu />
        {isAuthenticated && (
          <div className="navbar-item has-dropdown is-hoverable">
            <div className="dropdown-trigger">
              <img
                className="transcript-users-img user-avatar"
                src={user.picture}
                alt={`${user.name}'s profile`}
              />
            </div>
            <div className="dropdown-menu" role="menu">
              <div className="dropdown-content">
                {profileMenuItems.map((subItem: MenuItem, index: number)=>(
                  <a
                    key={index}
                    href={subItem.linkTo || "#"}
                    onClick={subItem.onClick}
                    className="dropdown-item"
                  >
                    {subItem.title}
                  </a>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
      <aside className={`menu ${menuOff ? "is-hidden" : ""}`}>
        <p className="menu-label">Menu</p>
        <ul className="menu-list">
          {items.map((menuItem: MenuItem)=>(
            <li className="mobile-menu-li" key={menuItem.title}>
              {typeof menuItem.onClick === "function" ? (
                <span onClick={menuItem.onClick} className="is-white">
                  {menuItem.title}
                </span>
              ) : typeof menuItem.linkTo === "string" ? (
                <>
                  <Link to={menuItem.linkTo}>{menuItem.title}</Link>
                  {menuItem.subItems && (
                    <ul className="submenu-list">
                      {menuItem.subItems.map((subItem, subIndex) => (
                        <li key={subIndex}>
                          {subItem.linkTo ? (
                            <Link
                              to={subItem.linkTo}
                              className="submenu-item"
                              onClick={() => setMenuOff(true)} // Close menu after selection
                            >
                              {subItem.title}
                            </Link>
                          ) : (
                            <span className="submenu-item">
                              {subItem.title}
                            </span>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              ) : null}
            </li>
          ))}
        </ul>
      </aside>
      <main
        className="content"
        style={{
          display: menuOff ? "block" : "none",
          flex: "1 1 auto",
          overflowX: "hidden",
          overflowY: "auto",
          margin: "0",
          width: "100%",
          boxSizing: "border-box",
          minHeight: 0, /* Allow flex item to shrink below content size */
        }}
      >
        {children}
      </main>
      <footer aria-label="Site footer">
        <nav aria-label="Footer navigation">
          {!isChatPage && (
            <span>
              <span>
               <a
                  href={`//divinci.app/terms-of-service.html`}
                  target="_blank"
                  rel="noopener noreferrer"
                >Terms</a>
                -
                <a
                  href={`//divinci.app/privacy-policy.html`}
                  target="_blank"
                  rel="noopener noreferrer"
                >Privacy Policy</a>
              </span>
              -
            </span>
           )}
          <ul className={styles.footerLinks}>
            <li>
              <a
                href={`//divinci.app/ai-safety-ethics.html`}
                target="_blank"
                rel="noopener noreferrer"
              >
                <i className={`fas fa-check ${styles.footerFaIcon}`}></i> AI Safety
              </a>
            </li>
          </ul>
          -
          <div className={styles.copyright}>
            <i className={`fas fa-copyright ${styles.footerFaIcon}`}></i> {moment().year()} Divinci AI
          </div>
        </nav>
      </footer>
    </div>
  );
}
