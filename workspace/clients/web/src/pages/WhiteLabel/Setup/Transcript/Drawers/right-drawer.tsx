import React, { useEffect, useRef } from 'react';
import { RagVectorIndexUsingContext } from '../../../../../components/ReleaseComponents/RagVector';
import {
  LOCAL_STORAGE_KEYS, RIGHT_SIDEBAR_WIDTH_OPEN, ZERO_PX,
} from "../../../../../globals/constants/values";

interface RightDrawerProps {
  rightDrawerOpen: boolean;
  toggleDrawer: (isOpen: boolean, localStorageKey: string, cssVarOpenWidth: string, cssVarClosedWidth: string) => void;
}

export const RightDrawer = ({ rightDrawerOpen, toggleDrawer }: RightDrawerProps) => {
  const sidebarRef = useRef<HTMLDivElement>(null);
  const toggleButtonRef = useRef<HTMLDivElement>(null);

  const updateToggleButtonPosition = () => {
    if (sidebarRef.current && toggleButtonRef.current) {
      toggleButtonRef.current.style.right = `${sidebarRef.current.offsetWidth}px`;
    }
  };

  useEffect(() => {
    const handleResize = () => {
      updateToggleButtonPosition();
    };

    window.addEventListener("resize", handleResize);
    updateToggleButtonPosition(); // Initial update

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  useEffect(() => {
    updateToggleButtonPosition();
  }, [rightDrawerOpen]);

  return (
    <>
      <div
        ref={sidebarRef}
        className={`right-sidebar ${rightDrawerOpen ? "drawer-open" : ""}`}
        id="right-drawer"
        style={{
          width: `${rightDrawerOpen ? RIGHT_SIDEBAR_WIDTH_OPEN : ZERO_PX}`, // Default width values for open and closed states
        }}
      >
        <button disabled onClick={() => {/* Logic to add new AI */}}>Add New AI</button>
        <button disabled onClick={() => {/* Logic to assign roles/instructions */}}>Assign Roles</button>
        <input disabled type="range" onChange={() => {/* Logic to modify temperature */}} />
        <RagVectorIndexUsingContext />
      </div>
      <div
        ref={toggleButtonRef}
        className={`drawer-toggle-button right-drawer-toggle ${rightDrawerOpen ? "drawer-open" : ""} dropdown is-hoverable`}
        onClick={() => toggleDrawer(
          rightDrawerOpen,
          LOCAL_STORAGE_KEYS.RIGHT_DRAWER_OPEN,
          RIGHT_SIDEBAR_WIDTH_OPEN,
          ZERO_PX,
        )}
      >
        <div className="dropdown-trigger">
          <div className="dropdown-trigger"></div>
        </div>
        <div className="dropdown-menu drawer-dropdown-menu" id="dropdown-menu4" role="menu">
          <div className="dropdown-content">
            <div className="dropdown-item">
              <p>{rightDrawerOpen ? "Close Sidebar" : "Open Sidebar"}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
