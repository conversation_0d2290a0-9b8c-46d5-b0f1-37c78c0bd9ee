import React, { useState, useEffect, useRef } from "react";
import {
  NotificationData,
  useNotificationDefaultSettings,
} from "./useNotificationDefaultSettings";
import { useAuth0FetchJSON } from "../../../../globals/auth0-user";
import {
  PATH_WHITELABEL_INDEX,
  PATH_WHITELABEL_NOTIFICATION_SETTINGS_DEFAULT,
} from "../../paths";
import {
  successfulNotificationSettingsSaved,
  successfulNotificationSettingsDeleted,
  showNotificationTemporarily,
  updateNotificationAndShowSuccess,
  verifyEmailAndShowNotification,
  createNewNotificationData,
} from "./notificationUtils";
import "./notifications.css";
import { VerifiedEmail } from "./email";

export interface DefaultNotificationSettingsProps {
  setting?: NotificationData,
  onUpdate: (newNotificationData: NotificationData) => void,
}

export const DefaultNotificationSettings: React.FC<
  DefaultNotificationSettingsProps
> = ({ setting, onUpdate })=>{
  // Check if setting is null before trying to access its properties
  if(!setting) {
    return <div>⏳ Loading...</div>;
  }

  const fetchAuth = useAuth0FetchJSON(); // Make sure to call this hook at the top level
  const [emailInput, setEmailInput] = useState("");

  const [
    showSuccessDefaultNotificationSettings,
    setShowSuccessDefaultNotificationSettings,
  ] = useState(false);
  const [emailEnabled, setEmailEnabled] = useState(false);
  const [smsEnabled, setSmsEnabled] = useState(false);
  const [emails, setEmails] = useState<VerifiedEmail[]>([]);
  const [phoneNumbers, setPhoneNumbers] = useState("");
  const [notificationMessage, setNotificationMessage] = useState("");
  const [newEmailAdded, setNewEmailAdded] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { updateNotificationDefaultSettings } = useNotificationDefaultSettings(
    setting.whitelabelId,
  );

  // const [inputWidth, setInputWidth] = useState('100%');
  const containerRef = useRef(null);

  useEffect(() => {
    if (setting) {
      setEmailEnabled(setting.emailEnabled);
      setSmsEnabled(setting.smsEnabled);
      // Convert the array of emails to a string separated by commas
      setEmails(setting.emails); // Directly set as it's already in the correct format
      setPhoneNumbers(setting.phoneNumbers.join(", "));
    }
  }, [setting]);

  const saveNewVerifiedEmail = async (
    email: string,
    whitelabelId: string,
  ): Promise<any> => {
    try {
      // Assuming auth0FetchJSON is already set up to handle the authentication and headers
      const response = await fetchAuth(
        `${PATH_WHITELABEL_INDEX}/${whitelabelId}/verified-emails/${email}`,
        {
          method: "POST",
          body: JSON.stringify({ whitelabelIds: [whitelabelId] }), // Assuming the backend expects an array
          headers: {
            "Content-Type": "application/json",
          },
        },
      );
      const data =
        typeof response === "string" ? JSON.parse(response) : response;

      if(data.success) {
        return data;
      } else {
        // Handle the case where the email verification wasn't successful
        // This could be due to the email not being found, already verified, etc.
        // Optionally, show an error message to the user
        return "❌ Failed to verify email. Please try again.";
      }
    }catch(error) {
      console.error("❌ Error verifying new email: ", email, error);
      // Handle any errors that occurred during the fetch
      // Optionally, show an error message to the user
      return "❌ An error occurred while verifying the email.";
    }
  };

  // In handleAddEmail, update the state and set a flag that an email was added
  const handleAddEmail = async ()=>{
    const newEmail = emailInput.trim();

    if(emails.some((email)=>(email.email === newEmail))) {
      alert("🤞😃 This email is already added."); // Consider using a more user-friendly notification system
      return;
    }

    setIsSubmitting(true);

    try {
      const cloudflareEmail: any = await verifyEmailAndShowNotification(
        newEmail,
        setShowSuccessDefaultNotificationSettings,
        setNotificationMessage,
        fetchAuth,
      );

      if(
        cloudflareEmail &&
        cloudflareEmail.message &&
        cloudflareEmail.message.includes("Emails already exists")
      ) {
        // Handle the case where the email already exists
        // setEmails(prevEmails => [...prevEmails, cloudflareEmail]);

        // Update the NotificationDefaultSettings pushing the existing email
        updateNotificationDefaultSettings({
          _id: setting._id, // Ensure _id is included
          emails: [
            ...emails,
            {
              ...cloudflareEmail,
              verified: cloudflareEmail.verified || false,
              whitelabelIds: [setting.whitelabelId],
              _id: cloudflareEmail._id,
            },
          ], // Ensure all required fields are included
        });
      } else {
        const newlySavedEmail = await saveNewVerifiedEmail(
          newEmail,
          setting.whitelabelId,
        );

        const verifiedEmail: VerifiedEmail = {
          email: newEmail,
          whitelabelIds: [setting.whitelabelId],
          _id: newlySavedEmail._id,
          verified: newlySavedEmail.verified,
        };

        setEmails((prevEmails)=>[...prevEmails, verifiedEmail]);

        // Update the NotificationDefaultSettings with the new emails array
        updateNotificationDefaultSettings({
          _id: setting._id, // Ensure _id is included
          emails: [...emails, verifiedEmail],
        });
      }

      setEmailInput(""); // Clear input field
    }catch(error){
      console.error("❌ Error adding email: ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(()=>{
    if(newEmailAdded) {
      // Call the function to save the settings once the emails state is updated
      handleSaveDefaultSettings();

      // Reset the flag
      setNewEmailAdded(false);
    }
  }, [emails, newEmailAdded]); // Only run when emails or newEmailAdded changes

  const handleVerifyEmail = async (emailId: string): Promise<string> => {
    try {
      // Assuming auth0FetchJSON is already set up to handle the authentication and headers
      const response = await fetchAuth(
        `${PATH_WHITELABEL_INDEX}/${setting.whitelabelId}/verified-emails/${emailId}`,
        { method: "PUT" },
      );
      const data =
        typeof response === "string" ? JSON.parse(response) : response;

      if (data.success) {
        // Update the email tag to show it's verified
        const updatedTags = emails.map((verifiedEmail) => {
          if (verifiedEmail._id === emailId) {
            return {
              ...verifiedEmail,
              verified: true,
            };
          }
          return verifiedEmail;
        });
        setEmails(updatedTags);
        return "✅ Email verified successfully.";
      } else {
        // Handle the case where the email verification wasn't successful
        // This could be due to the email not being found, already verified, etc.
        // Optionally, show an error message to the user
        return "❌ Failed to verify email. Please try again.";
      }
    }catch(error) {
      console.error("❌ Error verifying default email: ", emailId, error);
      // Handle any errors that occurred during the fetch
      // Optionally, show an error message to the user
      return "❌ An error occurred while verifying the email. ";
    }
  };

  const handleEmailKeydown = (e: any) => {
    if(e.key === "Enter" || e.key === ",") {
      e.preventDefault(); // Stop form submission with Enter
      handleAddEmail();
    }
  };

  const handleRemoveEmail = async (emailId: string) => {
    setIsSubmitting(true); // Disable the UI to prevent further actions

    try {
      // Call the API to delete the VerifiedEmail by its _id
      const response = await fetchAuth(
        `${PATH_WHITELABEL_INDEX}/${setting.whitelabelId}${PATH_WHITELABEL_NOTIFICATION_SETTINGS_DEFAULT}/verified-emails/${emailId}`,
        { method: "DELETE" },
      );

      const data =
        typeof response === "string" ? JSON.parse(response) : response;

      if(!data.success) {
        throw new Error("❌ Failed to delete the email.");
      }

      // Update the local state to remove the email from the list
      const updatedEmails = emails.filter((email) => email._id !== emailId);
      setEmails(updatedEmails);

      // Update the NotificationDefaultSettings to remove the deleted email
      await updateNotificationDefaultSettings({
        _id: setting._id, // Ensure _id is included
        emailEnabled: setting.emailEnabled,
        smsEnabled: setting.smsEnabled,
        emails: updatedEmails, // Update with the new emails array
        phoneNumbers: setting.phoneNumbers,
      });

      showNotificationTemporarily(
        setShowSuccessDefaultNotificationSettings,
        setNotificationMessage,
        successfulNotificationSettingsDeleted,
      );
    }catch(error) {
      console.error("❌ Error removing email: ", error);
      // Handle error (e.g., show an error message to the user)
      alert("❌ Error removing email.");
    } finally {
      setIsSubmitting(false); // Re-enable the UI
    }
  };

  const renderEmailTags = () => {
    return emails.map((email, index) => (
      <div
        key={email._id || `${email.email}-${index}`}
        className="control email-tag"
      >
        <div className="tags has-addons">
          <span className="tag is-dark is-medium">{email.email}</span>
          <span
            className={`tag ${email.verified ? "is-success" : "is-warning"} is-medium`}
          >
            {email.verified ? "Verified" : "Pending"}
          </span>
          {!email.verified && (
            <span
              onClick={() => email._id && handleVerifyEmail(email._id)}
              className="tag button is-medium is-info"
            >
              Verify
            </span>
          )}
          <button
            onClick={() => email._id && handleRemoveEmail(email._id)}
            className="tag delete is-large"
          ></button>
        </div>
      </div>
    ));
  };

  const handleSaveDefaultSettings = async () => {
    const newNotificationData = createNewNotificationData(
      setting,
      emailEnabled,
      smsEnabled,
      emails,
      phoneNumbers,
    );

    await updateNotificationAndShowSuccess(
      onUpdate,
      newNotificationData,
      setShowSuccessDefaultNotificationSettings,
      setNotificationMessage,
      successfulNotificationSettingsSaved,
    );
  };

  return (
    <div className="default-notification-settings">
      {showSuccessDefaultNotificationSettings && (
        <div className="notification notification-banner is-success">
          <button
            className="delete"
            onClick={() => setShowSuccessDefaultNotificationSettings(false)}
          ></button>
          {notificationMessage}
        </div>
      )}
      {/* <div className="field">
        <label className="checkbox">
          Email Enabled:
          <input
            type="checkbox"
            checked={emailEnabled}
            onChange={(e) => setEmailEnabled(e.target.checked)}
          />
        </label>
      </div> */}
      <div className="under-construction">
      🚧 Under Construction 🚧
      </div>
      <div className="field tags-input-container" ref={containerRef}>
        Default Emails:
        <div className="field is-grouped is-grouped-multiline">
          {renderEmailTags()}
        </div>
        <input
          disabled
          className="input"
          type="text"
          placeholder="Enter email(s)"
          value={emailInput}
          onChange={(e) => setEmailInput(e.target.value)}
          onKeyDown={handleEmailKeydown}
          // ℹ️ Temporarily Disabled. Please Keep.
          // disabled={isSubmitting} // Disable the input field when isSubmitting is true
        />
        <div className="control">
          <button
            disabled
            className="button"
            onClick={handleAddEmail}
            // ℹ️ Temporarily Disabled. Please Keep.
            // disabled={isSubmitting}
          >
            Add Email
          </button>
        </div>
      </div>
      <div className="field tags-input-container">
      <label className="checkbox">SMS Enabled: </label>
        <input
          disabled
          type="checkbox"
          checked={smsEnabled}
          onChange={(e)=>setSmsEnabled(e.target.checked)}
        />
      <label> Default Phone Numbers: </label>
        <input
          disabled
          type="tel"
          className="input input-box"
          value={phoneNumbers}
          onChange={(e)=>setPhoneNumbers(e.target.value)}
        />
      <button
        disabled
        className="button"
        onClick={handleSaveDefaultSettings}
      >
        Save
      </button>
      </div>
    </div>
  );
};
