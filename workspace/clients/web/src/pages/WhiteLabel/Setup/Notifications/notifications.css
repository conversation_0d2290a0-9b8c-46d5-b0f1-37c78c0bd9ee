.tags-input-container {
  border: 1px solid rgb(105 116 140 / 19%);
  border-radius: 5px;
  padding: 8px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 4px;
  margin-bottom: .75rem;

  .tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }

  .tags-input-container .input {
    border: none;
    outline: none;
    flex-grow: 1;
    min-width: 120px; /* Adjust the minimum width as needed */
  }
}

.default-notification-settings {
  .under-construction {
    display: flex; 
    justify-content: center; 
    align-items: center; 
    font-size: large;
    font-weight: 600;
    margin-bottom: 20px;
    opacity: 0.5;
  }  
}

input {
  margin-right: 8px;
  width: auto;
}

.notif-form-btn{
  padding: 8px 0;
}

.trigger-title-input{
  padding: 10px 0;
}

@media (max-width: 768px) {
  input.input-box {
    width: -webkit-fill-available;
  }

  .title.is-4 {
    word-break: normal;
    font-size: larger;
  }
}