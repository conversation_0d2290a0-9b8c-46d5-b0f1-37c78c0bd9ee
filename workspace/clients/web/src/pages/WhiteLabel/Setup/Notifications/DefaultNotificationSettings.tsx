import React from "react";
import { DefaultNotificationSettings } from "./NotificationDefaultSettingsForm";
import { VerifiedEmail } from "./email";

import "./notifications.css";

interface DefaultNotificationSettingsWrapperProps {
  emails: VerifiedEmail[],
  updateDefaultNotificationEmails: (newNotificationData: {
    emails: VerifiedEmail[],
  }) => void,
}

export const DefaultNotificationSettingsWrapper: React.FC<
  DefaultNotificationSettingsWrapperProps
> = ({ emails, updateDefaultNotificationEmails })=>{
  return (
    <section className="box">
      <h1 className="title is-4">📢 Default Notification Emails</h1>
      <DefaultNotificationSettings
        setting={{
          emails,
          emailEnabled: true,
          smsEnabled: false,
          phoneNumbers: [],
          whitelabelId: "",
        }}
        onUpdate={updateDefaultNotificationEmails}
      />
    </section>
  );
};
