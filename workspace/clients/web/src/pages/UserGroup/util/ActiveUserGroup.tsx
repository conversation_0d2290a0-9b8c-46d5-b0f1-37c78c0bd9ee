import React from "react";
import styles from "./ActiveUserGroup.module.css";

import { useActiveUserGroup, UserGroupDoc } from "../../../globals/user-preferences";

export function ActiveUserGroupSet(
  { userGroup: displayedGroup }: { userGroup: UserGroupDoc }
){
  const { activeGroup, setActiveGroup } = useActiveUserGroup();

  if(!activeGroup || activeGroup._id !== displayedGroup._id){
    return (
      <button
        className="button"
        onClick={(e)=>{ e.preventDefault(); setActiveGroup(displayedGroup._id); }}
      >Set as Active Group</button>
    );
  }
  return (
    <div className={styles.btnPadding}>
    <button
      className="button"
      onClick={(e)=>{ e.preventDefault(); setActiveGroup(null); }}
    >Remove as Active Group</button>
    </div>
  );
}
