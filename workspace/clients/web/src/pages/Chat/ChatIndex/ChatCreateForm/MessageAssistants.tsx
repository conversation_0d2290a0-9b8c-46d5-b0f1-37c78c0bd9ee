import React, { useMemo } from "react";

import { AIAssistantInfo } from "@divinci-ai/models";

import { useCategoryAssistant } from "../../../../components/Transcript/data/CategoryAssistantContext";
import styles from "./mobile-styles.module.css";

export function MessageAssistants(){
  const { assistant: selectedAssistant, setAssistant, info, suggested } = useCategoryAssistant();

  const assistantList = useMemo(()=>{
    if(!info) return [];
    const allAssistants: Array<AIAssistantInfo> = [];
    for(const { available } of Object.values(info)){
      allAssistants.push(...available);
    }
    return allAssistants;
  }, [info]);

  if(!info) return null;
  if(!selectedAssistant) return null;

  return (
    <div className={styles.messageAssistantsWrapper}>
      <table className="table">
        <thead>
          <tr>
            <th>Title</th>
            <th>Category</th>
            <th>Author</th>
            <th>Details</th>
          </tr>
        </thead>
        <tbody>
          {assistantList.map((assistant)=>(
            <tr
              key={assistant.assistantName}
              className={
                suggested.use && suggested.loading ? "" :
                assistant.assistantName !== selectedAssistant.assistantName ? "" :
                "is-selected"
              }
          >
            <td
              style={{ cursor: "pointer" }}
              onClick={()=>(setAssistant({
                category: assistant.category,
                assistantName: assistant.assistantName
              }))}
            >{assistant.title}</td>
            <td
              style={{ cursor: "pointer" }}
              onClick={()=>(setAssistant({
                category: assistant.category,
                assistantName: assistant.assistantName
              }))}
            >{assistant.category}</td>
            <td><a target="_blank" href={assistant.orgUrl} >{assistant.org}</a></td>
            <td><a target="_blank" href={assistant.url} >View More</a></td>
          </tr>
        ))}
      </tbody>
    </table>
    </div>
  );
}
