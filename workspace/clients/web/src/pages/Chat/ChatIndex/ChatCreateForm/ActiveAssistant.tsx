import React, { useMemo } from "react";

import { useCategoryAssistant } from "../../../../components/Transcript/data/CategoryAssistantContext";
import styles from "./mobile-styles.module.css";

export function ActiveAssistant(){
  const { assistant: selected, info } = useCategoryAssistant();

  const assistant = useMemo(()=>{
    if(!selected) return void 0;
    const category = info[selected.category];
    if(!category) return void 0;
    const selectedAssistant = selected.assistantName || category.default;
    return category.available.find(({ assistantName })=>(assistantName === selectedAssistant));
  }, [selected, info]);

  if(!assistant) return null;

  return (
    <div className={styles.activeAssistantWrapper}>
      <h1><a target="_blank" href={assistant.url} >{assistant.title}</a></h1>
      <div><span>Assistant Category: </span><span>{assistant.category}</span></div>
      <div><span>Author: </span><span>{assistant.org}</span></div>
      <div>{assistant.description}</div>
    </div>
  );
}

