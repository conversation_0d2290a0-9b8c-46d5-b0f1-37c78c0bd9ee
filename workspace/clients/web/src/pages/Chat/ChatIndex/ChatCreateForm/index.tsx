import React, { useState } from "react";
import { MessageForm } from "./MessageForm";

import { aichatCreate } from "@divinci-ai/actions";
import { useAuth0Fetch } from "../../../../globals/auth0-user";
import { MessageAssistants } from "./MessageAssistants";
import { SuggestedStatus } from "./SuggestedStatus";
import { ActiveAssistant } from "./ActiveAssistant";
import { useNavigate } from "react-router";
import { replaceParams } from "@divinci-ai/utils";
import { PATH_CHAT_ITEM } from "../../paths";
import styles from "./mobile-styles.module.css";

export function ChatCreateForm(){
  const auth0 = useAuth0Fetch();
  const [content, setContent] = useState("");
  const navigate = useNavigate();

  return (
    <div className={`chat-item-wrapper ${styles.mobileContainer}`}>
      <MessageAssistants />
      <SuggestedStatus content={content} />
      <ActiveAssistant />
      <div style={{ flexGrow: 1 }} />
      <MessageForm
        value={content}
        onChange={setContent}
        addMessage={async ({ content, assistantName, })=>{
          const { chat } = await aichatCreate(auth0, {
            title: (new Date()).toString(),
            titleSet: false,
            releases: [],
            initialMessage: { assistantName, content }
          });
          navigate(replaceParams(
            PATH_CHAT_ITEM, { chatId: chat._id }
          ));
        }}
      />
    </div>
  );
}
