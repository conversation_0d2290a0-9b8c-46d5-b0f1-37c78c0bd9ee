import React from "react";
import { useCategoryAssistant } from "../../../../components/Transcript/data/CategoryAssistantContext";
import { setMaxDecimalPlaces } from "@divinci-ai/utils";
import styles from "./mobile-styles.module.css";

export function SuggestedStatus({ content }: { content: string }){
  const { suggested, setSuggest } = useCategoryAssistant();
  return (
    <div className={`box ${styles.suggestedStatusWrapper}`}>
      <span>Use Suggested:</span>
      <input
        type="checkbox"
        checked={suggested.use}
        onChange={(e)=>(setSuggest(e.target.checked, content))}
      />
      {!suggested.use ? null : suggested.loading ? (
        <>
          <div style={{ flexGrow: 1 }} />
          <span>Loading...</span>
        </>
      ) : (
        <>
          <div style={{ flexGrow: 1 }} />
          <span>Confidence: {setMaxDecimalPlaces(suggested.confidence, 2) * 100}%</span>
        </>
      )}
    </div>
  );
}

