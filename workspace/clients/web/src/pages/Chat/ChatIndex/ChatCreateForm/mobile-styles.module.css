@import "@styles/variables.css";

/* Container styles */
.mobileContainer {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  /* Removed padding from container to not affect the message input */
}

/* MessageAssistants styles */
.messageAssistantsWrapper {
  width: 100%;
  overflow-x: auto;
  margin-bottom: 10px;
  margin-left: 15px; /* Increased margin to move it more to the right */
}

.messageAssistantsWrapper table {
  width: 100%;
  min-width: 100%;
}

.messageAssistantsWrapper th,
.messageAssistantsWrapper td {
  padding: 0.5rem;
  white-space: nowrap;
}

/* For very small screens */
@media (max-width: 480px) {
  .messageAssistantsWrapper th,
  .messageAssistantsWrapper td {
    padding: 0.3rem;
    font-size: 0.8rem;
  }
}

/* SuggestedStatus styles */
.suggestedStatusWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  padding: 10px;
  margin-bottom: 10px;
  margin-left: 15px; /* Add margin to move it to the right */
}

.suggestedStatusWrapper > span:first-child {
  white-space: nowrap;
}

.suggestedStatusWrapper > div {
  margin-left: auto;
}

/* ActiveAssistant styles */
.activeAssistantWrapper {
  padding: 12px;
  margin-bottom: 10px;
  width: 100%;
  margin-left: 15px; /* Add margin to move it to the right */
}

.activeAssistantWrapper h1 {
  font-size: 1.2rem;
  margin-bottom: 8px;
  word-break: break-word;
}

.activeAssistantWrapper div {
  margin-bottom: 5px;
  word-break: break-word;
}

@media (max-width: 768px) {
  /* Override the flex-direction from new-file-form.css */
  .suggestedStatusWrapper {
    flex-direction: row !important; /* Use !important to ensure it takes precedence */
  }
}

@media (max-width: 480px) {
  .activeAssistantWrapper h1 {
    font-size: 1rem;
  }

  .activeAssistantWrapper div {
    font-size: 0.9rem;
  }
}
