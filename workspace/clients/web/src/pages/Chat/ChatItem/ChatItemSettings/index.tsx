import React from "react";
import { useNavigate } from "react-router";
import { useAuth0 } from "@auth0/auth0-react";
import { useAuth0FetchJSON } from "../../../../globals/auth0-user";
import styles from "./chatSettings.module.css";

import { ErrorPage } from "../../../Util/ErrorPage";

import { DocPermissionInput } from "../../../../components/Permission/input";

import { EditTitle } from "./EditTitle";
import { ForkForm } from "./Fork";
import { PATH_CHAT_INDEX } from "../../paths";

import { useChat } from "../data/Chat";

export function ChatItemSettings(){
  const { isAuthenticated } = useAuth0();
  const auth0FetchJSON = useAuth0FetchJSON();

  const { value } = useChat();

  const navigate = useNavigate();

  if(!isAuthenticated){
    return (
      <ErrorPage
        error={{
          statusCode: 401,
          message: "Need to Be authenticated to edit the Settings",
          json: {},
        }}
      />
    );
  }
  if(!value || !value.chat){
    return <h3>Loading...</h3>;
  }

  const { chat } = value;

  return (
    <div className="container is-fluid">
      <div className="columns is-centered">
        <div className="column">
            <div className="section">
          <section className="box">
              <h1 className="title is-4">Fork This Chat</h1>
              <ForkForm chat={chat} />
          </section>

          <section className="box">
              <h1 className="title is-4">Change Title</h1>
              <EditTitle chat={chat} />
          </section>
            </div>
        </div>
      </div>

      <div className={`section ${styles.setPermission}`}>
        <h2 className={`subtitle ${styles.setPermissionSubtitle}`}>Set Permissions</h2>
        <DocPermissionInput
          apiUrlPrefix={`${PATH_CHAT_INDEX}/${chat._id}/permission/`}
        />
      </div>

      <div className="container is-fluid">
      <div className="columns is-centered">
        <div className="column">
            <div className={`section ${styles.setSectionPosition}`}>
          <div className="box">
          <h3 className="subtitle">Delete Chat</h3>
          <button
            className="button is-danger"
            onClick={async (e)=>{
              e.preventDefault();
              await auth0FetchJSON(`${PATH_CHAT_INDEX}/${chat._id}`, {
                method: "DELETE",
              });
              navigate(PATH_CHAT_INDEX);
            }}
          >
            Delete
          </button>
        </div>
      </div>
    </div>
    </div>
    </div>
    </div>
  );
}
