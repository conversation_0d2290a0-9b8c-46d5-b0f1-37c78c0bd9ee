import React, { useEffect, useState, useRef } from "react";
import { EmojiPickerProvider } from "../../../../components/Transcript/views/Chat/EmojiPickerContext";
import { PATH_CHAT_INDEX } from "../../paths";

import { Transcript } from "../../../../components/Transcript/views/Chat/Transcript";
import { ChatMessageHeader } from "./ChatMessage/Header";
import { ChatMessageFooter } from "./ChatMessage/Footer";
import { useAuth0 } from "@auth0/auth0-react";

import { TranscriptMessage } from "@divinci-ai/models";
import { EmojiListProvider } from "../../../../components/Transcript/views/Emoji";
import { useTranscript } from "../../../../components/Transcript/data/TranscriptContext";
import { FeedbackProvider } from "../../../../components/Transcript/views/Chat/FeedbackContext";
import { ChatMessageForm } from "./MessageForm";
import { useParams } from "react-router";
import { ChatListProvider } from "../../data/ChatListContext";
import {
  LEFT_SIDEBAR_WIDTH_OPEN, RIGHT_SIDEBAR_WIDTH_OPEN,
  LEFT_SIDEBAR_WIDTH_CLOSED, ZERO_PX,
} from "../../../../globals/constants/values";

import { LeftDrawer, RightDrawer, toggleDrawer } from "./Drawers";

export function ChatItemWithDrawers() {
  const [drawerOpen, setDrawerOpen] = useState(() => {
    const savedState = localStorage.getItem("drawerOpen");
    return savedState ? JSON.parse(savedState) : true;
  });

  const [rightDrawerOpen, setRightDrawerOpen] = useState(() => {
    const savedRightState = localStorage.getItem("rightDrawerOpen");
    return savedRightState ? JSON.parse(savedRightState) : false;
  });

  useEffect(() => {
    const sidebarWidth = drawerOpen ? LEFT_SIDEBAR_WIDTH_OPEN : LEFT_SIDEBAR_WIDTH_CLOSED;
    const rightSidebarWidth = rightDrawerOpen ? RIGHT_SIDEBAR_WIDTH_OPEN : ZERO_PX;

    document.documentElement.style.setProperty("--sidebar-width", sidebarWidth);
    document.documentElement.style.setProperty(
      "--right-drawer-width",
      rightSidebarWidth,
    );
  }, [drawerOpen, rightDrawerOpen]);

  return (
    <ChatListProvider>
      <div className="mainInner">
        <LeftDrawer
          drawerOpen={drawerOpen}
          toggleDrawer={(
            isOpen,
            localStorageKey,
            cssVarOpenWidth,
            cssVarClosedWidth,
          ) =>
            toggleDrawer(
              isOpen,
              localStorageKey,
              cssVarOpenWidth,
              cssVarClosedWidth,
              setDrawerOpen,
              setRightDrawerOpen // Pass the opposite drawer's setState
            )
          }
        />
        <div className={`${drawerOpen ? "open" : "closed"} chat-item-wrapper`}>
          <ChatItem />
        </div>
        <RightDrawer
          rightDrawerOpen={rightDrawerOpen}
          toggleDrawer={(
            isOpen,
            localStorageKey,
            cssVarOpenWidth,
            cssVarClosedWidth,
          ) =>
            toggleDrawer(
              isOpen,
              localStorageKey,
              cssVarOpenWidth,
              cssVarClosedWidth,
              setRightDrawerOpen,
              setDrawerOpen // Pass the opposite drawer's setState
            )
          }
        />
      </div>
    </ChatListProvider>
  );
}

export function ChatItem(){
  const { user } = useAuth0();
  const { transcript, setTranscript } = useTranscript();
  const params = useParams();

  const updateMessageInChat = (updatedMessage: TranscriptMessage)=>{
    if(!transcript){
      throw new Error("Can't update message without Transcript");
    }
    const updatedMessages = transcript.messages.map(
      (message: TranscriptMessage)=>(
        message._id === updatedMessage._id
          ? { ...message, ...updatedMessage }
          : message
      )
    );
    setTranscript({ ...transcript, messages: updatedMessages });
  };

  // Ref for chat transcript scrolling
  const transcriptAreaRef = useRef<HTMLDivElement>(null);
  const [isScrollToBottomVisible, setIsScrollToBottomVisible] = useState(false);

  const scrollToBottom = ()=>{
    if(transcriptAreaRef.current) {
      transcriptAreaRef.current.scrollTop =
        transcriptAreaRef.current.scrollHeight;
    }
  };

  // Function to check if the chat area is larger than the visible viewport
  const checkScrollToBottomVisibility = ()=>{
    if(transcriptAreaRef.current) {
      const { scrollHeight, clientHeight } = transcriptAreaRef.current;

      // If the scrollHeight is larger than the visible area (clientHeight)
      const isContentOverflowing = scrollHeight > clientHeight;

      setIsScrollToBottomVisible(isContentOverflowing);
    }
  };

  useEffect(()=>{
    // Initial check
    checkScrollToBottomVisibility();

    // Recheck when content changes (new messages added)
    const observer = new MutationObserver(checkScrollToBottomVisibility);
    if(transcriptAreaRef.current) {
      observer.observe(transcriptAreaRef.current, {
        childList: true,
        subtree: true,
      });
    }

    // Add resize event listener to handle window resize
    window.addEventListener("resize", checkScrollToBottomVisibility);

    return ()=>{
      window.removeEventListener("resize", checkScrollToBottomVisibility);
      if(transcriptAreaRef.current) {
        observer.disconnect(); // Clean up observer on unmount
      }
    };
  }, [(!transcript ? [] : transcript.messages)]);

  return (
    <div className="chat-item-wrapper">
      <div className="chat-transcript-area" ref={transcriptAreaRef}>
        <FeedbackProvider>
          <EmojiPickerProvider>
            <Transcript
              MessageWrapper={({ children, message })=>(
                <>
                  <EmojiListProvider
                    message={message}
                    urlPrefix={`${PATH_CHAT_INDEX}/${params.chatId}`}
                  >
                    <ChatMessageHeader
                      message={message}
                      onUpdateMessage={updateMessageInChat}
                      user={user || null}
                    />
                    {children}
                    <ChatMessageFooter message={message} />
                  </EmojiListProvider>
                </>
              )}
            />
          </EmojiPickerProvider>
        </FeedbackProvider>
      </div>

      {/* Show the scroll to bottom button only if the chat area overflows */}
      {isScrollToBottomVisible && (
        <button
          className="button is-small is-ghost scroll-to-bottom-button"
          onClick={scrollToBottom}
          title="Scroll to Bottom"
        >
          ⬇
        </button>
      )}

      <ChatMessageForm />

    </div>
  );
}
