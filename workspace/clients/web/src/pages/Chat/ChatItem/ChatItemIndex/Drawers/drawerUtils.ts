import React from "react";

export const toggleDrawer = (
  isOpen: boolean,
  localStorageKey: string,
  cssVarOpenWidth: string,
  cssVarClosedWidth: string,
  setDrawerState: React.Dispatch<React.SetStateAction<boolean>>,
  setOppositeDrawerState?: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const newState = !isOpen;
  setDrawerState(newState);
  localStorage.setItem(localStorageKey, JSON.stringify(newState));
  
  const cssVarName = localStorageKey === 'leftDrawerOpen' ? '--sidebar-width' : '--right-drawer-width';
  const newWidth = newState ? cssVarOpenWidth : cssVarClosedWidth;
  document.documentElement.style.setProperty(cssVarName, newWidth);

  // Close the opposite drawer in smaller viewports if this drawer is being opened
  if (newState && window.innerWidth <= 768 && setOppositeDrawerState) {
    setOppositeDrawerState(false);
    const oppositeKey = localStorageKey === 'leftDrawerOpen' ? 'rightDrawerOpen' : 'leftDrawerOpen';
    const oppositeVarName = localStorageKey === 'leftDrawerOpen' ? '--right-drawer-width' : '--sidebar-width';
    localStorage.setItem(oppositeKey, 'false');
    document.documentElement.style.setProperty(oppositeVarName, cssVarClosedWidth);
  }
};
