import React, { useEffect, useState, PropsWithChildren } from "react";
import { LeftDrawer } from "./left-drawer";
export { LeftDrawer };
import { RightDrawer } from "./right-drawer";
export { RightDrawer };
import { toggleDrawer } from "./drawerUtils";
export { toggleDrawer };

import {
  LEFT_SIDEBAR_WIDTH_OPEN, RIGHT_SIDEBAR_WIDTH_OPEN,
  LEFT_SIDEBAR_WIDTH_CLOSED, ZERO_PX,
  LOCAL_STORAGE_KEYS
} from "../../../../../globals/constants/values";


export function ChatDrawers({ children }: PropsWithChildren){
  const [drawerOpen, setDrawerOpen] = useState(()=>{
    const savedState = localStorage.getItem(LOCAL_STORAGE_KEYS.LEFT_DRAWER_OPEN);
    return savedState ? JSON.parse(savedState) : true;
  });

  const [rightDrawerOpen, setRightDrawerOpen] = useState(()=>{
    const savedRightState = localStorage.getItem(LOCAL_STORAGE_KEYS.RIGHT_DRAWER_OPEN);
    return savedRightState ? JSON.parse(savedRightState) : false;
  });

  useEffect(()=>{
    const sidebarWidth = drawerOpen ? LEFT_SIDEBAR_WIDTH_OPEN : LEFT_SIDEBAR_WIDTH_CLOSED;
    const rightSidebarWidth = rightDrawerOpen ? RIGHT_SIDEBAR_WIDTH_OPEN : ZERO_PX;

    document.documentElement.style.setProperty("--sidebar-width", sidebarWidth);
    document.documentElement.style.setProperty(
      "--right-drawer-width",
      rightSidebarWidth,
    );
  }, [drawerOpen, rightDrawerOpen]);

  return (
    <div className="mainInner">
      <LeftDrawer
        drawerOpen={drawerOpen}
        toggleDrawer={(
          isOpen,
          localStorageKey,
          cssVarOpenWidth,
          cssVarClosedWidth,
        )=>(
          toggleDrawer(
            isOpen,
            localStorageKey,
            cssVarOpenWidth,
            cssVarClosedWidth,
            setDrawerOpen,
          )
        )}
      />
      <div className={`${drawerOpen ? "open" : "closed"} chat-item-wrapper`}>
        {children}
      </div>
      <RightDrawer
        rightDrawerOpen={rightDrawerOpen}
        toggleDrawer={(
          isOpen,
          localStorageKey,
          cssVarOpenWidth,
          cssVarClosedWidth,
        )=>(
          toggleDrawer(
            isOpen,
            localStorageKey,
            cssVarOpenWidth,
            cssVarClosedWidth,
            setRightDrawerOpen,
          )
        )}
      />
    </div>
  );
}

