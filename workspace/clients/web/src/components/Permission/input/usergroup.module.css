@import '../../../../public/variables.css';


.chatSettingsLines {
  padding: 12px;
    margin: 10px;
    border: 1px solid rgb(105 116 140 / 19%);
    border-radius: 5px;

  .preWrap {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.btnPadding {
  padding: 8px 0;
}

li {
  list-style: none;
}

.chatSettings {
  word-wrap: normal;
}

.groupAutoComplete {
  input {
    color: var(--divi-dark-purp);
  }
  label {
    color: var(--divi-light-purp);
  }
  fieldset {
    border: 2px solid var(--divi-light-purp);
  }
}

.inviteeEmail {
  display: flex;
  align-items: center;
  gap: 23px;
}

.inviteLinkInput {
  width: 100% !important;
}

.inviteeEmailLinkBox {
  display: flex;
  gap: 22px;
}

@media (max-width: 768px) {
  .mobileScrollContainer {
    overflow-x: auto;
    width: 100%;
    box-sizing: unset;
  }

  .docPermissionContext .section {
    padding: 0;

  }

  * {
    box-sizing: unset;
  }

  h1 {
    font-size: 1rem;
  }

  .mobileView .titleSizing {
    font-size: large;
    padding: 8px 0;
  }

  .inviteeEmailLinkBox {
    display: block;
    padding: 1px;
  }
}