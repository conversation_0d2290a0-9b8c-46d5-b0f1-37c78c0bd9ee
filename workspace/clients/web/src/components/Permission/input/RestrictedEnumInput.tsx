import React, { useState } from "react";
import { InputProps } from "../../../util/react/input";
import { JSON_Unknown } from "@divinci-ai/utils";
import { useFetcher } from "../../../util/react/fetcher";
import styles from "./usergroup.module.css";

type DescriptionMap = {
  [key: string]: {
    title: string,
    description: string,
  },
};

export function RestrictedEnumInput({
  value,
  onChange,
  disabled,
  descriptions,
  restriction: restrictionList,
}: {
  disabled?: boolean,
  descriptions: DescriptionMap,
  restriction: Array<string>,
} & InputProps<Array<string>>){
  return (
    <ul className={`${styles.chatSettings}`}>
      {restrictionList.map((restrictedItem) => {
        const valueIndex = value.indexOf(restrictedItem);
        const info = descriptions[restrictedItem];
        return (
          <li
            key={restrictedItem}
            className={styles.chatSettingsLines}
          >
            <div>
              <input
                className="checkbox"
                type="checkbox"
                disabled={!!disabled}
                checked={valueIndex > -1}
                onChange={()=>{
                  if(valueIndex === -1){
                    onChange(value.concat(restrictedItem));
                  } else {
                    const newValue = [...value];
                    newValue.splice(valueIndex, 1);
                    onChange(newValue);
                  }
                }}
              />
              <span>{info.title}</span>
            </div>
            <pre className={`${styles.preWrap}`}>{info.description}</pre>
          </li>
        );
      })}
    </ul>
  );
}

export function AsyncUpdateRestrictedEnumInput({
  url,
  value,
  onChange,
  prepareNewValue,
  descriptions,
  restriction,
}: {
  url: string,
  prepareNewValue?: (oldValue: Array<string>) => JSON_Unknown,
  descriptions: DescriptionMap,
  restriction: Array<string>,
} & InputProps<Array<string>>){
  const fetcher = useFetcher();
  const [loading, setLoading] = useState(false);

  return (
    <RestrictedEnumInput
      value={value}
      disabled={loading}
      onChange={handleChange}
      descriptions={descriptions}
      restriction={restriction}
    />
  );

  async function handleChange(newValue: Array<string>){
    setLoading(true);
    try {
      const toSend = prepareNewValue ? prepareNewValue(newValue) : newValue;

      await fetcher(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(toSend),
      });

      await onChange(newValue);
    }catch(e){
      console.error(e);
    }

    setLoading(false);
  }
}
