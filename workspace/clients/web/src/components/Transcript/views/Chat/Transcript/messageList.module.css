@import "@styles/variables.css";

:root {
  --font-size-small: 9px;
  --border-radius-bottom: 7px;
  --notice-height: 19px;
  --notice-icon: 14px;
}

.messageFormParent {
  background: var(--main-header-gradient-mid);
  padding: 6px 0;
  position: relative;
  z-index: 2;
}

.privacyNoticed {
  font-size: var(--font-size-small);
  justify-content: center;
  display: flex;
  padding-right: 2px;
}

.aiNotice {
  color: var(--text-color);
  position: absolute;
  font-size: var(--font-size-small);
  display: block;
  bottom: 2px;
  left: 7px;
}

.aiNoticeWrapper {
  position: relative;
  display: block;
  background: grey;
  width: calc(100% - 2px);
  height: var(--notice-height);
  margin-top: calc((var(--notice-height) * -1) - 1px);
  border-bottom-left-radius: var(--border-radius-bottom);
  border-bottom-right-radius: var(--border-radius-bottom);
  background: linear-gradient(
    180deg,
    var(--main-header-gradient-mid) 100%,
    var(--main-header-gradient-start) 0%
  );
  z-index: 4;
  margin-left: 1px;
}

.aiMistakesNotice {
  opacity: 0.65;
  font-size: 8px;
  border-radius: 50%;
  border: 1px solid grey;
  height: var(--notice-icon);
  width: var(--notice-icon);
  text-align: center;
  display: flex !important;
  justify-content: center;
  align-items: center;
  margin: 0 3px -3px 0;
}
.chatInputTextarea {
  z-index: 2;
  position: relative;
}
.chatInputTextarea textarea {
  flex-grow: 1;
  resize: none;
  z-index: 3;
}

.speechOptionsParent {
  display: flex;
  flex-direction: row;
}

.optionsPanel {
  align-items: baseline;
}


@media (max-width: 768px) {
  .aiNoticeWrapper {
    position: fixed;
    display: block;
    width: 100%;
    height: var(--notice-height);
    bottom: 24%; /* Increased to position it well above the chat input box */
    left: 0;
    border-bottom-left-radius: var(--border-radius-bottom);
    border-bottom-right-radius: var(--border-radius-bottom);
    background: linear-gradient(
      180deg,
      var(--main-header-gradient-mid) 100%,
      var(--main-header-gradient-start) 0%
    );
    z-index: 9; /* Lower than chat-input-form's z-index of 10 */
    margin: 0;
  }

  .aiNotice {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    width: 100%;
    text-align: center;
  }

  .privacyNoticed {
    position: static;
    top: 27px;
    z-index: 999; /* Ensures it appears above other elements */
  }
}

@media (max-width: 480px) {
  .aiNoticeWrapper {
    position: fixed;
    display: block;
    width: 100%;
    height: var(--notice-height);
    bottom: 20%;
    left: 0;
    border-bottom-left-radius: var(--border-radius-bottom);
    border-bottom-right-radius: var(--border-radius-bottom);
    background: linear-gradient(
      180deg,
      var(--main-header-gradient-mid) 100%,
      var(--main-header-gradient-start) 0%
    );
    z-index: 99; /* Lower than chat-input-form's z-index of 10 */
    margin: 0;
  }

  .aiNotice {
    position: absolute;
    bottom: 2px;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    width: 100%;
    text-align: center;
    font-size: 8px; /* Slightly smaller font for very small screens */
  }

  .privacyNoticed {
    position: static;
    top: 27px;
    z-index: 999; /* Ensures it appears above other elements */
  }
}