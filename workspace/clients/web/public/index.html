<!doctype html>
<html lang="en">
  <head>
    <!-- Character set for proper encoding -->
    <meta charset="utf-8" />

    <!-- Viewport meta tag for responsive design -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />

    <!-- Title for the web app -->
    <title>Divinci AI</title>

    <!-- Favicon -->
    <link rel="icon" href="/favicon.ico" />

    <!-- Web App Manifest for Progressive Web Apps (PWA) -->
    <!-- <link rel="manifest" href="/manifest.json" /> -->

    <!-- Meta tags for SEO -->
    <meta
      name="description"
      content="Platform for fully managed, tested, custom AIs"
    />
    <meta name="keywords" content="RAG, AI, LLM, Fine-Tune, Chat" />
    <meta name="author" content="Divinci AI, Inc." />

    <!-- Open Graph for Social Media Sharing -->
    <meta
      property="og:title"
      content="Platform for fully managed, tested, custom AIs"
    />
    <meta
      property="og:description"
      content="Platform for fully managed, tested, custom AIs"
    />
    <meta property="og:image" content="/img/divinci_logo.png" />
    <meta property="og:url" content="https://chat.divinci.app" />

    <meta name="twitter:title" content="Divinci AI" />
    <meta
      name="twitter:description"
      content="Platform for fully managed, tested, custom AIs"
    />
    <meta name="twitter:image" content="/img/divinci_logo.png" />
  <script defer src="/hidden.build.js"></script></head>
  <body>
    <!-- Root element for React app -->
    <div id="init"></div>

    <!-- Accessibility improvement: no-js message -->
    <noscript> You need to enable JavaScript to run this app. </noscript>
  </body>
</html>
