{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    // Add test-specific TypeScript settings
    "types": [
      "node", 
      "@cloudflare/workers-types", 
      "vitest/globals"
    ],
    // Enable more strict checks for tests
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    // Allow importing JSON files in tests (useful for fixtures)
    "resolveJsonModule": true,
    // Set baseUrl for absolute imports
    "baseUrl": "."
  },
  // Include both source and test files
  "include": [
    "src/**/*", 
    "tests/**/*"
  ],
  // But exclude test utilities from type checking
  "exclude": [
    "node_modules",
    "dist",
    "tests/helpers/**/*"
  ]
}