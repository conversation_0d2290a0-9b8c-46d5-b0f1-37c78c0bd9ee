import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import tsconfigPaths from 'vite-tsconfig-paths';

/**
 * Vitest configuration with hybrid approach:
 * 1. Use path aliases directly from tsconfig.json via tsconfigPaths plugin
 * 2. Add additional path aliases here for more explicit control
 * 3. Configure test environment with globals for better DX
 */
export default defineConfig({
  plugins: [
    // Use path mappings from tsconfig.json
    tsconfigPaths(),
  ],
  test: {
    globals: true,
    environment: 'node',
    include: ['tests/**/*.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/', 'tests/'],
    },
    // Environment variables needed for tests
    environmentOptions: {
      // Add any test-specific env vars here
    },
  },
  // Fallback path aliases in case the plugin doesn't pick them up correctly
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/stream': resolve(__dirname, './src/stream'),
      '@/utils': resolve(__dirname, './src/utils'),
    },
  },
});
