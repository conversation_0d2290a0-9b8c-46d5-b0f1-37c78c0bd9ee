import { describe, it, expect, vi, beforeEach } from 'vitest';

// Import the module dynamically to avoid issues with missing implementations
let utilModule: any;

// Use beforeAll to import the module once before all tests
beforeEach(async () => {
  utilModule = await import('@/stream/util');
});

describe('getTableName', () => {
  it('should convert whitelabel ID to table name format', () => {
    // Test with a simple ID
    expect(utilModule.getTableName('test')).toBe('d1_chunks_test');

    // Test with hyphens
    expect(utilModule.getTableName('test-id')).toBe('d1_chunks_test_id');

    // Test with multiple hyphens
    expect(utilModule.getTableName('test-id-123')).toBe('d1_chunks_test_id_123');

    // Test with UUID format
    expect(utilModule.getTableName('12345678-1234-1234-1234-123456789012')).toBe(
      'd1_chunks_12345678_1234_1234_1234_123456789012'
    );
  });
});

describe('ensureTableExists', () => {
  let mockContext: any;
  let mockRun: any;
  let mockPrepare: any;

  beforeEach(() => {
    mockRun = vi.fn().mockResolvedValue({});
    mockPrepare = vi.fn().mockReturnValue({ run: mockRun });

    mockContext = {
      env: {
        DB: {
          prepare: mockPrepare
        }
      }
    };
  });

  it('should create table if it does not exist', async () => {
    const whitelabelId = 'test-id';
    const tableName = 'd1_chunks_test_id';

    const result = await utilModule.ensureTableExists(mockContext, whitelabelId);

    // Check that the function returns the correct table name
    expect(result).toBe(tableName);

    // Check that prepare was called twice (once for table creation, once for index)
    expect(mockPrepare).toHaveBeenCalledTimes(2);

    // Check that the first call creates the table
    expect(mockPrepare.mock.calls[0][0]).toContain(`CREATE TABLE IF NOT EXISTS ${tableName}`);

    // Check that the second call creates the index
    expect(mockPrepare.mock.calls[1][0]).toContain(`CREATE INDEX IF NOT EXISTS idx_${tableName}_metadata`);

    // Check that run was called twice
    expect(mockRun).toHaveBeenCalledTimes(2);
  });

  it('should handle errors during table creation', async () => {
    const whitelabelId = 'test-id';
    const error = new Error('Database error');

    mockRun.mockRejectedValueOnce(error);

    await expect(utilModule.ensureTableExists(mockContext, whitelabelId)).rejects.toThrow(error);

    // Check that prepare was called once
    expect(mockPrepare).toHaveBeenCalledTimes(1);

    // Check that run was called once
    expect(mockRun).toHaveBeenCalledTimes(1);
  });
});

describe('validateRequest', () => {
  let mockContext: any;

  beforeEach(() => {
    mockContext = {
      env: {
        ENVIRONMENT: 'production',
        DB: {},
        CLOUDFLARE_WORKER_X_AUTH_DEV: 'test-auth-token'
      },
      req: {
        raw: {
          headers: {
            get: vi.fn()
          }
        }
      }
    };
  });

  it('should return true for production environment', () => {
    mockContext.env.ENVIRONMENT = 'production';

    const result = utilModule.validateRequest(mockContext);

    expect(result).toBe(true);
  });

  it('should validate dev environment with correct auth header', () => {
    mockContext.env.ENVIRONMENT = 'dev';
    mockContext.req.raw.headers.get.mockReturnValueOnce('test-auth-token');

    const result = utilModule.validateRequest(mockContext);

    expect(result).toBe(true);
    expect(mockContext.req.raw.headers.get).toHaveBeenCalledWith('Cloudflare-Worker-X-Dev-Auth');
  });

  it('should return undefined (falsy) for dev environment with incorrect auth header', () => {
    mockContext.env.ENVIRONMENT = 'dev';
    mockContext.req.raw.headers.get.mockReturnValueOnce('wrong-token');

    const result = utilModule.validateRequest(mockContext);

    expect(result).toBeFalsy();
    expect(mockContext.req.raw.headers.get).toHaveBeenCalledWith('Cloudflare-Worker-X-Dev-Auth');
  });
});
