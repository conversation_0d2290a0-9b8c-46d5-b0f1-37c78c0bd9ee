import { describe, it, expect, vi, beforeEach } from 'vitest';
import { deleteChunk, deleteChunkWithIds, deleteChunks } from '@/stream/delete';

// Tests for deleteChunk function
describe('deleteChunk', () => {
  let mockContext: any;
  let mockRun: any;
  let mockBind: any;
  let mockPrepare: any;
  let mockJson: any;
  
  beforeEach(() => {
    mockRun = vi.fn().mockResolvedValue({ success: true });
    mockBind = vi.fn().mockReturnValue({ run: mockRun });
    mockPrepare = vi.fn().mockReturnValue({ bind: mockBind });
    mockJson = vi.fn().mockReturnValue({});
    
    mockContext = {
      env: {
        DB: {
          prepare: mockPrepare
        }
      },
      req: {
        json: vi.fn()
      },
      json: mockJson
    };
  });
  
  it('should return 400 if id is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunk(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: id and whitelabelId",
      400
    );
  });
  
  it('should return 400 if whitelabelId is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      id: 'test-id'
    });
    
    await deleteChunk(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: id and whitelabelId",
      400
    );
  });
  
  it('should delete the chunk with the specified id', async () => {
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
    
    mockContext.req.json.mockResolvedValueOnce({
      id: 'test-id',
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunk(mockContext);
    
    // Check that prepare was called with the correct SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      'DELETE FROM d1_chunks_test_whitelabel WHERE id = ?'
    );
    
    // Check that bind was called with the id
    expect(mockBind).toHaveBeenCalledWith('test-id');
    
    // Check that run was called
    expect(mockRun).toHaveBeenCalled();
    
    // Check that the success response is returned
    expect(mockJson).toHaveBeenCalledWith(
      "🗑️ Record deleted",
      200
    );
  });
  
  it('should handle database errors', async () => {
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
    
    mockContext.req.json.mockResolvedValueOnce({
      id: 'test-id',
      whitelabelId: 'test-whitelabel'
    });
    
    // Simulate a database error
    const dbError = new Error('Database error');
    mockRun.mockRejectedValueOnce(dbError);
    
    await deleteChunk(mockContext);
    
    // In the current implementation, errors are not caught and handled
    // If we were to update the implementation, we would test the error handling here
  });
});

// Tests for deleteChunkWithIds function
describe('deleteChunkWithIds', () => {
  let mockContext: any;
  let mockRun: any;
  let mockPrepare: any;
  let mockJson: any;
  
  beforeEach(() => {
    mockRun = vi.fn().mockResolvedValue({ success: true });
    mockPrepare = vi.fn().mockReturnValue({ run: mockRun });
    mockJson = vi.fn().mockReturnValue({});
    
    mockContext = {
      env: {
        DB: {
          prepare: mockPrepare
        }
      },
      req: {
        json: vi.fn()
      },
      json: mockJson
    };
  });
  
  it('should return 400 if ids is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunkWithIds(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: ids and whitelabelId",
      400
    );
  });
  
  it('should return 400 if whitelabelId is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      ids: ['id1', 'id2']
    });
    
    await deleteChunkWithIds(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: ids and whitelabelId",
      400
    );
  });
  
  it('should return 400 if ids is not an array', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      ids: 'not-an-array',
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunkWithIds(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Expected Array List for ids",
      400
    );
  });
  
  it('should return 200 if ids array is empty', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      ids: [],
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunkWithIds(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "🗑️ No Items deleted",
      200
    );
  });
  
  it('should return 400 if an id in the array is not a string', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      ids: ['id1', 123, 'id3'],
      whitelabelId: 'test-whitelabel'
    });
    
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
    
    await deleteChunkWithIds(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Expected String at index 1",
      400
    );
  });
  
  it('should delete chunks with the specified ids', async () => {
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
    
    mockContext.req.json.mockResolvedValueOnce({
      ids: ['id1', 'id2', 'id3'],
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunkWithIds(mockContext);
    
    // Check that prepare was called with the correct SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      "DELETE FROM d1_chunks_test_whitelabel WHERE id IN ('id1','id2','id3')"
    );
    
    // Check that run was called
    expect(mockRun).toHaveBeenCalled();
    
    // Check that the success response is returned
    expect(mockJson).toHaveBeenCalledWith(
      "🗑️ Records deleted", 
      200
    );
  });
  
  it('should properly escape single quotes in ids', async () => {
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
    
    mockContext.req.json.mockResolvedValueOnce({
      ids: ["id1", "O'Reilly", "id3"],
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunkWithIds(mockContext);
    
    // Check that prepare was called with properly escaped SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      "DELETE FROM d1_chunks_test_whitelabel WHERE id IN ('id1','O\\'Reilly','id3')"
    );
  });
});

// Tests for deleteChunks function
describe('deleteChunks', () => {
  let mockContext: any;
  let mockRun: any;
  let mockPrepare: any;
  let mockJson: any;
  
  beforeEach(() => {
    mockRun = vi.fn().mockResolvedValue({ success: true });
    mockPrepare = vi.fn().mockReturnValue({ run: mockRun });
    mockJson = vi.fn().mockReturnValue({});
    
    mockContext = {
      env: {
        DB: {
          prepare: mockPrepare
        }
      },
      req: {
        json: vi.fn()
      },
      json: mockJson
    };
    
    // Mock the ensureTableExists function
    vi.mock('@/stream/util', () => ({
      ensureTableExists: vi.fn().mockResolvedValue('d1_chunks_test_whitelabel')
    }));
  });
  
  it('should return 400 if fileId is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      length: 5,
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunks(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: fileId, length, and whitelabelId",
      400
    );
  });
  
  it('should return 400 if length is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      fileId: 'test-file',
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunks(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: fileId, length, and whitelabelId",
      400
    );
  });
  
  it('should return 400 if whitelabelId is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      fileId: 'test-file',
      length: 5
    });
    
    await deleteChunks(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      "Missing required fields: fileId, length, and whitelabelId",
      400
    );
  });
  
  it('should delete chunks with the specified fileId and length', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      fileId: 'test-file',
      length: 3,
      whitelabelId: 'test-whitelabel'
    });
    
    await deleteChunks(mockContext);
    
    // Check that prepare was called with the correct SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      "DELETE FROM d1_chunks_test_whitelabel WHERE id IN ('test-file-0','test-file-1','test-file-2')"
    );
    
    // Check that run was called
    expect(mockRun).toHaveBeenCalled();
    
    // Check that the success response is returned
    expect(mockJson).toHaveBeenCalledWith(
      "🗑️ Records deleted",
      200
    );
  });
  
  it('should handle database errors', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      fileId: 'test-file',
      length: 3,
      whitelabelId: 'test-whitelabel'
    });
    
    // Simulate a database error
    const dbError = new Error('Database error');
    mockRun.mockRejectedValueOnce(dbError);
    
    await deleteChunks(mockContext);
    
    // In the current implementation, errors are not caught and handled
    // If we were to update the implementation, we would test the error handling here
  });
});