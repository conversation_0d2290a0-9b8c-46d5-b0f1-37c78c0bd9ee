import { describe, it, expect } from 'vitest';
import { buildFilterQuery } from '@/stream/filter';

describe('buildFilterQuery', () => {
  it('should build a query with a single filter condition', () => {
    const filter = { docId: 'doc-1' };

    const result = buildFilterQuery(filter);

    expect(result.query).toBe("json_extract(metadata, '$.docId') = ?");
    expect(result.params).toEqual(['doc-1']);
  });

  it('should build a query with multiple filter conditions', () => {
    const filter = {
      docId: 'doc-1',
      type: 'text',
      status: 'active'
    };

    const result = buildFilterQuery(filter);

    expect(result.query).toBe(
      "json_extract(metadata, '$.docId') = ? AND json_extract(metadata, '$.type') = ? AND json_extract(metadata, '$.status') = ?"
    );
    expect(result.params).toEqual(['doc-1', 'text', 'active']);
  });

  it('should handle empty filter object', () => {
    const filter = {};

    const result = buildFilterQuery(filter);

    expect(result.query).toBe('');
    expect(result.params).toEqual([]);
  });

  it('should handle null values in filter', () => {
    const filter = {
      docId: 'doc-1',
      type: null
    };

    const result = buildFilterQuery(filter);

    expect(result.query).toBe(
      "json_extract(metadata, '$.docId') = ? AND json_extract(metadata, '$.type') IS NULL"
    );
    expect(result.params).toEqual(['doc-1']);
  });

  it('should handle undefined values in filter', () => {
    const filter = {
      docId: 'doc-1',
      type: undefined
    };

    const result = buildFilterQuery(filter);

    // Undefined values should be ignored
    expect(result.query).toBe("json_extract(metadata, '$.docId') = ?");
    expect(result.params).toEqual(['doc-1']);
  });

  it('should handle boolean values in filter', () => {
    const filter = {
      docId: 'doc-1',
      isActive: true,
      isDeleted: false
    };

    const result = buildFilterQuery(filter);

    expect(result.query).toBe(
      "json_extract(metadata, '$.docId') = ? AND json_extract(metadata, '$.isActive') = ? AND json_extract(metadata, '$.isDeleted') = ?"
    );
    expect(result.params).toEqual(['doc-1', true, false]);
  });

  it('should handle number values in filter', () => {
    const filter = {
      docId: 'doc-1',
      count: 5,
      score: 3.14
    };

    const result = buildFilterQuery(filter);

    expect(result.query).toBe(
      "json_extract(metadata, '$.docId') = ? AND json_extract(metadata, '$.count') = ? AND json_extract(metadata, '$.score') = ?"
    );
    expect(result.params).toEqual(['doc-1', 5, 3.14]);
  });
});
