import { describe, it, expect, vi, beforeEach } from 'vitest';
import { retrieveChunks } from '@/stream/retrieve';

describe('retrieveChunks', () => {
  let mockContext: any;
  let mockAll: any;
  let mockBind: any;
  let mockPrepare: any;
  let mockJson: any;
  
  beforeEach(() => {
    mockAll = vi.fn().mockResolvedValue([]);
    mockBind = vi.fn().mockReturnValue({ all: mockAll });
    mockPrepare = vi.fn().mockReturnValue({ bind: mockBind });
    mockJson = vi.fn().mockReturnValue({});
    
    mockContext = {
      env: {
        DB: {
          prepare: mockPrepare
        }
      },
      req: {
        json: vi.fn()
      },
      json: mockJson
    };
  });
  
  it('should return 400 if table is missing', async () => {
    mockContext.req.json.mockResolvedValueOnce({});
    
    await retrieveChunks(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        error: 'Missing required field: table'
      }),
      400
    );
  });
  
  it('should return 400 if neither ids nor filter is provided', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      table: 'test_table'
    });
    
    await retrieveChunks(mockContext);
    
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        error: 'Must provide either ids or filter'
      }),
      400
    );
  });
  
  it('should retrieve chunks by ids', async () => {
    const mockChunks = [
      {
        id: 'chunk-1',
        text: 'Test text 1',
        metadata: JSON.stringify({ key: 'value1' })
      },
      {
        id: 'chunk-2',
        text: 'Test text 2',
        metadata: JSON.stringify({ key: 'value2' })
      }
    ];
    
    mockAll.mockResolvedValueOnce(mockChunks);
    
    mockContext.req.json.mockResolvedValueOnce({
      table: 'test_table',
      ids: ['chunk-1', 'chunk-2']
    });
    
    await retrieveChunks(mockContext);
    
    // Check that prepare was called with the correct SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM test_table WHERE id IN')
    );
    
    // Check that bind was called with the correct parameters
    expect(mockBind).toHaveBeenCalledWith('chunk-1', 'chunk-2');
    
    // Check that all was called
    expect(mockAll).toHaveBeenCalled();
    
    // Check that the response contains the chunks with parsed metadata
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      chunks: [
        {
          id: 'chunk-1',
          text: 'Test text 1',
          metadata: { key: 'value1' }
        },
        {
          id: 'chunk-2',
          text: 'Test text 2',
          metadata: { key: 'value2' }
        }
      ]
    });
  });
  
  it('should retrieve chunks by filter', async () => {
    const mockChunks = [
      {
        id: 'chunk-1',
        text: 'Test text 1',
        metadata: JSON.stringify({ key: 'value1', docId: 'doc-1' })
      }
    ];
    
    mockAll.mockResolvedValueOnce(mockChunks);
    
    mockContext.req.json.mockResolvedValueOnce({
      table: 'test_table',
      filter: {
        docId: 'doc-1'
      }
    });
    
    await retrieveChunks(mockContext);
    
    // Check that prepare was called with the correct SQL
    expect(mockPrepare).toHaveBeenCalledWith(
      expect.stringContaining('SELECT * FROM test_table WHERE json_extract(metadata, \'$.docId\') = ?')
    );
    
    // Check that bind was called with the correct parameters
    expect(mockBind).toHaveBeenCalledWith('doc-1');
    
    // Check that all was called
    expect(mockAll).toHaveBeenCalled();
    
    // Check that the response contains the chunks with parsed metadata
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      chunks: [
        {
          id: 'chunk-1',
          text: 'Test text 1',
          metadata: { key: 'value1', docId: 'doc-1' }
        }
      ]
    });
  });
  
  it('should handle multiple filter conditions', async () => {
    mockAll.mockResolvedValueOnce([]);
    
    mockContext.req.json.mockResolvedValueOnce({
      table: 'test_table',
      filter: {
        docId: 'doc-1',
        type: 'text',
        status: 'active'
      }
    });
    
    await retrieveChunks(mockContext);
    
    // Check that prepare was called with the correct SQL containing multiple conditions
    expect(mockPrepare).toHaveBeenCalledWith(
      expect.stringMatching(/json_extract\(metadata, '\$\.docId'\) = \? AND json_extract\(metadata, '\$\.type'\) = \? AND json_extract\(metadata, '\$\.status'\) = \?/)
    );
    
    // Check that bind was called with all filter values
    expect(mockBind).toHaveBeenCalledWith('doc-1', 'text', 'active');
  });
  
  it('should handle database errors', async () => {
    mockContext.req.json.mockResolvedValueOnce({
      table: 'test_table',
      ids: ['chunk-1']
    });
    
    // Simulate a database error
    const dbError = new Error('Database error');
    mockAll.mockRejectedValueOnce(dbError);
    
    await retrieveChunks(mockContext);
    
    // Check that the error response is returned
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        error: expect.stringContaining('Failed to retrieve chunks')
      }),
      400
    );
  });
  
  it('should handle unexpected errors', async () => {
    // Simulate a JSON parsing error
    const parseError = new Error('JSON parse error');
    mockContext.req.json.mockRejectedValueOnce(parseError);
    
    await retrieveChunks(mockContext);
    
    // Check that the error response is returned
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        success: false,
        error: expect.stringContaining('Internal server error')
      }),
      500
    );
  });
});
