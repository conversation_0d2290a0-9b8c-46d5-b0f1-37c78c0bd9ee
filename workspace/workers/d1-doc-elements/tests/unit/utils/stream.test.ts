import { describe, it, expect, vi, beforeEach } from 'vitest';
import { streamToString, parseJsonStream } from '@/utils/stream';

describe('streamToString', () => {
  it('should convert a ReadableStream to a string', async () => {
    // Create a ReadableStream with some data
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        controller.enqueue(encoder.encode('Hello, '));
        controller.enqueue(encoder.encode('world!'));
        controller.close();
      }
    });
    
    const result = await streamToString(stream);
    
    expect(result).toBe('Hello, world!');
  });
  
  it('should handle empty streams', async () => {
    // Create an empty ReadableStream
    const stream = new ReadableStream({
      start(controller) {
        controller.close();
      }
    });
    
    const result = await streamToString(stream);
    
    expect(result).toBe('');
  });
  
  it('should handle streams with errors', async () => {
    // Create a ReadableStream that errors
    const stream = new ReadableStream({
      start(controller) {
        controller.error(new Error('Stream error'));
      }
    });
    
    await expect(streamToString(stream)).rejects.toThrow('Stream error');
  });
});

describe('parseJsonStream', () => {
  it('should parse a JSON stream into an object', async () => {
    // Create a ReadableStream with JSON data
    const encoder = new TextEncoder();
    const jsonData = JSON.stringify({ name: 'Test', value: 42 });
    const stream = new ReadableStream({
      start(controller) {
        controller.enqueue(encoder.encode(jsonData));
        controller.close();
      }
    });
    
    const result = await parseJsonStream(stream);
    
    expect(result).toEqual({ name: 'Test', value: 42 });
  });
  
  it('should handle empty streams', async () => {
    // Create an empty ReadableStream
    const stream = new ReadableStream({
      start(controller) {
        controller.close();
      }
    });
    
    await expect(parseJsonStream(stream)).rejects.toThrow('Unexpected end of JSON input');
  });
  
  it('should handle invalid JSON', async () => {
    // Create a ReadableStream with invalid JSON
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        controller.enqueue(encoder.encode('{ "name": "Test", value: 42 }')); // Missing quotes around value
        controller.close();
      }
    });
    
    await expect(parseJsonStream(stream)).rejects.toThrow();
  });
  
  it('should handle streams with errors', async () => {
    // Create a ReadableStream that errors
    const stream = new ReadableStream({
      start(controller) {
        controller.error(new Error('Stream error'));
      }
    });
    
    await expect(parseJsonStream(stream)).rejects.toThrow('Stream error');
  });
});
