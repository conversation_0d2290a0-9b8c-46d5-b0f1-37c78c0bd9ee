import { describe, it, expect, vi, beforeEach } from "vitest";

// Create a mock Hono app
const mockFetch = vi.fn();
vi.mock("@/stream", () => {
  return {
    default: {
      fetch: mockFetch
    }
  };
});

// Import the main module after mocking dependencies
import app from "@/index";

describe("Worker Entry Point", () => {
  let mockRequest;
  let mockEnv;
  let mockExecutionContext;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock request
    mockRequest = new Request("https://example.com/stream", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ test: "data" })
    });

    // Create mock environment
    mockEnv = {
      DB: {},
      ENVIRONMENT: "production",
      CLOUDFLARE_WORKER_X_AUTH_DEV: "test-auth-token"
    };

    // Create mock execution context
    mockExecutionContext = {
      waitUntil: vi.fn()
    };
  });

  it("should pass requests to the Hono app", async () => {
    // Mock the Hono app's fetch method to return a response
    const mockResponse = new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { "Content-Type": "application/json" }
    });
    mockFetch.mockResolvedValueOnce(mockResponse);

    // Call the fetch function
    const response = await app.fetch(mockRequest, mockEnv, mockExecutionContext);

    // Check that the Hono app's fetch method was called with the correct parameters
    expect(mockFetch).toHaveBeenCalledWith(mockRequest, mockEnv, mockExecutionContext);

    // Check that the response is correct
    expect(response).toBe(mockResponse);
  });

  it("should handle errors from the Hono app", async () => {
    // Mock the Hono app's fetch method to throw an error
    const error = new Error("Test error");
    mockFetch.mockRejectedValueOnce(error);

    // Call the fetch function and expect it to reject with the same error
    await expect(app.fetch(mockRequest, mockEnv, mockExecutionContext)).rejects.toThrow(error);
  });
});
