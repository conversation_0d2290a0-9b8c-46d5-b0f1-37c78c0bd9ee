# D1 Doc Elements Tests

This directory contains tests for the D1 Doc Elements worker.

## Directory Structure

- `unit/`: Unit tests for individual components
  - `stream/`: Tests for the stream API endpoints
  - `utils/`: Tests for utility functions

## Running Tests

To run all tests:

```bash
npm test
```

To run tests in watch mode:

```bash
npm run test:watch
```

To run tests with coverage:

```bash
npm run test:coverage
```

## Writing Tests

Tests are written using Vitest. Each test file should be named with the `.test.ts` extension.

Example:

```typescript
import { describe, it, expect, vi } from 'vitest';

describe('My Function', () => {
  it('should do something', () => {
    expect(true).toBe(true);
  });
});
```
