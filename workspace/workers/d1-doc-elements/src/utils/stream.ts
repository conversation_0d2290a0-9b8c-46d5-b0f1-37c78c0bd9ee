import { ReadableStream as WebReadableStream } from '@cloudflare/workers-types';
import { Readable } from 'stream';

export function convertWebReadableToReadable(webReadable: WebReadableStream): Readable {
  const reader = webReadable.getReader();

  return new Readable({
    async read() {
      try {
        const { done, value } = await reader.read();
        if (done) {
          this.push(null);
        } else {
          this.push(value);
        }
      } catch (error) {
        this.destroy(error as Error);
      }
    },
    destroy(error, callback) {
      reader.cancel().then(() => callback(error)).catch(callback);
    }
  });
}

/**
 * Converts a ReadableStream to a string.
 *
 * @param stream - The ReadableStream to convert
 * @returns A Promise that resolves to the string content of the stream
 */
export async function streamToString(stream: ReadableStream<Uint8Array>): Promise<string> {
  const reader = stream.getReader();
  const decoder = new TextDecoder();
  let result = '';

  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      result += decoder.decode(value, { stream: true });
    }
    // Final decode to flush any remaining content
    result += decoder.decode();
    return result;
  } catch (error) {
    throw error;
  } finally {
    reader.releaseLock();
  }
}

/**
 * Parses a ReadableStream containing JSON data into an object.
 *
 * @param stream - The ReadableStream containing JSON data
 * @returns A Promise that resolves to the parsed JSON object
 */
export async function parseJsonStream<T = any>(stream: ReadableStream<Uint8Array>): Promise<T> {
  const text = await streamToString(stream);
  return JSON.parse(text) as T;
}