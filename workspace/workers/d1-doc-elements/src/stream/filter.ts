/**
 * Builds a SQL query and parameters for filtering chunks based on metadata fields.
 * 
 * @param filter - An object containing key-value pairs to filter on
 * @returns An object with the query string and parameters array
 */
export function buildFilterQuery(filter: Record<string, any>): { query: string; params: any[] } {
  const conditions: string[] = [];
  const params: any[] = [];

  // Process each filter key-value pair
  Object.entries(filter).forEach(([key, value]) => {
    // Skip undefined values
    if (value === undefined) {
      return;
    }

    // Handle null values with IS NULL
    if (value === null) {
      conditions.push(`json_extract(metadata, '$.${key}') IS NULL`);
      return;
    }

    // Handle all other values with parameters
    conditions.push(`json_extract(metadata, '$.${key}') = ?`);
    params.push(value);
  });

  return {
    query: conditions.join(' AND '),
    params
  };
}
