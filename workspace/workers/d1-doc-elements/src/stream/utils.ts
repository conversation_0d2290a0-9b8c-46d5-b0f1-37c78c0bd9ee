import { Context } from 'hono';
import { Writable } from 'stream';

export class InputError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InputError';
  }
}

export function createDBWritable(c: Context, tableName: string): Writable {
  return new Writable({
    objectMode: true,
    write: async (chunk, encoding, callback) => {
      try {
        const { value } = chunk;
        const { id, text, metadata } = value;

        if (!id || !text) {
          callback(new InputError('Missing required fields: id or text'));
          return;
        }

        const sql = `INSERT OR REPLACE INTO ${tableName} (id, text, metadata) VALUES (?, ?, ?)`;
        await c.env.DB.prepare(sql)
          .bind(id, text, JSON.stringify(metadata))
          .run();

        callback();
      } catch (error) {
        callback(error as Error);
      }
    }
  });
}

export function waitForFinish(writable: Writable): Promise<void> {
  return new Promise((resolve, reject) => {
    writable.on('finish', resolve);
    writable.on('error', reject);
  });
}