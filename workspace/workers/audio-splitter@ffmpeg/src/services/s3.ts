import { S3 } from "@aws-sdk/client-s3";

import { requireEnvVar } from "../utils/env";
const CLOUDFLARE_AUDIO_ACCESS_KEY = requireEnvVar("CLOUDFLARE_AUDIO_ACCESS_KEY");
const CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY = requireEnvVar("CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY");
const CLOUDFLARE_AUDIO_S3 = requireEnvVar("CLOUDFLARE_AUDIO_S3");

export const s3 = new S3({
  endpoint: CLOUDFLARE_AUDIO_S3,
  credentials: {
    accessKeyId: CLOUDFLARE_AUDIO_ACCESS_KEY,
    secretAccessKey: CLOUDFLARE_AUDIO_SECRET_ACCESS_KEY,
  },
  region: "auto",
});



import { Readable } from "node:stream";

export async function getS3Readable(
  s3: S3, { Bucket, Key }: { Bucket: string, Key: string }
): Promise<Readable>{
  const abortController = new AbortController();
  const response = await s3.getObject(
    { Bucket, Key, },
    { abortSignal: abortController.signal }
  );
  if(!response.Body) {
    throw new Error("❌ No body in get checks response. ");
  }

  const stream = response.Body as Readable;

  let finishedNormally = false;

  // Listen for successful completion
  stream.on("end", ()=>finishedNormally = true);
  // Only abort if the stream did not finish normally
  stream.on("close", ()=>{
    if(!finishedNormally) abortController.abort();
  });
  stream.on("destroy", ()=>abortController.abort());

  return stream;
}


import { PassThrough, Writable } from "node:stream";
import { Upload } from "@aws-sdk/lib-storage";

const UPLOAD_FINISHED = Symbol("Upload Finished");
export class S3Writable extends Writable {
  public upload: Upload;
  private bodyStream = new PassThrough();
  private [UPLOAD_FINISHED] = false;
  private uploadPromise: Promise<any>;
  constructor(
    private r2: S3,
    public config: { Bucket: string, Key: string, ContentType: string, Metadata: Record<string, string> }
  ){
    super();
    this.upload = new Upload({
      client: this.r2,
      params: {
        ...config,
        Body: this.bodyStream,
      }
    });
    this.uploadPromise = this.upload.done().catch((e)=>{
      console.error("S3 Writable Upload Error:", e);
    });
  }

  _write(chunk: any, encoding: BufferEncoding, callback: (error?: Error | null) => void){
    this.bodyStream.write(chunk, encoding, callback);
  }

  async _final(callback: (error?: any) => void){
    try {
      this.bodyStream.end();
      this[UPLOAD_FINISHED] = true;
      await this.uploadPromise;
      callback();
    }catch(e){
      callback(e);
    }
  }

  async _destroy(error: Error | null, callback: (error?: Error | null) => void){
    if(this[UPLOAD_FINISHED]) return callback(error);
    try { await this.upload.abort(); }catch(e){ console.error("Ignore abort error"); }
    callback(error);
  }
}


