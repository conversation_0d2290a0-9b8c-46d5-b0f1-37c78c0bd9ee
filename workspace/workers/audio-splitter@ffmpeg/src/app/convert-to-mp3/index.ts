
import { s3, getS3Readable, S3Writable } from "../../services/s3";
import { parse as pathParse } from "node:path";
import { uniqueId } from "../../utils/unique";
import { spawnChild, streamToProcessToStream } from "../../utils/child-process";

import { HTTP_ERRORS_WITH_CONTEXT } from "../../utils/http-errors";

import { FFMPEG_SUPPORTED_INPUT_EXTENSIONS } from "./mime-types";
export * from "./mime-types";

export type ConvertMP3Args = {
  Bucket: string, Key: string,
};

export async function convertToMP3(
  { Bucket: sourceBucket, Key: sourceKey }: ConvertMP3Args,
  { Bucket: destinationBucket, Key: destinationKey }: ConvertMP3Args
){
  const originalFile = pathParse(sourceKey);
  if(!FFMPEG_SUPPORTED_INPUT_EXTENSIONS.has(
    originalFile.ext.slice(1).toLowerCase()
  )){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Unsupported file type");
  }
  const destinationFile = pathParse(destinationKey);


  const spawnArgs = [
    // Controls the output
    "-hide_banner",
    "-loglevel", "error",
    // Read input from stdin
    "-i", "pipe:0",
    // Disable video
    "-vn",
    // Set the audio codec
    "-acodec", "libmp3lame", "-f", "mp3",
    // High quality variable bitrate
    "-q:a", "2",
    // Make sure the header is written
    "-write_xing", "1",
    // Output to stdout
    "pipe:1"
  ];

  const ffmpeg = await spawnChild("ffmpeg", spawnArgs);
  const { stdin, stdout, stderr } = ffmpeg;
  if(!stdin || !stdout || !stderr){
    ffmpeg.kill();
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Failed to spawn ffmpeg");
  }

  const source = await getS3Readable(s3, {
    Bucket: sourceBucket, Key: sourceKey
  });


  const destination = new S3Writable(s3, {
    Bucket: destinationBucket,
    Key: `${destinationFile.dir ? destinationFile.dir + "/" : ""}${uniqueId()}.mp3`,
    ContentType: "audio/mpeg",
    Metadata: {}
  });

  try {
    await streamToProcessToStream(
      source, ffmpeg, destination
    );
    return {
      Bucket: destination.config.Bucket,
      Key: destination.config.Key
    };
  }catch(e){
    console.error("Failed to extract audio:", e);
    throw e;
  }
}

import {
  FFMPEG_SUPPORTED_VIDEO_EXTENSIONS,
  FFMPEG_SUPPORTED_AUDIO_EXTENSIONS,
} from "./mime-types";
export type SupportedFileArgs = {
  filename: string,
};

export function canHandleFilename({ filename }: SupportedFileArgs){
  const originalFile = pathParse(filename);
  const ext = originalFile.ext.slice(1).toLowerCase();
  if(FFMPEG_SUPPORTED_VIDEO_EXTENSIONS.has(ext)){
    return { support: true, type: "video" };
  }
  if(FFMPEG_SUPPORTED_AUDIO_EXTENSIONS.has(ext)){
    return { support: true, type: "audio" };
  }
  return { support: false, type: "unknown" };
}
