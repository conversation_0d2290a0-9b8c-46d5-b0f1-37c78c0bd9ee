import { ChildProcess, spawn } from "node:child_process";
import { s3, getS3Readable } from "../../services/s3";
import { pipeline } from "node:stream/promises";
import { collectOutputHandleExit } from "../../utils/child-process";
import { Readable } from "node:stream";
import { HTTP_ERRORS_WITH_CONTEXT } from "../../utils/http-errors";

export type AudioDurationArgs = { Bucket: string, Key: string };

// Spawn the ffprobe process; the "-i -" tells ffprobe to read from stdin
// ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 -i -
// ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 pipe:0
// curl --silent --no-buffer --output - INPUT_URL


/**
 * Get the duration (in seconds) of an audio file by piping its contents
 * to ffprobe via stdin.
 *
 * @param {string} filePath
 * @returns {Promise<number>}
 */
export async function getAudioDuration(
  { Buck<PERSON>, Key }: AudioDurationArgs
): Promise<number>{
  const source = await getS3Readable(s3, { Bucket, Key });

  const ffprobe = spawn("ffprobe", [
    "-v", "error",
    "-show_entries", "format=duration",
    "-of", "default=noprint_wrappers=1:nokey=1",
    "pipe:0",
  ]);

  let durationStr = await handleFFProbe(source, ffprobe);

  durationStr = durationStr.trim();

  if(durationStr === "N/A") {
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("ffprobe did not find a header on the file");
  }

  // Parse as float
  const duration = Number.parseFloat(durationStr);
  if(Number.isNaN(duration)){
    throw new Error(`ffprobe returned an invalid duration: ${durationStr}`);
  }

  return duration;
}

async function handleFFProbe(source: Readable, ffprobe: ChildProcess){
  try {
    if(!ffprobe.stdin){
      throw new Error("Failed to spawn ffprobe");
    }
    const [durationStr] = await Promise.all([
      collectOutputHandleExit(ffprobe),
      pipeline(source, ffprobe.stdin).catch((err)=>{
        switch(err.code){
          case "EPIPE":{
            return console.warn("FFmpeg closed stdin early, ignoring EPIPE.");
          }
          case "ERR_STREAM_PREMATURE_CLOSE": {
            return console.warn("FFmpeg closed stdin early, ignoring ERR_STREAM_PREMATURE_CLOSE.");
          }
          default: throw err;
        }
      }),
    ]);
    return durationStr;
  } finally {
    try { source.destroy(); }catch(e){ console.warn("Ignoring S3 source destroy Error", e); }
    try { ffprobe.kill(); }catch(e){ console.warn("Ignoring FFMpeg kill Error", e); }
  }
}
