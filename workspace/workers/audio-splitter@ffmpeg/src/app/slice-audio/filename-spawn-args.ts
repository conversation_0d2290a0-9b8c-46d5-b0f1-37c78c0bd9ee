import { lookup as mimeLookup } from "mime-types";
import { HTTP_ERRORS_WITH_CONTEXT } from "../../utils/http-errors";

const CODEC_MAP: Record<string, string> = {
  "audio/mp3": "libmp3lame",
  "audio/mpeg": "libmp3lame",  // MP3
  "audio/wav": "copy",         // WAV (no re-encoding)
  "audio/x-wav": "copy",       // Alternative MIME for WAV
  "audio/opus": "libopus",     // Opus encoding
  "audio/aac": "aac",          // AAC encoding
  "audio/x-aac": "aac",        // Alternative AAC MIME
  "audio/flac": "flac",        // FLAC encoding
  "audio/x-flac": "flac",      // Alternative FLAC MIME
};

const EXTRA_ARGS: Record<string, Array<string>> = {
  "libmp3lame": [
     // Set to high quality
    "-q:a", "2",
    // make sure the header is written
    "-write_xing", "1"
  ]
};

const OUTPUT_TYPE: Record<string, string> = {
  "libmp3lame": "mp3",
  "libopus": "libopus",
  "aac": "aac",
  "flac": "flac",
};

export function handleAudioCodec(filename: string){
  const contentType = mimeLookup(filename);
  if(!contentType){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Provided Key has no mimetype");
  }
  if(!(contentType in CODEC_MAP)){
    throw HTTP_ERRORS_WITH_CONTEXT.BAD_FORM("Provided file's mimetype cannot be processed");
  }

  const codec = CODEC_MAP[contentType];
  const args: Array<string> = [];
  args.push(...(EXTRA_ARGS[codec] || []));
  if(codec in OUTPUT_TYPE) args.push("-f", OUTPUT_TYPE[codec]);
  return {
    contentType,
    codec: codec,
    args
  };
}

import { parse as pathParse } from "node:path";
export function filenameSpawnArgs(
  { start, end, filename }: { start: number, end: number, filename: string }
){
  const { contentType, codec, args } = handleAudioCodec(filename);

  const spawnArgs = [
    "-i", "pipe:0", // Read input from stdin
    "-ss", start.toString(), // set start time
    "-to", end.toString(), // send end time
    // set the input and output
    "-c:a", codec,
    ...args,
    "pipe:1" // Output to stdout
  ];

  const originalFile = pathParse(filename);

  return {
    fileExtension: originalFile.ext,
    contentType,
    spawnArgs
  };
}
