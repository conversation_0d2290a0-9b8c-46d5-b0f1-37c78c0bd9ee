
import { s3, getS3Readable, S3Writable } from "../../services/s3";
import { parse as pathParse } from "node:path";
import { uniqueId } from "../../utils/unique";
import { spawnChild, streamToProcessToStream } from "../../utils/child-process";

import { HTTP_ERRORS_WITH_CONTEXT } from "../../utils/http-errors";
import { mp3SpawnArgs } from "./mp3-spawn-args";

export type SliceAudioArgs = {
  start: number, end: number, Bucket: string, Key: string,
};

export async function sliceAudio({ start, end, Bucket, Key }: SliceAudioArgs){

  const { spawnArgs, contentType, fileExtension } = mp3SpawnArgs({ start, end, filename: Key });

  const ffmpeg = await spawnChild("ffmpeg", spawnArgs);
  const { stdin, stdout, stderr } = ffmpeg;
  if(!stdin || !stdout || !stderr){
    ffmpeg.kill();
    throw HTTP_ERRORS_WITH_CONTEXT.SERVER_ERROR("Failed to spawn ffmpeg");
  }

  const source = await getS3Readable(s3, { Bucket, Key });

  const originalFile = pathParse(Key);

  const destination = new S3Writable(s3, {
    Bucket: Bucket,
    Key: `${originalFile.dir}/${originalFile.name}_${uniqueId()}${fileExtension}`,
    ContentType: contentType,
    Metadata: {}
  });

  try {
    await streamToProcessToStream(
      source, ffmpeg, destination
    );
    return { Bucket, Key: destination.config.Key };
  }catch(e){
    console.error("Failed to slice audio:", { start, end }, e);
    throw e;
  }
}


