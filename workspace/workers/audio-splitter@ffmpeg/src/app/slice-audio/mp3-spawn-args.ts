
export function mp3SpawnArgs(
  { start, end }: { start: number, end: number, filename: string }
){
  const spawnArgs = [
    // Controls the output
    "-hide_banner",
    "-loglevel", "error",
    // Read input from stdin
    "-i", "pipe:0",
    // Disable video
    "-vn",
    // set start time
    "-ss", start.toString(),
    // send end time
    "-to", end.toString(),
    // Set the audio codec
    "-acodec", "libmp3lame", "-f", "mp3",
    // Make sure the header is written
    "-write_xing", "1",
    // Output to stdout
    "pipe:1"
  ];

  return {
    fileExtension: ".mp3",
    contentType: "audio/mpeg",
    spawnArgs
  };
}
