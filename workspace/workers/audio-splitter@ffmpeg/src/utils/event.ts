import EventEmitter from "node:events";

type ListenerConfig<T> = { event: string, cb: (value: T)=>any };
export function setupListeners<T extends EventEmitter>(
  emitter: T, success: ListenerConfig<T>, fail: ListenerConfig<any>
){
  let sl: undefined | (()=>any);
  let el: undefined | ((e: any)=>any);
  emitter.on(success.event, sl = ()=>{cleanup();success.cb(emitter);});
  emitter.on(fail.event, el = (e)=>{cleanup();fail.cb(e);});

  function cleanup(){
    emitter.off(success.event, sl);
    emitter.off(fail.event, el);
  }
}
