import { resolve as pathResolve } from "path";
import {
  statSync as fsStat,
  readFileSync as fsReadFile,
  readdirSync as fsReadDir,
} from "fs";

function getEnvFolder(){
  if(typeof (global as any).__env === "string"){
    return (global as any).__env;
  }
  if(typeof process.env.ENV_FOLDER === "string"){
    return process.env.ENV_FOLDER;
  }
  throw new Error("❌ Need to set root with `setupEnv`.");
}

function debugLog(quiet: boolean, ...args: any[]){
  if(quiet) return;
  // eslint-disable-next-line no-console
  console.log(...args);
}

export function requireEnvVar(varname: string){
  const v = process.env[varname];
  if(typeof v !== "string"){
    throw new Error(`missing environment variable: "${varname}"`);
  }
  return v;
}

export function requireJSON(jsonFileName: string){
  const envPath = getEnvFolder();
  const credentials = pathResolve(envPath, "./credentials");
  const file_path = pathResolve(credentials, jsonFileName);
  fsStat(file_path);
  const contents = fsReadFile(file_path, { encoding: "utf-8" });
  return JSON.parse(contents);
}

import { config as dotEnvConfig } from "dotenv";

export function setupEnv(
  { envPath, client, quiet }: { envPath: string, client?: boolean, quiet?: boolean }
): { [key: string]: string }{
  quiet = !!quiet;
  client = !!client;
  (global as any).__env = envPath;

  debugLog(quiet, "set __env:", (global as any).__env);

  const stat = fsStat(envPath, { throwIfNoEntry: false }); // just checking if folder exists

  if(typeof stat === "undefined") {
    debugLog(quiet, "no env directory");
    return {};
  }

  if(!stat.isDirectory()) {
    debugLog(quiet, "no env directory");
    return {};
  }

  const isClient = /.*-client-.*/;
  const isShared = /\.shared\./;
  const isEnv = /.*\.env/;
  const files = fsReadDir(envPath);

  let totalEnvVars: { [key: string]: string } = {};

  files.forEach((file)=>{
    if(!isEnv.test(file)) return;

    // If the file is shared, we'll use it for both environments
    if(!isShared.test(file)){
      // If it's a clientside evirnoment, we want clientside files
      if(client && !isClient.test(file)) return;
      // else, we dont want clientside files
      if(!client && isClient.test(file)) return;
    }

    const filePath = pathResolve(envPath, file);
    const vars = dotEnvConfig({ path: filePath });

    if(vars && vars.parsed) {
      // Merge parsed variables
      totalEnvVars = { ...totalEnvVars, ...vars.parsed };

      // Update process.env
      for(const [key, value] of Object.entries(vars.parsed)) {
        if(typeof process.env[key] === "undefined") {
          process.env[key] = value;
        }
      }
    } else {
      console.warn(`⚠️ No variables parsed from ${filePath}`);
    }
  });

  return totalEnvVars;
}
