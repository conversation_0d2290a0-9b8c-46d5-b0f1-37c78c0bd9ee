import { ChildProcess, spawn } from "node:child_process";
import { Readable, Writable } from "node:stream";
import { pipeline } from "node:stream/promises";

export function spawnChild(processName: string, spawnArgs: Array<string>){
  return new Promise<ChildProcess>((res, rej)=>{
    const childProcess = spawn(processName, spawnArgs);

    let sl: (()=>any);
    let el: ((e: any)=>any);
    childProcess.on("spawn", sl = ()=>{cleanup();res(childProcess);});
    childProcess.on("error", el = (e)=>{cleanup();rej(e);});

    function cleanup(){
      childProcess.off("spawn", sl);
      childProcess.off("error", el);
    }
  });
}

export function handleBadExit(child: ChildProcess){
  const stderr = child.stderr;
  if(!stderr){
    throw new Error("Missing required stderr stream");
  }
  const { promise, resolve, reject } = Promise.withResolvers<void>();

  // Capture errors from stderr
  let errorMessage = "";
  stderr.on("data", (data)=>{
    errorMessage += data.toString();
  });

  child.on("close", (code)=>{
    if(code === 0) return resolve();
    reject(new Error(
      `Process failed with code ${code}:\n${errorMessage.trim()}`
    ));
  });

  return promise;
}

export function collectOutputHandleExit(child: ChildProcess){
  if(!child.stdout){
    throw new Error("Missing required stdout stream");
  }
  if(!child.stderr){
    throw new Error("Missing required stderr stream");
  }
  const { promise, resolve, reject } = Promise.withResolvers<string>();
  let stdoutData = "";
  let stderrData = "";

  // Collect ffprobe's standard output
  child.stdout.on("data", (chunk)=>{
    stdoutData += chunk;
  });

  // Collect ffprobe's error output (if any)
  child.stderr.on("data", (chunk)=>{
    stderrData += chunk;
  });

  // When ffprobe finishes, parse the duration from stdout
  child.on("close", (code)=>{
    if(code !== 0) {
      return reject(new Error(`Process failed with code ${code}:\n${stderrData}`));
    }
    resolve(stdoutData);
  });

  return promise;
}

export async function streamToProcessToStream(
  source: Readable, child: ChildProcess, destination: Writable
){
  try {
    const { stdin, stdout, stderr } = child;
    if(!stdin || !stdout || !stderr){
      throw new Error("Failed to spawn");
    }

    await Promise.all([
      pipeline(source, stdin).catch((err)=>{
        switch(err.code){
          case "EPIPE":{
            return console.warn("Child closed stdin early, ignoring EPIPE.");
          }
          case "ERR_STREAM_PREMATURE_CLOSE": {
            return console.warn("Child closed stdin early, ignoring ERR_STREAM_PREMATURE_CLOSE.");
          }
          default: throw err;
        }
      }),
      pipeline(stdout, destination),
      handleBadExit(child),
    ]);
  } finally {
    try { source.destroy(); }catch(e){ console.warn("Ignoring source cancel Error", e); }
    try { child.kill(); }catch(e){ console.warn("Ignoring ChildProcess kill Error", e); }
    try { destination.destroy(); }catch(e){ console.warn("Ignoring destination destroy Error", e); }
  }
}
