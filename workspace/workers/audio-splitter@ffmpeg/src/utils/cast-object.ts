import { JSON_Object, J<PERSON><PERSON>_Unknown } from "./type-json";

type ShallowObject = Record<string, boolean | number | string>;

export type TypeCastConfig<T extends ShallowObject> = (
  Record<keyof T, "boolean" | "number" | "string">
);

export function castObject(bodyUncasted: JSON_Unknown, e: any): JSON_Object{
  if(
    typeof bodyUncasted !== "object" ||
    bodyUncasted === null ||
    Array.isArray(bodyUncasted)
  ) throw e;
  return bodyUncasted;
}

export function castBody<T extends Record<string, boolean | number | string>>(
  castConfig: TypeCastConfig<T>, bodyUncasted: JSON_Unknown, e: any
){
  const objBody = castObject(bodyUncasted, e);

  const casted: Partial<T> = {};
  for(const [key, type] of Object.entries(castConfig)){
    if(!(key in objBody)) throw e;
    if(typeof objBody[key] !== type) throw e;
    casted[key as keyof T] = objBody[key] as T[keyof T]; // Type assertion
  }

  return casted as T;
}
