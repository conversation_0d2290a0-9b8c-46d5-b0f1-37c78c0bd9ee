import { JSON_Unknown } from "./type-json";

export const HTTP_ERRORS = {
  BAD_FORM: { statusCode: 400, message: "bad form data" },
  UNAUTHORIZED: { statusCode: 401, message: "unauthorized" },
  FORBIDDEN: { statusCode: 403, message: "forbidden" },
  NOT_FOUND: { statusCode: 404, message: "not found" },
  LOCKED: { statusCode: 423, message: "document is locked" },
  ALREADY_EXISTS: { statusCode: 409, message: "the item you want to add already exists" },
  SERVER_ERROR: {
    statusCode: 500, message: "something went wrong internally, someone should have been notified"
  },
  TIMEOUT: {
    statusCode: 500, message: "something went wrong internally, someone should have been notified"
  },
  UNAVAILABLE: { statusCode: 503, message: "service unavailable, try again" }
};

export const HTTP_ERRORS_WITH_CONTEXT = Object.fromEntries(Object.entries(HTTP_ERRORS).map(([key, error])=>{
  return [key as keyof typeof HTTP_ERRORS, (context: JSON_Unknown)=>({
    statusCode: error.statusCode,
    message: error.message,
    context
  })];
})) as Record<keyof typeof HTTP_ERRORS, (context: JSON_Unknown)=>{ statusCode: number, message: string, context: JSON_Unknown }>;

class HttpBaseError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public context: undefined | JSON_Unknown = void 0
  ){ super(message); }
}

export const HTTP_ERRORS_WITH_STACK = Object.fromEntries(Object.entries(HTTP_ERRORS).map(([key, error])=>{
  return [key, createErrorClass(error)];
}))  as Record<keyof typeof HTTP_ERRORS, ReturnType<typeof createErrorClass>>;

function createErrorClass({ statusCode, message }: { statusCode: number, message: string }){
  return class HttpError extends HttpBaseError {
    constructor(context?: undefined | JSON_Unknown){
      super(message, statusCode, context);
    }
  };
}

