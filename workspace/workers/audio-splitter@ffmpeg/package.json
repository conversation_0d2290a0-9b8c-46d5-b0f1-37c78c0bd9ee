{"name": "@divinci-ai/ffmpeg-audio-utils", "version": "0.0.0", "description": "Uses ffmpeg to get information and modify media files", "main": "dist/index.js", "scripts": {"prepare": "rimraf ./dist && tsc", "start": "node ./dist/index.js", "start:prod": "npm run prepare && npm run start", "start:dev": "ts-node ./src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "Sam <PERSON>", "license": "JSON", "devDependencies": {"@types/body": "^5.1.4", "@types/express": "^4.17.21", "@types/mime-types": "^2.1.4", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.733.0", "@aws-sdk/lib-storage": "^3.733.0", "body": "^5.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "mime-types": "^2.1.35"}}