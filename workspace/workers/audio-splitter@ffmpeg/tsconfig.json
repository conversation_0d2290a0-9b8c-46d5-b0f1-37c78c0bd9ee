{"compilerOptions": {"target": "es2024", "module": "nodenext", "moduleResolution": "nodenext", "types": ["node"], "lib": ["es2024", "dom"], "sourceMap": true, "outDir": "./dist", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "removeComments": true, "noImplicitAny": true}, "files": ["src/index.ts"], "include": ["./typings", "./types", "**/*.test.ts"], "exclude": ["node_modules", "**/*.spec.ts"]}