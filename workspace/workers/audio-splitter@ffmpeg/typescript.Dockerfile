FROM node:22-bookworm-slim

WORKDIR /home/<USER>

# Install required system dependencies
RUN apt-get update && apt-get install -y ffmpeg && rm -rf /var/lib/apt/lists/*

# Copy package files first for better caching
COPY package.json tsconfig.json ./

# Install dependencies without running prepare script
RUN npm i --ignore-scripts

COPY tsconfig.json ./tsconfig.json
COPY src ./src

# Create dist directory and run TypeScript compilation separately
RUN mkdir -p dist && npx tsc

# Create temp directory for file processing
RUN mkdir -p /file-tmp && chmod -R 755 /file-tmp

CMD [ "npm", "run", "start:dev" ]
