import { describe, it, expect, vi } from 'vitest';
import worker<PERSON><PERSON><PERSON> from '../../src/index';

// Mock the Cloudflare Request object
class MockRequest extends Request {
  constructor(input: RequestInfo, init?: RequestInit) {
    super(input, init);
  }
}

// Extract the fetch handler for easier testing
const fetchHandler = workerHandler.fetch;

describe('AI Prompt Format', () => {
  let env: any;
  let context: any;

  beforeEach(() => {
    // Mock the Cloudflare Worker environment
    env = {
      AI: {
        run: vi.fn().mockResolvedValue({
          category: "text/chat/question",
          confidence: 0.95
        })
      }
    };
    context = {};
  });

  it('should format the prompt with all categories', async () => {
    // Test categorization request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Test input text' })
    });

    // Execute the worker's fetch handler
    await fetch<PERSON><PERSON><PERSON>(mockRequest, env, context);
    
    // Get the prompt from the AI mock call
    const callArgs = env.AI.run.mock.calls[0][1];
    const promptMessage = callArgs.messages[0].content;
    
    // Expected categories from the CATEGORIES constant in the source code
    const expectedCategories = [
      'diagram/chart', 
      'text/chat/question', 
      'image', 
      'audio', 
      'video', 
      'unknown'
    ];
    
    // Verify each category is properly quoted in the prompt
    expectedCategories.forEach(category => {
      expect(promptMessage).toContain(`"${category}"`);
    });
  });

  it('should include input text in the prompt', async () => {
    const testInput = "This is a specific test phrase to check in the prompt";
    
    // Test categorization request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: testInput })
    });

    // Execute the worker's fetch handler
    await fetchHandler(mockRequest, env, context);
    
    // Get the prompt from the AI mock call
    const callArgs = env.AI.run.mock.calls[0][1];
    const promptMessage = callArgs.messages[0].content;
    
    // Verify the input text appears in the prompt
    expect(promptMessage).toContain(`"${testInput}"`);
  });

  it('should request JSON format response in the prompt', async () => {
    // Test categorization request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Test input' })
    });

    // Execute the worker's fetch handler
    await fetchHandler(mockRequest, env, context);
    
    // Get the prompt from the AI mock call
    const callArgs = env.AI.run.mock.calls[0][1];
    const promptMessage = callArgs.messages[0].content;
    
    // Verify the JSON format instructions are included
    expect(promptMessage).toContain('{"category": "{category}", "confidence": {confidence}}');
    expect(promptMessage).toContain('JSON format');
  });

  it('should use the correct AI model', async () => {
    // Test categorization request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Test input' })
    });

    // Execute the worker's fetch handler
    await fetchHandler(mockRequest, env, context);
    
    // Verify the correct model is being used
    const modelName = env.AI.run.mock.calls[0][0];
    expect(modelName).toBe('@hf/thebloke/mistral-7b-instruct-v0.1-awq');
  });

  it('should specify JSON response format in AI options', async () => {
    // Test categorization request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Test input' })
    });

    // Execute the worker's fetch handler
    await fetchHandler(mockRequest, env, context);
    
    // Verify response_format is set correctly
    const aiOptions = env.AI.run.mock.calls[0][1];
    expect(aiOptions.response_format).toEqual({ type: 'json_object' });
  });
});