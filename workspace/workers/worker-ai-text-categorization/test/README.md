# Text Categorization Worker Tests

This directory contains tests for the `worker-ai-text-categorization` Cloudflare Worker.

## Test Structure

- `unit/` - Unit tests for individual components of the worker
  - `index.test.ts` - Tests for the main worker functionality and error handling
  - `prompt-format.test.ts` - Tests specifically for AI prompt formatting
  - `simple.test.ts` - Basic tests to verify test environment
  - `setup.ts` - Test setup and global mocks

## Running Tests

To run the tests for this worker:

```bash
cd workspace/workers/worker-ai-text-categorization
pnpm test
```

For watching tests during development:

```bash
pnpm test --watch
```

## Testing Approach

The tests use Vitest with a Node-based test environment. Key testing strategies:

1. **Mock the Worker Environment**: We mock the Cloudflare Worker environment, including the AI API, to test our code in isolation.

2. **Test Error Handling**: Ensure the worker gracefully handles errors from malformed requests, AI service failures, etc.

3. **Test Response Format**: Verify the worker returns correctly formatted responses with appropriate status codes.

4. **Input Variations**: Test various text inputs to ensure proper categorization prompts are sent to the AI.

5. **Prompt Structure**: Verify that prompts sent to the AI model are correctly formatted with all required elements.

## Test Coverage

The tests cover:

- ✅ Valid text input processing
- ✅ Error handling for malformed requests
- ✅ Error handling for missing required fields
- ✅ Error handling for AI service failures
- ✅ Response formatting
- ✅ AI prompt content and structure
- ✅ Model selection and parameters
- ✅ JSON response formatting

## Adding New Tests

When adding new functionality to the worker, follow these guidelines:

1. Create a new test file in the appropriate directory if testing a new component
2. For small changes, add test cases to existing test files
3. Always mock external dependencies
4. Test both success and failure paths
5. When testing AI prompt formatting, verify all required components are included