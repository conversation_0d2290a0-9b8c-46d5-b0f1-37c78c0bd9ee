import { describe, it, expect, vi, beforeEach } from 'vitest';
import worker<PERSON>andler from '../../src/index';

// Mock the Cloudflare Request object
class MockRequest extends Request {
  constructor(input: RequestInfo, init?: RequestInit) {
    super(input, init);
  }
}

// Extract the fetch handler for easier testing
const fetchHandler = workerHandler.fetch;

describe('Text Categorization Worker', () => {
  let env: any;
  let context: any;

  beforeEach(() => {
    // Mock the Cloudflare Worker environment
    env = {
      AI: {
        run: vi.fn(),
      }
    };
    context = {};

    // Reset console mocks
    vi.spyOn(console, 'log').mockClear();
    vi.spyOn(console, 'error').mockClear();
  });

  it('should handle valid text input and return AI response', async () => {
    // Mock the AI response
    const mockAIResponse = {
      category: "text/chat/question", 
      confidence: 0.95
    };
    
    env.AI.run.mockResolvedValue(mockAIResponse);
    
    // Create a mock request with valid JSON body
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'How do I reset my password?' })
    });

    // Execute the worker's fetch handler
    const response = await fetchHandler(mockRequest, env, context);
    
    // Verify response
    expect(response.status).toBe(200);
    const responseBody = await response.json();
    expect(responseBody).toEqual(mockAIResponse);
    
    // Verify AI was called with correct parameters
    expect(env.AI.run).toHaveBeenCalledTimes(1);
    expect(env.AI.run).toHaveBeenCalledWith(
      expect.any(String), // Model name
      expect.objectContaining({
        messages: expect.arrayContaining([
          expect.objectContaining({
            role: 'user',
            content: expect.stringContaining('How do I reset my password?')
          })
        ]),
        response_format: { type: 'json_object' }
      })
    );
  });

  it('should handle malformed request body', async () => {
    // Create a mock request with invalid JSON body that will throw during parsing
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      // We need to use a valid JSON string that will still cause an error when parsed
      body: JSON.stringify({}), // Empty object will cause error since 'text' is missing
    });
    
    // Mock the json method to throw a parsing error
    mockRequest.json = vi.fn().mockRejectedValue(new Error('Invalid JSON'));

    // Execute the worker's fetch handler
    const response = await fetchHandler(mockRequest, env, context);
    
    // Verify error response
    expect(response.status).toBe(500);
    const responseText = await response.text();
    expect(responseText).toContain('Error');
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
  });

  it('should handle missing text field in request', async () => {
    // Create a mock request with missing text field
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ someOtherField: 'value' })
    });

    // Execute the worker's fetch handler
    const response = await fetchHandler(mockRequest, env, context);
    
    // Verify error response - this could be 500 or could be undefined text causing various errors
    expect(response.status).toBe(500);
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
  });

  it('should handle AI service errors', async () => {
    // Mock AI service error
    env.AI.run.mockRejectedValue(new Error('AI service unavailable'));
    
    // Create a mock request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Analyze this text' })
    });

    // Execute the worker's fetch handler
    const response = await fetchHandler(mockRequest, env, context);
    
    // Verify error response
    expect(response.status).toBe(500);
    const responseText = await response.text();
    expect(responseText).toContain('AI service unavailable');
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
  });

  it('should handle empty AI response', async () => {
    // Mock null AI response
    env.AI.run.mockResolvedValue(null);
    
    // Create a mock request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: 'Analyze this text' })
    });

    // Execute the worker's fetch handler
    const response = await fetchHandler(mockRequest, env, context);
    
    // Verify error response
    expect(response.status).toBe(500);
    const responseText = await response.text();
    expect(responseText).toContain('Invalid AI response structure');
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
  });

  it('should generate a proper prompt with categories', async () => {
    // Mock a successful response
    env.AI.run.mockResolvedValue({
      category: "text/chat/question", 
      confidence: 0.95
    });
    
    const inputText = "How does this work?";
    
    // Create a mock request
    const mockRequest = new MockRequest('https://example.com/categorize', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: inputText })
    });

    // Execute the worker's fetch handler
    await fetchHandler(mockRequest, env, context);
    
    // Check that all categories are included in the prompt
    const expectedCategories = ['diagram/chart', 'text/chat/question', 'image', 'audio', 'video', 'unknown'];
    
    const callArgs = env.AI.run.mock.calls[0][1];
    const promptMessage = callArgs.messages[0].content;
    
    // Verify all categories are in the prompt
    expectedCategories.forEach(category => {
      expect(promptMessage).toContain(category);
    });
    
    // Verify input text is in the prompt
    expect(promptMessage).toContain(inputText);
    
    // Verify JSON format instructions are in the prompt
    expect(promptMessage).toContain('JSON format');
  });
});