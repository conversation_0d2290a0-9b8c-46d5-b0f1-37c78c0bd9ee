import { Hono } from 'hono';
import { UnstructuredClient } from 'unstructured-client';
import { Strategy, ChunkingStrategy } from 'unstructured-client/sdk/models/shared/index.js';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';

interface CloudflareBindings {
  UNSTRUCTURED_API_KEY: string;
}

interface ProcessedChunk {
  text: string;
  metadata: {
    type?: string;
    page_number?: number;
    coordinates?: any;
    [key: string]: any;
  };
}

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Configuration schema
const configSchema = z.object({
  url: z.string().url(),
  filename: z.string(),
  config: z.object({
    minTokens: z.number().optional().default(256),
    maxTokens: z.number().optional().default(1024),
    chunkingStrategy: z.enum(['by_title', 'by_similarity', 'by_page']).optional().default('by_title'),
    maxCharacters: z.number().optional().default(1024),
    similarityThreshold: z.number().optional().default(0.5),
    splitPdfPage: z.boolean().optional().default(true),
    splitPdfConcurrencyLevel: z.number().optional().default(5),
    splitPdfAllowFailed: z.boolean().optional().default(true)
  }).optional().default({})
});

/**
 * Converts a ReadableStream to Uint8Array
 */
async function streamToBuffer(stream: ReadableStream<Uint8Array>): Promise<Uint8Array> {
  const chunks: Uint8Array[] = [];
  const reader = stream.getReader();
  
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    chunks.push(value);
  }
  
  // Compute total length
  const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0);
  
  // Concatenate chunks
  const result = new Uint8Array(totalLength);
  let offset = 0;
  for (const chunk of chunks) {
    result.set(chunk, offset);
    offset += chunk.length;
  }
  
  return result;
}

/**
 * Retries an operation with exponential backoff
 */
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 2000
): Promise<T> {
  let lastError: Error;
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      if (i === maxRetries - 1) break;
      
      const delay = initialDelay * Math.pow(2, i);
      console.log(`Retrying after ${delay}ms. Attempt ${i + 1}/${maxRetries}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw lastError!;
}

// Initialize endpoint
app.post('/fileUrl/init', zValidator('json', configSchema), async (c) => {
  const { url, filename, config } = c.req.valid('json');
  
  try {
    // Generate a session ID
    const sessionId = crypto.randomUUID();
    
    // Download the file with streaming
    const fileResponse = await fetch(url);
    if (!fileResponse.ok) {
      return c.json({ error: `Failed to download file: ${fileResponse.statusText}` }, 400);
    }
    
    if (!fileResponse.body) {
      return c.json({ error: 'File response has no body' }, 400);
    }

    // Convert stream to buffer for processing
    const fileBuffer = await streamToBuffer(fileResponse.body);
    
    // Initialize Unstructured client with retry configuration
    const client = new UnstructuredClient({
      security: { apiKeyAuth: c.env.UNSTRUCTURED_API_KEY },
      serverURL: "https://api.unstructuredapp.io/general/v0/general",
      retryConfig: {
        strategy: "backoff",
        retryConnectionErrors: true,
        backoff: {
          initialInterval: 2000,
          maxInterval: 15000,
          exponent: 2,
          maxElapsedTime: 120000
        },
      }
    });

    // Process the file with retries
    const response = await retryWithBackoff(async () => {
      return await client.general.partition({
        partitionParameters: {
          files: {
            content: new Blob([fileBuffer]),
            fileName: filename
          },
          strategy: Strategy.Fast,
          chunkingStrategy: config.chunkingStrategy as ChunkingStrategy,
          maxCharacters: config.maxCharacters,
          splitPdfPage: config.splitPdfPage,
          splitPdfConcurrencyLevel: config.splitPdfConcurrencyLevel,
          splitPdfAllowFailed: config.splitPdfAllowFailed
        }
      });
    });

    // Handle empty response from Unstructured API
    if (!response.elements || response.elements.length === 0) {
      console.warn('No elements returned from Unstructured API');
      return c.json({
        session_id: sessionId,
        total_chunks: 0
      });
    }

    // Transform elements into chunks
    const chunks: ProcessedChunk[] = response.elements.map(element => ({
      text: element.text,
      metadata: {
        type: element.type,
        page_number: element.metadata?.page_number,
        coordinates: element.metadata?.coordinates,
        ...element.metadata
      }
    }));

    return c.json({
      session_id: sessionId,
      total_chunks: chunks.length,
      chunks // Return chunks directly in the response
    });

  } catch (err: unknown) {
    console.error('Processing error:', err);
    const error = err as Error;
    return c.json({ 
      error: 'Processing failed',
      details: error?.message || 'Unknown error occurred'
    }, 500);
  }
});

// Batch endpoint removed as we're returning all chunks in the init response

// Health check endpoint
app.get('/', (c) => {
  return c.text('Unstructured Chunker Service - OK');
});

export default app;
