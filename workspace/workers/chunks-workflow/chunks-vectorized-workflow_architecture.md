# chunks-workflow Architecture

## Ultimate Goal

- [x] Convert all Cloudflare related steps, R2 and Vectorize, from `workspace/resources/server-models/src/white-label/rag/custom-vector/methods/addRagFile/index.ts` into a Cloudflare Worker Workflow. Improvements and Optimizations welcome.🤗

### Workflow Steps (can happen async I think. We can do it one file as a time given all of this context and steps involved)

- [x] File(s), folder, zip upload
- [x] Files saved in R2
- [x] file sent to chunking tool (Unstructured or Open-Parse): `workspace/clients/web/src/pages/WhiteLabel/Setup/ReleaseComponents/RagVector/File/Root/v1/NewFileForm/submit-add-file.ts::submitAddFile()`, `workspace/servers/public-api/src/ux/whitelabel/router/rag-vector/files/add-file.ts`
- [x] Junk Chunks filtered out like page formatting/numbers ect maybe via an mini-LLM function filter asking if this chunk is on-topic or not based on a float value.
- [x] Chunks saved to Cloudflare D1: `workspace/resources/server-models/src/white-label/rag/shared/chunks/d1/stream.ts::storeTextChunkStreamD1`, `workspace/workers/d1-doc-elements/src`
  - [x] Chunks will be auto finalized: `workspace/resources/server-models/src/white-label/rag/file/methods/finalize/index.ts`
- [x] ensureEmbeddings: `workspace/resources/server-models/src/white-label/rag/custom-vector/methods/addRagFile/ensure-embedings.ts`
  - [x] existsInR2: `workspace/resources/server-models/src/white-label/rag/shared/r2.ts`
  - [x] getStreamFromR2: `workspace/resources/server-models/src/white-label/rag/shared/r2.ts`
  - [x] saveStreamToR2: `workspace/resources/server-models/src/white-label/rag/shared/r2.ts`
- [x] getEmbedStream::getStreamFromR2 `workspace/resources/server-models/src/white-label/rag/custom-vector/methods/addRagFile/get-embed-stream.ts`, `workspace/resources/server-models/src/white-label/rag/shared/r2.ts`
- [x] tools::addVectorsToIndex: `workspace/resources/server-tools/src/rag/vector/cloudflare/add-id-to-index.ts`


/workers/chunks-workflow
  /r2-operations/
    - Handle raw file uploads/downloads
    - Manage file chunks
    - Stream management

  /embeddings-worker/
    - Generate embeddings from chunks
    - Store embeddings in R2
    - Stream processing

  /vector-index-worker/
    - Interface with Cloudflare Vectorize
    - Handle vector additions/queries
    - Manage vector indexes

#### Cloudeflare Worker Workfflow Example:
`workspace/workers/chunks-workflow`
