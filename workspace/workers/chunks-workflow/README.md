# Chunks Vectorized Workflow

A Cloudflare Worker Workflow that processes documents through chunking, filtering, embedding generation, and vector storage for RAG (Retrieval Augmented Generation) applications.

## API Endpoints

### Single File Processing
POST `/api/process`
```json
{
  "files": [{
    "fileId": "string",
    "target": "string",
    "fileName": "string",
    "bucket": "string",
    "objectKey": "string",
    "processor": "unstructured" | "openparse",
    "processorConfig": {
      "semantic_chunking": boolean,
      "embeddings_provider": "cloudflare" | "ollama" | "none",
      "minTokens": number,
      "maxTokens": number,
      "relevanceThreshold": number,
      "skipDeJunk": boolean
    }
  }],
  "vectorizeConfig": {
    "accountId": "string",
    "apiToken": "string",
    "ragName": "string",
    "ragId": "string",
    "whitelabelId": "string",
    "auth0Token": "string"
  }
}
```

### Streaming File Upload
POST `/api/process/stream`
- Headers:
  - `x-file-name`: Filename
  - `x-file-id`: Unique file identifier
  - `x-target`: Target namespace
  - `x-processor`: "unstructured" | "openparse"
  - `x-processor-config`: JSON string of processor configuration
  - `x-vectorize-config`: JSON string of vectorize configuration

## Workflow Architecture

```mermaid
flowchart TD
    A[Document Upload] -->|R2 Storage| B[Document Processing]
    B -->|Raw Chunks| C[DeJunk Filter]
    C -->|Filtered Chunks| D[D1 Database]
    D -->|Chunks| E[BGE Embeddings]
    E -->|Vectors| F[Cloudflare Vectorize]

    subgraph "Step 1: Storage & Processing"
        A
        B
    end

    subgraph "Step 2: Filtering & Storage"
        C
        D
    end

    subgraph "Step 3: Vector Processing"
        E
        F
    end
```

## Workflow Steps & Metadata Tracking

1. **R2 File Processing**
   - Validates file in R2
   - Processes document using selected processor (Unstructured/OpenParse)
   - Tracks:
     - Processing duration
     - Original chunk count
     - File size and mime type

2. **DeJunk Filtering**
   - Optional filtering step (can be skipped)
   - Filters low-quality or irrelevant content
   - Tracks:
     - Processing duration
     - Chunks before/after filtering
     - Relevance threshold used
     - Skip status

3. **Vector Processing**
   - Generates embeddings using BGE model
   - Stores vectors in Cloudflare Vectorize
   - Tracks:
     - Processing duration
     - Total batches processed
     - Total vectors generated
     - Batch size
     - Embeddings provider

## Configuration Constants

```typescript
const FILTER_BATCH_SIZE = 100;    // Chunks per filtering batch
const EMBEDDING_BATCH_SIZE = 50;   // Chunks per embedding batch
```

## Required Environment Variables

```typescript
type Env = {
  DB: D1Database;                 // Cloudflare D1
  R2: R2Bucket;                   // Cloudflare R2
  AI: any;                        // Cloudflare AI
  UNSTRUCTURED_API_KEY: string;   // Unstructured API
  OPENPARSE_API_KEY: string;     // OpenParse API
  OPENPARSE_API_URL: string;         // OpenParse endpoint
  CLOUDFLARE_ACCOUNT_ID: string;  // CF Account ID
  CLOUDFLARE_API_TOKEN: string;   // CF API Token
  CLOUDFLARE_API_URL?: string;    // CF API URL
};
```

## Workflow Metadata Schema

```typescript
interface WorkflowMetadata {
  startTime: number;
  endTime?: number;
  totalDuration?: number;
  steps: {
    r2Processing: {
      duration: number;
      originalChunks: number;
    };
    deJunk: {
      duration: number;
      chunksBeforeDeJunk: number;
      chunksAfterDeJunk: number;
      skipped: boolean;
      relevanceThreshold?: number;
    };
    vectorization: {
      duration: number;
      totalBatches: number;
      totalVectors: number;
      batchSize: number;
      embeddingsProvider: string;
    };
  };
  fileInfo: {
    fileId: string;
    fileName: string;
    processor: string;
    processorConfig: any;
    size?: number;
    mimeType?: string;
  };
  status: 'completed' | 'failed';
  error?: string;
}
```

## Error Handling

- Comprehensive error tracking in workflow metadata
- Failed workflows store error details in D1
- Non-retryable errors for permanent failures
- Proper cleanup in finally block

## APIs Used

1. **Cloudflare Services**
   - R2 (Document Storage)
   - D1 (Metadata & Chunks)
   - AI (@cf/baai/bge-base-en-v1.5)
   - Vectorize (Vector Storage)
   - Workers (Compute)

2. **Document Processing**
   - Unstructured API
   - OpenParse API

## Response Format

```typescript
{
  success: boolean;
  fileId: string;
  chunks: Array<{
    id: string;
    text: string;
    metadata: string;
  }>;
  chunkCount: number;
  embeddingsCount: number;
  vectorizeResults: Array<{
    batchId: string;
    vectorCount: number;
    timestamp: string;
  }>;
}
```
