CREATE TABLE IF NOT EXISTS workflow_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workflow_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    metadata JSON NOT NULL,
    error TEXT,
    created_at DATETIME NOT NULL,
    UNIQUE(workflow_id, file_id)  -- This ensures one record per workflow-file combination
);

CREATE INDEX IF NOT EXISTS idx_workflow_metadata_file_id ON workflow_metadata(file_id);
CREATE INDEX IF NOT EXISTS idx_workflow_metadata_created_at ON workflow_metadata(created_at);
