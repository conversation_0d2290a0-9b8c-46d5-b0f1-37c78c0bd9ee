CREATE TABLE IF NOT EXISTS workflow_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    workflow_id TEXT NOT NULL,
    file_id TEXT NOT NULL,
    metadata TEXT NOT NULL,
    error TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(workflow_id, file_id)
);

-- Add indexes for common queries
CREATE INDEX IF NOT EXISTS idx_workflow_metadata_workflow_id ON workflow_metadata(workflow_id);
CREATE INDEX IF NOT EXISTS idx_workflow_metadata_file_id ON workflow_metadata(file_id);