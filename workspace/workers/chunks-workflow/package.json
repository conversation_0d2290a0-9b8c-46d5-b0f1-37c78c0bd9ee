{"name": "chunks-workflow", "scripts": {"dev": "wrangler dev", "dev:local": "wrangler dev --local", "deploy:local": "wrangler deploy", "deploy": "wrangler deploy --minify", "build": "node -r esbuild-register ./esbuild.config.js", "cf-typegen": "wrangler types", "test": "vitest"}, "dependencies": {"@aws-sdk/client-s3": "^3.750.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@divinci-ai/server-tools": "file:../../resources/server-tools", "@divinci-ai/utils": "file:../../resources/utils", "@hono/zod-validator": "^0.1.11", "esbuild": "^0.25.0", "gpt-tokens": "^1.3.12", "hono": "^3.11.7", "mongoose": "^8.11.0", "unstructured-client": "^0.19.0", "zod": "^3.24.2"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250204.0", "@types/node": "^22.13.4", "typescript": "^5.7.3", "wrangler": "^3.109.1"}}