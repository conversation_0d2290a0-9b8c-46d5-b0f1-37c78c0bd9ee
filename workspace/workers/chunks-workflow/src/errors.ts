/**
 * Custom error class for non-retryable errors in the workflow
 * These errors indicate that retrying the operation would not help
 */
export class NonRetryableError extends Error {
  readonly __type = 'NonRetryableError';

  constructor(message: string) {
    super(message);
    this.name = 'NonRetryableError';
    Object.setPrototypeOf(this, NonRetryableError.prototype);
  }

  static isNonRetryableError(error: unknown): error is NonRetryableError {
    return error instanceof NonRetryableError || 
           (error as any)?.__type === 'NonRetryableError';
  }
}

/**
 * Custom error class for retryable errors in the workflow
 * These errors indicate that the operation might succeed if retried
 */
export class RetryableError extends Error {
  readonly __type = 'RetryableError';

  constructor(message: string) {
    super(message);
    this.name = 'RetryableError';
    Object.setPrototypeOf(this, RetryableError.prototype);
  }

  static isRetryableError(error: unknown): error is RetryableError {
    return error instanceof RetryableError || 
           (error as any)?.__type === 'RetryableError';
  }
}