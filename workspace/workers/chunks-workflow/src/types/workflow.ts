export interface WorkflowMetadata {
  startTime: number,
  endTime?: number,
  totalDuration?: number,
  steps: {
    r2Processing: {
      duration: number,
      originalChunks: number,
    },
    chunking: {
      duration: number,
      originalChunks: number,
      processor: "unstructured" | "openparse",
      success: boolean,
      error?: string,
    },
    deJunk: {
      duration: number,
      chunksBeforeDeJunk: number,
      chunksAfterDeJunk: number,
      skipped: boolean,
      relevanceThreshold?: number,
    },
    vectorization: {
      duration: number,
      totalBatches: number,
      totalVectors: number,
      batchSize: number,
      embeddingsProvider: string,
    },
  },
  fileInfo: {
    fileId: string,
    fileName: string,
    processor: string,
    processorConfig: any,
    size?: number,
    mimeType?: string,
  },
  status: "completed" | "failed",
  error?: string,
}

interface ChunkMetadata {
  whiteLabelId: string,
  originalName: string,
  vectorId: string,
  fileObjectKey: string,
  tokenCount: number,
  tags?: string[],  // Add tags as an optional string array
  textPreview?: string,  // Add textPreview as an optional string
}

export interface ProcessedChunk {
  id: string,
  text: string,
  metadata: ChunkMetadata,
}
