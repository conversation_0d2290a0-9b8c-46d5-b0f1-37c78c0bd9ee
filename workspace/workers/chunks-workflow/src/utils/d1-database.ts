import { NonRetryableError } from 'cloudflare:workflows';

interface CloudflareApiResponse<T> {
  result: T;
  success: boolean;
  errors: any[];
  messages: any[];
}

interface D1Database {
  uuid: string;
  name: string;
  version: string;
  created_at: string;
  modified_at: string;
}

export async function findOrCreateD1Database(
  whitelabelId: string,
  accountId: string,
  apiToken: string,
  apiUrl: string = 'https://api.cloudflare.com/client/v4'
): Promise<D1Database> {
  const databaseName = `d1-chunks-${whitelabelId}`;
  
  // Check if we're in local development
  const isLocal = apiUrl.includes('local-d1-rag');
  
  try {
    // If we're in local development, return a mock D1Database object
    if (isLocal) {
      return {
        uuid: databaseName, // Use the name as the UUID for local development
        name: databaseName,
        version: 'local',
        created_at: new Date().toISOString(),
        modified_at: new Date().toISOString()
      };
    }

    // Simplified headers with just the essential authentication
    const headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };

    // Log request details (remove in production)
    console.log('Making D1 API request:', {
      url: `${apiUrl}/accounts/${accountId}/d1/database`,
      accountId,
      tokenPrefix: apiToken.substring(0, 4) + '...',
      headers: Object.keys(headers)
    });

    // First try to find existing database
    const listResponse = await fetch(
      `${apiUrl}/accounts/${accountId}/d1/database`,
      {
        method: 'GET',
        headers
      }
    );

    if (!listResponse.ok) {
      const errorText = await listResponse.text();
      console.error('D1 List Response Error:', {
        status: listResponse.status,
        statusText: listResponse.statusText,
        body: errorText,
        requestUrl: `${apiUrl}/accounts/${accountId}/d1/database`,
        requestHeaders: headers
      });
      throw new Error(`Failed to list D1 databases: ${errorText}`);
    }

    const { result: databases } = await listResponse.json() as CloudflareApiResponse<D1Database[]>;
    const existingDb = databases.find((db: D1Database) => db.name === databaseName);

    if (existingDb) {
      return existingDb;
    }

    // Create new database if not found
    const createResponse = await fetch(
      `${apiUrl}/accounts/${accountId}/d1/database`,
      {
        method: 'POST',
        headers,
        body: JSON.stringify({ name: databaseName })
      }
    );

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error('D1 Create Response Error:', {
        status: createResponse.status,
        statusText: createResponse.statusText,
        body: errorText
      });
      throw new Error(`Failed to create D1 database: ${errorText}`);
    }

    const { result: newDb } = await createResponse.json() as CloudflareApiResponse<D1Database>;
    return newDb;
  } catch (error) {
    console.error('Error in findOrCreateD1Database:', error);
    // Add more context to the error
    console.error('Request details:', {
      accountId,
      apiUrl,
      databaseName,
      tokenLength: apiToken?.length || 0
    });
    throw new NonRetryableError(`Failed to find or create D1 database: ${error.message}`);
  }
}

export async function ensureChunksTable(
  databaseId: string,
  accountId: string,
  apiToken: string,
  whitelabelId: string,  // Add whitelabelId parameter
  apiUrl: string = 'https://api.cloudflare.com/client/v4'
): Promise<void> {
  // Check if we're in local development
  const isLocal = apiUrl.includes('local-d1-rag');
  
  if (isLocal) {
    return;
  }

  const tableName = `d1_chunks_${whitelabelId.replace(/-/g, '_')}`;
  const query = `
    CREATE TABLE IF NOT EXISTS ${tableName} (
      id TEXT PRIMARY KEY,
      text TEXT,
      metadata TEXT
    )
  `;

  try {
    const response = await fetch(
      `${apiUrl}/accounts/${accountId}/d1/database/${databaseId}/query`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ sql: query })
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to create table: ${await response.text()}`);
    }
  } catch (error) {
    console.error('Error in ensureChunksTable:', error);
    throw new NonRetryableError(`Failed to ensure chunks table: ${error.message}`);
  }
}
