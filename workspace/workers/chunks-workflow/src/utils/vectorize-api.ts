export class VectorizeAPI {
  private readonly baseUrl: string;
  private readonly headers: Headers;
  private readonly skipTlsVerify: boolean;
  private readonly maxRetries: number = 3;
  private readonly initialRetryDelay: number = 1000; // 1 second
  private readonly maxRetryDelay: number = 10000; // 10 seconds
  private readonly timeout: number = 30000; // 30 seconds

  constructor(config: {
    accountId: string,
    apiToken: string,
    ragName: string,
  }, opts: {
    skipTlsVerify?: boolean,
    maxRetries?: number,
    timeout?: number,
  } = {}){
    this.baseUrl = `https://api.cloudflare.com/client/v4/accounts/${config.accountId}/vectorize/v2/indexes`;
    this.headers = new Headers({
      "Authorization": `Bearer ${config.apiToken}`,
      "Content-Type": "application/x-ndjson",
    });
    this.skipTlsVerify = opts.skipTlsVerify ?? false;
    if(opts.maxRetries) this.maxRetries = opts.maxRetries;
    if(opts.timeout) this.timeout = opts.timeout;
  }

  private async sleep(ms: number): Promise<void>{
    return new Promise(resolve=>setTimeout(resolve, ms));
  }

  private calculateRetryDelay(attempt: number): number{
    // Exponential backoff with jitter
    const exponentialDelay = Math.min(
      this.maxRetryDelay,
      this.initialRetryDelay * Math.pow(2, attempt - 1)
    );
    // Add random jitter ±20%
    const jitter = exponentialDelay * 0.2 * (Math.random() * 2 - 1);
    return exponentialDelay + jitter;
  }

  private isRetryableError(error: any): boolean{
    // Network errors, timeouts, and certain status codes are retryable
    if(error instanceof TypeError) return true; // Network errors
    if(error.message?.includes("timeout")) return true;
    const retryableStatusCodes = [408, 429, 500, 502, 503, 504];
    return retryableStatusCodes.includes(error.status);
  }

  private async request(path: string, init?: RequestInit){
    let lastError: Error | null = null;

    for(let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const url = `${this.baseUrl}${path}`;
        console.log(`🔄 Vectorize API Request (attempt ${attempt}/${this.maxRetries}):`, {
          url,
          method: init?.method,
          bodyPreview: init?.body ? JSON.stringify(init.body).slice(0, 200) + "..." : null
        });

        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);

        const response = await fetch(url, {
          ...init,
          headers: this.headers,
          signal: controller.signal,
          cf: this.skipTlsVerify ? { insecureHTTPParser: true } : undefined
        });

        clearTimeout(timeoutId);

        const responseData = await response.text();
        console.log(`📥 Vectorize API Response (attempt ${attempt}/${this.maxRetries}):`, {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          body: responseData.slice(0, 200) + "..."
        });

        if(!response.ok) {
          throw new Error(`HTTP ${response.status} ${response.statusText}\nResponse: ${responseData}`);
        }

        return JSON.parse(responseData);
      }catch(error: any) {
        lastError = error;

        if(attempt === this.maxRetries || !this.isRetryableError(error)) {
          throw new Error(`Vectorize API request failed after ${attempt} attempts: ${error.message}`);
        }

        const retryDelay = this.calculateRetryDelay(attempt);
        console.warn(`⚠️ Request failed (attempt ${attempt}/${this.maxRetries}). Retrying in ${retryDelay}ms...`, error);
        await this.sleep(retryDelay);
      }
    }

    throw lastError;
  }

  async createIndex(indexName: string, description: string){
    return this.request("/", {
      method: "POST",
      body: JSON.stringify({
        name: indexName,
        description,
        config: { preset: "@cf/baai/bge-base-en-v1.5" }
      })
    });
  }

  async getIndex(indexName: string){
    return this.request(`/${indexName}`, {
      method: "GET"
    });
  }

  async deleteIndex(indexName: string){
    return this.request(`/${indexName}`, {
      method: "DELETE"
    });
  }

  async upsert(indexName: string, vectors: Array<{
    id: string,
    values: number[],
    metadata?: Record<string, unknown>,
  }>){
    // Split vectors into smaller batches if needed
    const BATCH_SIZE = 10; // Reduce batch size to handle timeouts better
    const batches = [];

    for(let i = 0; i < vectors.length; i += BATCH_SIZE) {
      batches.push(vectors.slice(i, i + BATCH_SIZE));
    }

    const results = [];
    for(let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`📤 Upserting vectors batch ${i + 1}/${batches.length}:`, {
        indexName,
        vectorCount: batch.length,
        firstVector: {
          id: batch[0]?.id,
          valueLength: Array.isArray(batch[0]?.values) ? batch[0].values.length : "not an array",
          sampleValues: Array.isArray(batch[0]?.values) ? batch[0].values.slice(0, 5) : "not an array",
          metadata: batch[0]?.metadata
        }
      });

      // Add validation before processing
      if(!batch.every(vector=>Array.isArray(vector.values))) {
        throw new Error("Invalid vector format: values must be an array of numbers");
      }

      // Convert batch to NDJSON
      const ndjsonVectors = batch.map(vector=>JSON.stringify({
        id: vector.id,
        values: vector.values,
        metadata: vector.metadata || {}
      })).join("\n");

      const result = await this.request(`/${indexName}/upsert`, {
        method: "POST",
        body: ndjsonVectors
      });

      results.push(result);
    }

    return results;
  }

  async query(
    indexName: string,
    vector: number[],
    topK: number = 5,
    metadata?: Record<string, unknown>
  ){
    return this.request(`/${indexName}/query`, {
      method: "POST",
      body: JSON.stringify({
        vector,
        topK,
        metadata
      })
    });
  }

  async getIndexes(){
    return this.request("/", {
      method: "GET"
    }) as Promise<{
      name: string,
      description: string,
      created: string,
      updated: string,
      vectorDimensions: number,
      metric: string,
    }[]>;
  }

  async findOrCreateIndex(indexName: string, description: string){
    try {
      return await this.getIndex(indexName);
    }catch(error) {
      if(error instanceof Error && error.message.includes("404")) {
        return this.createIndex(indexName, description);
      }
      throw error;
    }
  }

  async getVectors(indexName: string, ids: string[]): Promise<string[]>{
    return this.request(`/${indexName}/vectors`, {
      method: "POST",
      body: JSON.stringify({ ids })
    }) as Promise<string[]>;
  }
}
