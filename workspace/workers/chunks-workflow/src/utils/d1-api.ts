import { NonRetryableError } from 'cloudflare:workflows';

interface D1Response {
  success: boolean;
  message?: string;
  error?: string;
}

const MAX_BATCH_SIZE = 20; // Increased from 1 to 20 for better performance

export async function ensureChunksTable(
  whitelabelId: string,
  db: D1Database
): Promise<void> {
  if (!db) {
    throw new NonRetryableError('D1 Database instance is required');
  }

  console.log("💽 db:", db);

  const tableName = `d1_chunks_${whitelabelId.replace(/-/g, '_')}`;
  
  try {
    // Create table if it doesn't exist
    await db.prepare(`
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        text TEXT NOT NULL,
        metadata TEXT
      )
    `).run();

    // Add index for better query performance
    await db.prepare(`
      CREATE INDEX IF NOT EXISTS idx_${tableName}_metadata 
      ON ${tableName}((json_extract(metadata, '$.fileObjectKey')))
    `).run();

    console.log(`✅ Successfully ensured table ${tableName} exists`);
  } catch (error) {
    console.error(`❌ Error ensuring table ${tableName}:`, error);
    throw new NonRetryableError(`❌ Failed to ensure table exists: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export async function executeD1Query(
  accountId: string,
  apiToken: string,
  apiUrl: string,
  chunks: Array<{id: string; text: string; metadata: any}>
): Promise<void> {
  console.log(`🔍 D1 API URL: ${apiUrl}`);
  console.log(`📊 Total chunks to process: ${chunks.length}`);

  // Split chunks into smaller batches
  for (let i = 0; i < chunks.length; i += MAX_BATCH_SIZE) {
    const batchChunks = chunks.slice(i, i + MAX_BATCH_SIZE);
    
    // Format chunks for D1 and ensure it's an array
    const formattedChunks = batchChunks.map(chunk => ({
      id: chunk.id,
      text: chunk.text,
      metadata: JSON.stringify(chunk.metadata)
    }));

    console.log(`📦 Processing batch ${i / MAX_BATCH_SIZE + 1} details:`, {
      batchSize: formattedChunks.length,
      firstChunkId: formattedChunks[0]?.id,
      lastChunkId: formattedChunks[formattedChunks.length - 1]?.id
    });

    try {
      const response = await fetch(`${apiUrl}/api/upsert`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiToken}`,
        },
        body: JSON.stringify(formattedChunks)
      });

      const responseText = await response.text();
      console.log(`📡 D1 API Response: ${response.status}`, responseText);

      if (!response.ok) {
        console.error('❌ Failed to execute D1 query:', {
          status: response.status,
          statusText: response.statusText,
          error: responseText,
          sentData: formattedChunks
        });
        throw new NonRetryableError(`❌ Failed to execute D1 query: ${response.status} ${response.statusText} - ${responseText}`);
      }

      const result = JSON.parse(responseText) as D1Response;
      if (!result.success) {
        throw new NonRetryableError(`❌ Failed to execute D1 query: ${JSON.stringify(result)}`);
      }

      console.log(`✅ Successfully processed batch ${i / MAX_BATCH_SIZE + 1}`);
    } catch (error) {
      console.error(`❌ Error processing batch ${i / MAX_BATCH_SIZE + 1}:`, error);
      throw error instanceof Error ? error : new Error(String(error));
    }
  }
}
