import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";

interface Env {
  ENVIRONMENT: string,
  R2: R2Bucket,
}

export class StorageClient {
  private client: R2Bucket | S3Client;
  private isLocal: boolean;

  constructor(env: Env){
      this.client = env.R2;
  }

  async get(key: string): Promise<ArrayBuffer | null>{
    if(this.isLocal) {
      const response = await (this.client as S3Client).send(new GetObjectCommand({
        Bucket: (this.client as any).bucket,
        Key: key,
      }));
      if(!response.Body) {
        throw new Error(`Failed to get object body for key: ${key}`);
      }
      const chunks: Uint8Array[] = [];
      const stream = response.Body as unknown as ReadableStream;
      const reader = stream.getReader();

      let reading = true;
      while(reading) {
        const { done, value } = await reader.read();
        if(done) {
          reading = false;
          break;
        }
        chunks.push(value);
      }
      return Buffer.concat(chunks).buffer;
    } else {
      const obj = await (this.client as R2Bucket).get(key);
      return obj ? await obj.arrayBuffer() : null;
    }
  }
}
