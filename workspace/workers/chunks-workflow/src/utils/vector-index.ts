export interface VectorizeApiResponse {
  result: any,
  success: boolean,
  errors: Array<{
    code: number,
    message: string,
  }>,
  messages: string[],
}

export async function findOrCreateVectorizeIndex(
  indexName: string,
  description: string,
  accountId: string,
  apiToken: string,
  apiUrl: string = "https://api.cloudflare.com/client/v4"
): Promise<void>{
  console.log(`🔍 Checking if vector index exists: ${indexName}`);
  const apiUrlFull = `${apiUrl}/accounts/${accountId}/vectorize/v2/indexes/${indexName}`;
  console.log(`🌐 Checking vector index at: ${apiUrlFull}`);
  console.log(`🌐 Checking apiToken: ${apiToken}`);

  try {
    // First, try to get the index
    const checkResponse = await fetch(
      apiUrlFull,
      {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${apiToken}`,
        },
      }
    );

    const checkResult = await checkResponse.json() as VectorizeApiResponse;
    console.log("🌐 Index check response:", JSON.stringify(checkResult, null, 2));

    // If index doesn't exist, create it
    if(!checkResult.success) {
      console.log(`🆕 Creating new vector index: ${indexName}`);

      const createResponse = await fetch(
        `${apiUrl}/accounts/${accountId}/vectorize/v2/indexes`,
        {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${apiToken}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: indexName,
            description: description,
            config: {
              dimensions: 768, // BGE base model dimensions
              metric: "cosine",
              metadata_fields: [
                { name: "text", type: "string" },
                { name: "fileName", type: "string" },
                { name: "fileId", type: "string" },
                { name: "target", type: "string" },
                { name: "processorType", type: "string" },
                { name: "timestamp", type: "string" },
                { name: "batchIndex", type: "number" },
                { name: "totalBatches", type: "number" }
              ]
            }
          })
        }
      );

      const createResult = await createResponse.json() as VectorizeApiResponse;

      if(!createResult.success) {
        throw new Error(`❌ Failed to create vector index: ${JSON.stringify(createResult)}`);
      }

      console.log(`✅ Successfully created vector index: ${indexName}`);
    } else {
      console.log(`✅ Vector index already exists: ${indexName}`);
    }
  }catch(error) {
    console.error("✘ [ERROR] ❌ Error in findOrCreateVectorizeIndex:", error);
    throw error;
  }
}
