import { DocumentProcessor } from './types';

interface UnstructuredInitResponse {
  session_id: string;
  total_chunks: number;
}

interface ProcessedChunk {
  text: string;
  metadata: {
    type?: string;
    page_number?: number;
    coordinates?: any;
    [key: string]: any;
  };
}

export interface UnstructuredConfig {
  chunkingStrategy?: 'by_title' | 'by_similarity' | 'by_page';
  maxCharacters?: number;
  splitPdfPage?: boolean;
  splitPdfConcurrencyLevel?: number;
  splitPdfAllowFailed?: boolean;
  minTokens?: number;
  maxTokens?: number;
}

export class UnstructuredProcessor implements DocumentProcessor {
  constructor(
    // private apiKey: string,
    private workerUrl: string,
    private options: { useStreaming?: boolean } = {}
  ) {}

  async initializeSession(
    config: UnstructuredConfig,
    fileUrl: string
  ): Promise<{ sessionId: string }> {
    try {
      const response = await fetch(`${this.workerUrl}/fileUrl/init`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          url: fileUrl,
          filename: fileUrl.split('/').pop() || 'document',
          config: {
            chunkingStrategy: config.chunkingStrategy || 'by_title',
            maxCharacters: config.maxCharacters || 1024,
            splitPdfPage: config.splitPdfPage ?? true,
            splitPdfConcurrencyLevel: config.splitPdfConcurrencyLevel || 5,
            splitPdfAllowFailed: config.splitPdfAllowFailed ?? true,
            minTokens: config.minTokens,
            maxTokens: config.maxTokens
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Worker initialization failed: ${errorText}`);
      }

      const { session_id, total_chunks } = await response.json() as UnstructuredInitResponse;
      console.log(`✅ Unstructured worker initialized: ${session_id} (${total_chunks} chunks)`);

      return { sessionId: session_id };
    } catch (error) {
      console.error('Failed to initialize worker session:', error);
      throw error;
    }
  }

  async processBatch(
    sessionId: string,
    batchNumber: number,
    batchSize: number = 50
  ): Promise<Array<{ text: string; metadata?: any }> | null> {
    try {
      const response = await fetch(`${this.workerUrl}/fileUrl/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          // 'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          session_id: sessionId,
          batch_number: batchNumber,
          batch_size: batchSize
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        if (response.status === 404) {
          throw new Error('Session expired');
        }
        throw new Error(`Batch processing failed: ${errorText}`);
      }

      const chunks = await response.json() as ProcessedChunk[];
      
      // Return null if no more chunks or null response
      if (!chunks) {
        return null;
      }

      return chunks.map(chunk => ({
        text: chunk.text,
        metadata: chunk.metadata
      }));
    } catch (error) {
      console.error('Failed to process batch:', error);
      throw error;
    }
  }

  async dispose(): Promise<void> {
    // Nothing to clean up when using the worker
  }
}
