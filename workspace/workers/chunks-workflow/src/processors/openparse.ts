
import { Env } from "../types";
import { DocumentProcessor, OpenParseConfig } from "./types";

interface OpenParseInitResponse {
  total_chunks: number,
  session_id: string,
}

export class OpenParseProcessor implements DocumentProcessor {
  private currentConfig?: OpenParseConfig;
  private currentFilePath?: string;
  private existingSessionId?: string;

  constructor(
    private apiKey: string,
    private apiUrl: string,
    private env: Env,
    private options: { useStreaming: boolean } = { useStreaming: true }
  ){}

  async initializeSession(
    config: OpenParseConfig,
    fileUrl: string
  ): Promise<{ sessionId: string, totalChunks: number }>{
    try {
      // Store current config and file path for potential reconnection
      this.currentConfig = config;
      this.currentFilePath = fileUrl;

      console.log("🔄 Initializing OpenParse processing config:", config);
      console.log("🔄🌐 Initializing OpenParse processing this.apiUrl:", this.apiUrl);

      // Extract embeddings provider from nested structure if needed
      const embeddingsProvider: string = config.openparse.embeddings_provider || "none";

      const response = await fetch(`${this.apiUrl}/fileUrl/init`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          url: fileUrl,
          config: {
            semantic_chunking: config.semantic_chunking || false,
            embeddings_provider: embeddingsProvider,
            minTokens: config.openparse.minTokens || 525,
            maxTokens: config.openparse.maxTokens || 1056,
            overlap: config.openparse.chunkOverlap || 85,
            useTokens: config.openparse.useTokens !== false,
          }
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        console.error("❌ OpenParse init failed:", {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });
        throw new Error(`OpenParse init failed: ${response.status} - ${errorText}`);
      }

      const initResponse = await response.json() as OpenParseInitResponse;
      console.log("✅ OpenParse init successful:", initResponse);
      return {
        sessionId: initResponse.session_id,
        totalChunks: initResponse.total_chunks
      };
    }catch(error) {
      console.error("Failed to initialize OpenParse session:", error);
      throw error;
    }
  }

  async processBatch(
    sessionId: string,
    batchNumber: number,
    batchSize: number
  ): Promise<Array<{ text: string, metadata?: any }> | null>{
    try {
      console.log(`📦 Fetching batch ${batchNumber} with size ${batchSize}`);

      const response = await fetch(`${this.apiUrl}/fileUrl/batch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          session_id: sessionId,
          batch_number: batchNumber,
          batch_size: batchSize
        })
      });

      if(!response.ok) {
        const errorText = await response.text();
        console.error(`❌ Batch fetch failed: ${response.status} - ${errorText}`);

        if(response.status === 404) {
          throw new Error("Session expired");
        }

        throw new Error(`Failed to fetch batch: ${response.status} - ${errorText}`);
      }

      const chunks = await response.json();

      if(!Array.isArray(chunks)) {
        console.error("❌ Invalid response format:", chunks);
        throw new Error("Invalid batch response format");
      }

      // Return null if no chunks (end of processing)
      if(chunks.length === 0) {
        return null;
      }

      console.log(`✅ Successfully fetched ${chunks.length} chunks in batch ${batchNumber}`);
      return chunks;

    }catch(error) {
      console.error(`❌ Error in processBatch ${batchNumber}:`, error);
      throw error;
    }
  }

  async *processInBatches(
    config: OpenParseConfig,
    fileUrl: string,
    batchSize: number = 50
  ): AsyncGenerator<Array<{ text: string, metadata?: any }>>{
    const initData = await this.initializeProcessing(config, fileUrl);

    // Create transform stream for processing chunks
    const { readable, writable } = new TransformStream({
      transform(chunk, controller){
        // Parse JSON chunk and enqueue
        try {
          const parsedChunk = JSON.parse(chunk);
          controller.enqueue(parsedChunk);
        }catch(error) {
          console.error("Failed to parse chunk:", error);
        }
      }
    });

    if(this.options.useStreaming) {
      // Stream response from OpenParse API
      const response = await fetch(`${this.apiUrl}/fileUrl/stream`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          session_id: initData.session_id,
          batch_size: batchSize
        })
      });

      // Start streaming response
      response.body?.pipeTo(writable);

      // Yield chunks as they arrive
      const reader = readable.getReader();
      while(true) {
        const { done, value } = await reader.read();
        if(done) break;
        yield value;
      }
    } else {
      // Fallback to batch processing
      for(let offset = 0; offset < initData.total_chunks; offset += batchSize) {
        const batchNumber = Math.floor(offset / batchSize);
        const batchChunks = await this.fetchBatch(
          initData.session_id,
          batchNumber,
          config,
          fileUrl
        );
        yield batchChunks;
      }
    }
  }

  async process(
    config: OpenParseConfig,
    fileUrl: string
  ): Promise<Array<{ text: string, metadata?: any }>>{
    try {
      console.log("🔄 OpenParse processing file:", fileUrl);

      // Use generator to process chunks in batches
      const chunks: Array<{ text: string, metadata?: any }> = [];
      for await (const batch of this.processInBatches(config, fileUrl)) {
        chunks.push(...batch);

        // If we've accumulated too many chunks, yield them and reset
        if(chunks.length >= 50) {
          const batchToReturn = chunks.splice(0, 50);
          return batchToReturn;
        }
      }

      // Return any remaining chunks
      return chunks;
    }catch(error) {
      console.error("Failed to process file:", error);
      throw error;
    }
  }

  async dispose(): Promise<void>{
    return Promise.resolve();
  }

  private async initializeProcessing(
    config: OpenParseConfig,
    fileUrl: string
  ): Promise<OpenParseInitResponse>{
    // Add debug logging
    console.log("🔄 Initializing OpenParse processing:", {
      apiUrl: this.apiUrl,
      fileUrl,
      config,
    });

    const publicFileUrl = `${this.env.R2_BUCKET_URL}/${fileUrl}`;
    const originalFileName = fileUrl.split("/").pop() || "";

    const requestBody = {
      url: publicFileUrl,
      filename: originalFileName,
      config: {
        minTokens: config.openparse?.minTokens || 256,
        maxTokens: config.openparse?.maxTokens || 1024,
        semantic_chunking: config.semantic_chunking || false,
        embeddings_provider: config?.openparse?.embeddings_provider || "none",
        batch_size: 50
      }
    };

    console.log("📤 Sending init request:", {
      url: `${this.apiUrl}/fileUrl/init`,
      body: requestBody
    });

    const initialResponse = await fetch(`${this.apiUrl}/fileUrl/init`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody)
    });

    if(!initialResponse.ok) {
      const errorText = await initialResponse.text();
      console.error("❌ OpenParse init failed:", {
        status: initialResponse.status,
        statusText: initialResponse.statusText,
        error: errorText,
        requestBody
      });
      throw new Error(`OpenParse init failed: ${initialResponse.status} - ${errorText}`);
    }

    const response = await initialResponse.json() as OpenParseInitResponse;
    console.log("✅ OpenParse init successful:", response);
    return response;
  }

  private async fetchBatch(
    sessionId: string,
    batchNumber: number,
    config: OpenParseConfig,
    fileUrl: string
  ): Promise<any[]>{
    console.log(`📦 Fetching batch ${batchNumber}`);

    const response = await fetch(`${this.apiUrl}/fileUrl/batch`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        session_id: sessionId,
        batch_number: batchNumber
      })
    });

    if(!response.ok) {
      if(response.status === 404) {
        // Session expired, retry initialization
        console.log("🔄 Session expired, reinitializing...");
        const newSession = await this.initializeProcessing(config, fileUrl);
        return this.fetchBatch(newSession.session_id, batchNumber, config, fileUrl);
      }
      const errorText = await response.text();
      throw new Error(`Failed to fetch batch: ${response.status} - ${errorText}`);
    }

    return response.json();
  }
}
