
export interface OpenParseConfig {
  semantic_chunking?: boolean,
  skipDeJunk?: boolean,
  relevanceThreshold?: number,
  openparse?: {
    embeddings_provider?: string,
    useTokens?: boolean,
    minTokens?: number,
    maxTokens?: number,
    chunkOverlap?: number,
  },
}

export interface DocumentProcessor {
  initializeSession(config: ProcessorConfig, filePath: string): Promise<{ sessionId: string }>,
  processBatch(sessionId: string, batchNumber: number, batchSize: number): Promise<Array<{ text: string, metadata?: any }>>,
  dispose(): Promise<void>,
}

export interface ProcessorConfig {
  minTokens?: number,
  maxTokens?: number,
  semantic_chunking?: boolean,
  embeddings_provider?: string,
  skipDeJunk?: boolean,
}
