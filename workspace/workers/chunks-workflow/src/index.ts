import type { Context } from "hono";
import { Hono } from "hono";
import { cors } from "hono/cors";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { ChunksVectorizedWorkflow } from "./workflows/chunks-vectorized";
import { VideoToAudioWorkflow } from "./workflows/video-to-audio";
import { Env } from "./types";

export function createCloudflareExecutionContext(honoCtx: Context["executionCtx"]): ExecutionContext{
  return {
    waitUntil: (promise: Promise<any>)=>honoCtx.waitUntil(promise),
    passThroughOnException: ()=>honoCtx.passThroughOnException(),
    props: {}, // Required by Cloudflare"s ExecutionContext
    exports: {}, // Required by Cloudflare"s ExecutionContext
    abort: ()=>{} // Required by Cloudflare"s ExecutionContext
  };
}

// Create the Hono app with the correct environment type
type AppEnv = {
  Bindings: Env & {
    [key: string]: unknown, // Add index signature
  },
};

const app = new Hono<AppEnv>();

// Create a CORS middleware handler function
const createCorsMiddleware = (c: Context)=>{
  return cors({
    origin: (origin)=>{
      const allowedOrigins = c.env.ALLOWED_ORIGINS.split(",");

      if(!origin) return "*";

      // Convert wildcard pattern to regex pattern
      const matchesPattern = allowedOrigins.some(allowed=>{
        if(allowed.includes("*")) {
          // Convert the wildcard pattern to a proper regex
          // e.g., "https://*.stage.divinci.app" becomes "^https:\/\/[^\/]*\.stage\.divinci\.app$"
          const regexPattern = allowed
            .replace(/\./g, "\\.")
            .replace(/\*/g, "[^\\/]*");
          const pattern = new RegExp(`^${regexPattern}$`);
          return pattern.test(origin);
        }
        return allowed === origin;
      });

      return matchesPattern ? origin : "";
    },
    allowMethods: ["POST", "GET", "OPTIONS", "HEAD"],
    allowHeaders: [
      "Content-Type",
      "Authorization",
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-processor-config",
      "x-vectorize-config",
      "cloudflare-worker-x-dev-auth",
      "divinci-organization",
    ],
    exposeHeaders: [
      "x-file-name",
      "x-file-id",
      "x-target",
      "x-processor",
      "x-processor-config",
      "x-vectorize-config",
      "divinci-organization",
    ],
    maxAge: 600,
    credentials: true,
  });
};

// Use the middleware with context
app.use("*", async (c, next)=>{
  const corsMiddleware = createCorsMiddleware(c);
  return corsMiddleware(c, next);
});

// Add explicit OPTIONS handler for preflight requests
app.options("*", (c)=>{
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": c.req.header("Origin") || "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS, HEAD",
      "Access-Control-Max-Age": "600",
      "Access-Control-Allow-Headers": c.req.header("Access-Control-Request-Headers") || "*"
    }
  });
});

// Add a middleware to ensure proper Content-Type
app.use("*", async (c, next)=>{
  if(c.req.method === "POST") {
    c.header("Content-Type", "application/json");
  }
  await next();
});

// Define the validation schema for single file processing
const processDocumentSchema = z.object({
  files: z.array(z.object({
    fileId: z.string(),
    target: z.string(),
    fileName: z.string(),
    bucket: z.string(),
    objectKey: z.string(),
    processor: z.enum(["unstructured", "openparse"])
      .default("unstructured")
      .transform((val)=>{
        console.log("🔄 Processor selected:", val);
        return val;
      }),
    processorConfig: z.object({
      semantic_chunking: z.boolean().optional(),
      embeddings_provider: z.enum(["cloudflare", "ollama", "none"]).optional(),
      minTokens: z.number().optional(),
      maxTokens: z.number().optional(),
      relevanceThreshold: z.number().optional().default(0.3),
      skipDeJunk: z.boolean().optional(),
      disableStreaming: z.boolean().optional().default(false),
      unstructured: z.object({
        chunkingStrategy: z.enum(["by_title", "by_similarity", "by_page", "by_character"]).optional(),
        maxCharacters: z.number().optional(),
        similarityThreshold: z.number().optional(),
        includeOriginalElements: z.boolean().optional(),
        multipageSections: z.boolean().optional()
      }).optional()
    }).optional(),
    fileData: z.object({
      base64: z.string(),
      type: z.string(),
      size: z.number()
    }).optional(),
    title: z.string().optional(),
    description: z.string().optional()
  })).min(1),
  vectorizeConfig: z.object({
    accountId: z.string(),
    apiToken: z.string(),
    ragName: z.string(),
    ragId: z.string(),
    whitelabelId: z.string(),
    auth0Token: z.string()
  }),
  isBatch: z.boolean().optional()
}).strict();

// Define the batch processing schema
const batchProcessSchema = z.object({
  files: z.array(z.object({
    fileId: z.string(),
    target: z.string(),
    fileName: z.string(),
    bucket: z.string(),
    objectKey: z.string(),
    processor: z.enum(["unstructured", "openparse"]).default("unstructured"),
    processorConfig: z.object({
      semantic_chunking: z.boolean().optional(),
      embeddings_provider: z.enum(["cloudflare", "ollama", "none"]).optional(),
      minTokens: z.number().optional(),
      maxTokens: z.number().optional(),
      relevanceThreshold: z.number().optional().default(0.3),
      skipDeJunk: z.boolean().optional(),
      unstructured: z.object({
        chunkingStrategy: z.enum(["by_title", "by_similarity", "by_page", "by_character"]).optional(),
        maxCharacters: z.number().optional(),
        similarityThreshold: z.number().optional(),
        includeOriginalElements: z.boolean().optional(),
        multipageSections: z.boolean().optional()
      }).optional()
    }).optional(),
    file: z.any().optional(),
    title: z.string().optional(),
    description: z.string().optional()
  })),
  vectorizeConfig: z.object({
    accountId: z.string(),
    apiToken: z.string(),
    ragName: z.string(),
    ragId: z.string(),
    whitelabelId: z.string(),
    auth0Token: z.string()
  }),
  isBatch: z.boolean().optional()
}).strict();

// Define the routes
app.post("/api/process", zValidator("json", processDocumentSchema), async (c)=>{
  const payload = c.req.valid("json");
  const file = payload.files[0];

  try {
    // Create a unique instance ID with proper formatting
    const instanceId = `wf-${file.target}-${file.fileId}-${crypto.randomUUID()}`;

    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId, // Add the id field here
      params: {
        files: [{
          fileId: file.fileId,
          target: file.target,
          fileName: file.fileName,
          bucket: file.bucket,
          objectKey: file.objectKey,
          processor: file.processor || "unstructured",
          processorConfig: file.processorConfig,
          title: file.title,
          description: file.description
        }],
        vectorizeConfig: {
          accountId: payload.vectorizeConfig.accountId,
          apiToken: payload.vectorizeConfig.apiToken,
          ragName: payload.vectorizeConfig.ragName,
          ragId: payload.vectorizeConfig.ragId,
          whitelabelId: payload.vectorizeConfig.whitelabelId,
          auth0Token: payload.vectorizeConfig.auth0Token
        },
        timestamp: new Date(), // Create a new Date object for the current timestamp
        instanceId
      }
    });

    return c.json({ success: true, workflowId: instanceId });
  }catch(error) {
    console.error("Workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/process/batch", zValidator("json", batchProcessSchema), async (c)=>{
  const payload = c.req.valid("json");

  try {
    // Create a unique instance ID with proper formatting
    const instanceId = `wf-batch-${crypto.randomUUID()}`;

    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId, // Add the id field here
      params: {
        files: payload.files.map(file=>({
          fileId: file.fileId,
          target: file.target,
          fileName: file.fileName,
          bucket: file.bucket,
          objectKey: file.objectKey,
          processor: file.processor || "unstructured",
          processorConfig: file.processorConfig,
          title: file.title,
          description: file.description
        })),
        vectorizeConfig: {
          accountId: payload.vectorizeConfig.accountId,
          apiToken: payload.vectorizeConfig.apiToken,
          ragName: payload.vectorizeConfig.ragName,
          ragId: payload.vectorizeConfig.ragId,
          whitelabelId: payload.vectorizeConfig.whitelabelId,
          auth0Token: payload.vectorizeConfig.auth0Token
        },
        timestamp: new Date(), // Create a new Date object for the current timestamp
        instanceId
      }
    });

    return c.json({
      success: true,
      workflowId: instanceId
    });
  }catch(error) {
    console.error("Batch workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/process/stream", async (c)=>{
  // Get metadata from headers
  const fileName = c.req.header("x-file-name");
  const fileId = c.req.header("x-file-id");
  const target = c.req.header("x-target");
  const processor = c.req.header("x-processor");
  const processorConfigStr = c.req.header("x-processor-config");
  const vectorizeConfigStr = c.req.header("x-vectorize-config");

  // Add debug logging
  console.log("📝 Processing stream request:", {
    fileName,
    fileId,
    target,
    processor: processor || "default",
    processorConfig: processorConfigStr ? JSON.parse(processorConfigStr) : null
  });

  // Parse configs with error handling
  let processorConfig;
  try {
    processorConfig = processorConfigStr ? JSON.parse(processorConfigStr) : {};
    console.log("Parsed processorConfig:", processorConfig);  // Debug log
  }catch(error) {
    console.error("Failed to parse processor config:", error);
    return c.json({ error: "Invalid processor config" }, 400);
  }

  // Create a cleaner, more predictable instance ID
  const cleanFileId = fileId.replace(/[^a-zA-Z0-9-]/g, "").substring(0, 20); // Sanitize and truncate fileId
  const shortUUID = crypto.randomUUID().replace(/-/g, "").substring(0, 8); // Get first 8 chars of UUID
  const instanceId = `wf-${target}-${shortUUID}`; // More concise format

  console.log("🔍 Creating workflow instance:", {
    instanceId,
    originalFileId: fileId,
    cleanFileId,
    target,
    processor: processor || "default"
  });

  if(!fileName || !fileId || !target || !processor || !vectorizeConfigStr) {
    return c.json({ error: "Missing required headers" }, 400);
  }

  try {
    let fileData;

    // Check if the content type is multipart/form-data
    const contentType = c.req.header("content-type") || "";
    if(contentType.includes("multipart/form-data")) {
      // Handle FormData
      const formData = await c.req.formData();
      const file = formData.get("file");

      if(!file || !(file instanceof File)) {
        return c.json({ error: "Missing file in form data" }, 400);
      }

      fileData = await file.arrayBuffer();
    } else {
      // Handle JSON body (original implementation)
      const [bodyStream1, bodyStream2] = c.req.raw.body?.tee() || [];

      // Parse the JSON metadata from the first stream
      const bodyText = await new Response(bodyStream1).text();
      let bodyData;
      try {
        bodyData = JSON.parse(bodyText);
        console.log("🔍 API - Body data:", {
          processorConfig: bodyData?.processorConfig,
          size: bodyText.length,
          keys: Object.keys(bodyData || {}),
          fileData: bodyData?.fileData ? {
            type: bodyData.fileData.type,
            size: bodyData.fileData.size,
            base64Length: bodyData.fileData.base64?.length || 0
          } : undefined
        });

        // Get file data from base64
        if(bodyData?.fileData?.base64) {
          const base64Data = bodyData.fileData.base64;
          const binaryData = Buffer.from(base64Data, "base64");
          fileData = binaryData.buffer;
        } else {
          // Get the file data from the second stream
          fileData = await new Response(bodyStream2).arrayBuffer();
        }

        processorConfig = bodyData?.processorConfig;
      }catch(e) {
        console.log("🔍 Not a JSON body, treating as raw file");
        fileData = await new Response(bodyStream2).arrayBuffer();
      }
    }

    const vectorizeConfig = JSON.parse(vectorizeConfigStr);
    const objectKey = `${target}/${fileId}/${Date.now()}-${fileName}`;

    // Log only essential information
    console.log("🚀 Creating workflow: ", {
      instanceId,
      fileName,
      processor,
      objectKey
    });

    // Upload to R2
    await c.env.R2.put(objectKey, fileData, {
      httpMetadata: {
        contentType: c.req.header("content-type") || "application/octet-stream",
      },
      customMetadata: {
        fileName,
        uploadedAt: new Date().toISOString()
      }
    });

    // Create workflow with validated instance ID - IMPORTANT: Don"t pass fileData to workflow
    const workflow = await c.env.CHUNKS_VECTORIZED.create({
      id: instanceId,
      params: {
        files: [{
          fileId: cleanFileId,
          target,
          fileName,
          bucket: "default",
          objectKey,
          processor: (processor === "unstructured" || processor === "openparse")
            ? processor
            : "unstructured", // Default to unstructured if invalid value
          processorConfig: processorConfig ? {
            semantic_chunking: processorConfig.semantic_chunking,
            embeddings_provider: processorConfig.embeddings_provider,
            minTokens: processorConfig.minTokens,
            maxTokens: processorConfig.maxTokens,
            relevanceThreshold: processorConfig.relevanceThreshold,
            skipDeJunk: processorConfig.skipDeJunk,
          } : undefined,
          // Don't include fileData here to avoid RPC size limit
        }],
        vectorizeConfig,
        timestamp: new Date(),
        instanceId
      }
    });

    return c.json({
      id: workflow.id,
      success: true,
      objectKey
    });

  }catch(error) {
    console.error("Stream processing error:", {
      error: error.message,
      instanceId,
      fileId: cleanFileId,
      target
    });
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

// Endpoint to receive logs from tail worker
app.post("/api/logs/chunks-workflow", async (c)=>{
  try {
    const events = await c.req.json();

    // Process the workflow events
    for(const event of events) {
      const {
        scriptName,
        outcome,
        eventTimestamp,
        logs,
        exceptions
      } = event;

      // Option 1: Store in dedicated LOGS_DB
      await c.env.LOGS_DB.prepare(`
        INSERT INTO workflow_logs (
          script_name,
          outcome,
          timestamp,
          logs,
          exceptions
        ) VALUES (?, ?, ?, ?, ?)
      `).bind(
        scriptName,
        outcome,
        new Date(eventTimestamp).toISOString(),
        JSON.stringify(logs),
        JSON.stringify(exceptions)
      ).run();
    }

    return c.json({ success: true });
  }catch(error) {
    console.error("Failed to process workflow logs:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

app.post("/api/video-to-audio", async (c)=>{
  try {
    const payload = await c.req.json();
    const { videoFile, instanceId, whitelabelId, auth0Token } = payload;

    if(!videoFile || !instanceId || !whitelabelId || !auth0Token) {
      return c.json({
        success: false,
        error: "Missing required fields in payload"
      }, 400);
    }

    console.log("🎬 Starting video-to-audio workflow:", {
      instanceId,
      fileId: videoFile.fileId,
      fileName: videoFile.fileName
    });

    // Create the workflow with the provided instance ID
    const workflow = await c.env.VIDEO_TO_AUDIO.create({
      id: instanceId,
      params: {
        videoFile,
        instanceId,
        whitelabelId,
        auth0Token
      }
    });

    return c.json({
      success: true,
      workflowId: instanceId
    });
  }catch(error) {
    console.error("Video-to-audio workflow error:", error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, 500);
  }
});

export { ChunksVectorizedWorkflow, VideoToAudioWorkflow };

export default app;
