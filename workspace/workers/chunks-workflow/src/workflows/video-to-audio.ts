
import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers";
import { NonRetryableError } from "cloudflare:workflows";
import { Env, VideoToAudioEvent } from "../types";

export class VideoToAudioWorkflow extends WorkflowEntrypoint<Env, VideoToAudioEvent> {
  constructor(ctx: ExecutionContext, protected readonly env: Env){
    super(ctx, env);
  }

  async setup(): Promise<void>{
    try {
      console.log("Setting up video-to-audio workflow");
    }catch(error) {
      console.error("❌ Error setting up video-to-audio workflow:", error);
      throw error;
    }
  }

  async run(event: WorkflowEvent<VideoToAudioEvent>, step: WorkflowStep): Promise<void>{
    const { videoFile, whitelabelId, auth0Token } = event.payload;

    // Step 1: Validate and get R2 file metadata
    const r2Result = await step.do("validateAndGetR2File", async ()=>{
      const existingFile = await this.env.R2.get(videoFile.objectKey);

      // Instead of throwing an error, just return metadata with hasExistingFile flag
      console.log("1️⃣ step('validateAndGetR2File')::objectKey:", videoFile.objectKey);
      console.log("1️⃣ step('validateAndGetR2File')::existingFile metadata:", {
        key: existingFile?.key,
        size: existingFile?.size,
        contentType: existingFile?.httpMetadata?.contentType,
        customMetadata: existingFile?.customMetadata
      });

      return {
        objectKey: videoFile.objectKey,
        fileName: videoFile.fileName,
        hasExistingFile: !!existingFile,
        size: existingFile?.size || 0,
        mimeType: existingFile?.httpMetadata?.contentType || "video/mp4",
        customMetadata: existingFile?.customMetadata || {}
      };
    });

    // Step 1.5: Upload file to R2 if it doesn't exist
    if(!r2Result.hasExistingFile) {
      await step.do("uploadToR2", {
        retries: {
          limit: 3,
          delay: "5 seconds",
          backoff: "exponential"
        },
        timeout: "10 minutes"
      }, async ()=>{
        // Fetch the file from the source
        console.log(`📥 Fetching video file from source for ${videoFile.fileId}`);

        const response = await fetch(`${this.env.API_HOST}/white-label/${whitelabelId}/data-source/audio-transcript/download/${videoFile.fileId}`, {
          headers: {
            "Authorization": `Bearer ${auth0Token}`
          }
        });

        if(!response.ok) {
          throw new Error(`Failed to fetch video file: ${response.statusText}`);
        }

        const fileBuffer = await response.arrayBuffer();

        // Upload to R2
        console.log(`📤 Uploading video file to R2: ${r2Result.objectKey}, size: ${fileBuffer.byteLength} bytes`);

        await this.env.R2.put(r2Result.objectKey, fileBuffer, {
          httpMetadata: {
            contentType: "video/mp4",
          },
          customMetadata: {
            fileName: r2Result.fileName,
            uploadedAt: new Date().toISOString(),
            size: fileBuffer.byteLength.toString()
          }
        });

        console.log(`✅ Successfully uploaded video file to R2: ${r2Result.objectKey}`);
        return { uploaded: true, size: fileBuffer.byteLength };
      });
    }

    // Step 2: Extract audio from video using ffmpeg
    const audioResult = await step.do("extractAudioFromVideo", {
      retries: {
        limit: 3,
        delay: "5 seconds",
        backoff: "exponential"
      },
      timeout: "10 minutes"
    }, async ()=>{
      // Generate a unique audio file key
      const audioKey = `${whitelabelId}/audio/${videoFile.fileId}.mp3`;

      // Use ffmpeg to extract audio
      // Note: In a real implementation, you would call a worker or service that runs ffmpeg
      // This is a placeholder for the actual implementation
      const audioExtractionResult = await this.callFfmpegService(
        r2Result.objectKey,
        audioKey,
        r2Result.mimeType,
        auth0Token,
      );

      if(!audioExtractionResult.success) {
        throw new Error(`Failed to extract audio: ${audioExtractionResult.error}`);
      }

      return {
        audioKey,
        duration: audioExtractionResult.duration,
        format: "mp3"
      };
    });

    // Step 3: Update file record with audio information
    await step.do("updateFileRecord", async ()=>{
      const updateResponse = await fetch(
        `${this.env.API_HOST}/white-label/${whitelabelId}/data-source/audio-transcript/update-audio-file`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`,
          },
          body: JSON.stringify({
            fileId: videoFile.fileId,
            audioKey: audioResult.audioKey,
            duration: audioResult.duration,
            status: "COMPLETED"
          })
        }
      );

      if(!updateResponse.ok) {
        const error = await updateResponse.json();
        throw new Error(`Failed to update file record: ${JSON.stringify(error)}`);
      }

      console.log(`✅ Successfully processed video file ${videoFile.fileId}`);
      return await updateResponse.json();
    });
  }

  // This would call a separate worker that handles ffmpeg processing
  private async callFfmpegService(
    videoKey: string, audioKey: string,
    mimeType: string, auth0Token: string,
  ): Promise<{
    success: boolean,
    duration?: number,
    error?: string,
  }>{
    try {
      const response = await fetch(`${this.env.FFMPEG_WORKER_URL}/extract-audio`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`
        },
        body: JSON.stringify({
          videoKey,
          audioKey,
          mimeType
        })
      });

      if(!response.ok) {
        const error = await response.json();
        return { success: false, error: JSON.stringify(error) };
      }

      // Define the expected response type
      interface FfmpegResponse {
        duration: number,
        [key: string]: any,
      }

      const result = await response.json() as FfmpegResponse;
      return {
        success: true,
        duration: result.duration
      };
    }catch(error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}
