import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers";
import { NonRetryableError } from "cloudflare:workflows";
import { findOrCreateVectorizeIndex } from "../utils/vector-index";
import { WorkflowMetadata } from "src/types/workflow";
import { createVectorId, countTokens } from "../utils";
import { ensureChunksTable } from "../utils/d1-api";
import {
  Env, ChunksVectorizedEvent, FileRecordPayload,
  FileRecord, R2ValidationResult } from "../types";
import { VectorizeAPI } from "../utils/vectorize-api";
import { UnstructuredProcessor } from "../processors/unstructured";
import { OpenParseProcessor } from "../processors/openparse";

const FILTER_BATCH_SIZE = 100; // For filtering batches
const EMBEDDING_BATCH_SIZE = 50; // For embedding generation batches

enum RagVectorTextChunksStatus {
  CHUNKING = "file to chunks",
  FAILED_TO_CHUNK = "failed to chunk",
  EDITING = "editing chunks",
  STORING = "chunks to storage",
  VECTORIZING = "chunks to vectors",
  FAILED_TO_VECTORIZE = "failed to vectorize",
  COMPLETED = "completed",
}

function getTableName(whitelabelId: string): string{
  // Use a consistent table name format
  return `d1_chunks_${whitelabelId}`;
}

//📓 relevanceThreshold used for Dejunking
function getRelevanceThreshold(processorConfig?: {
  relevanceThreshold?: number,
}): number{
  // Default threshold if not specified
  const DEFAULT_THRESHOLD = 0.3;

  // Return the configured threshold or default
  return processorConfig?.relevanceThreshold ?? DEFAULT_THRESHOLD;
}

interface FileRecordResponse {
  status: "success" | "error",
  data?: {
    _id: string,
    bucket: string,
    objectKey: string,
    originalName: string,
    [key: string]: any,
  },
  message?: string,
}

interface ProcessedChunk {
  id: string,
  text: string,
  metadata: {
    whiteLabelId: string,
    originalName: string,
    vectorId: string,
    fileObjectKey: string,
    tokenCount?: number,
    tags?: string[],
    [key: string]: any,
  },
}

export interface VectorizationJob {
  chunks: ProcessedChunk[],
  vectorIndex: string,
  fileId: string,
}

export class ChunksVectorizedWorkflow extends WorkflowEntrypoint<Env, ChunksVectorizedEvent> {
  private fileRecord: FileRecordResponse["data"] | null = null;

  constructor(ctx: ExecutionContext, protected readonly env: Env){
    super(ctx, env);
  }

  private async storeChunksInD1(
    chunks: ProcessedChunk[],
    fileInfo: {
      fileId: string,
      target?: string,
      fileName: string,
      bucket: string,
      objectKey: string,
    },
    whitelabelId: string
  ): Promise<void>{
    if(!chunks || chunks.length === 0) {
      throw new Error("❌ No chunks provided for storage");
    }

    const batchSize = 20;
    const tableName = getTableName(whitelabelId);
    console.log(`📊 Storing ${chunks.length} chunks in batches of ${batchSize}`);
    console.log(`🔍 Using table: ${tableName}`);

    for(let i = 0; i < chunks.length; i += batchSize) {
      const batch = chunks.slice(i, i + batchSize);
      if(!batch.length) {
        console.warn(`⚠️ Empty batch encountered at index ${i}`);
        continue;
      }

      console.log(`📝 Attempting to store ${batch.length} records in batch ${i / batchSize + 1}`);

      let attempts = 0;
      const maxAttempts = 3;

      while(attempts < maxAttempts) {
        try {
          // Prepare the SQL statement with placeholders
          const placeholders = batch.map(()=>"(?, ?, ?)").join(",");
          const sql = `
            INSERT OR REPLACE INTO ${tableName} (id, text, metadata)
            VALUES ${placeholders}
          `;

          // Prepare the values array for binding
          const values = [];
          for(const chunk of batch) {
            // Validate each chunk"s required fields
            if(!chunk.id || typeof chunk.text !== "string" || !chunk.metadata) {
              console.error("❌ Invalid chunk detected:", {
                id: !!chunk.id,
                textType: typeof chunk.text,
                hasMetadata: !!chunk.metadata
              });
              throw new Error("Chunk text must be a string");
            }

            // Add values in the correct order
            values.push(
              chunk.id,                          // id
              chunk.text.trim() || "",           // text (ensure not empty)
              JSON.stringify(chunk.metadata)     // metadata
            );
          }

          // Validate the final values array
          const expectedLength = batch.length * 3; // 3 values per chunk
          if(values.length !== expectedLength) {
            console.error("❌ Invalid values array:", {
              expected: expectedLength,
              actual: values.length,
              batchSize: batch.length
            });
            throw new Error(`Invalid values array length: expected ${expectedLength}, got ${values.length}`);
          }

          // Additional validation for null/undefined
          const invalidValues = values.map((val, idx)=>({
            index: idx,
            valid: val !== null && val !== undefined
          })).filter(v=>!v.valid);

          if(invalidValues.length > 0) {
            console.error("❌ Null or undefined values detected at indices:", invalidValues);
            throw new Error("Cannot insert null or undefined values into D1");
          }

          // Log the SQL and values for debugging - TRIM THIS DOWN
          console.log("🔍 Preparing SQL:", {
            sql: sql.substring(0, 100) + "...", // Truncate SQL
            valueCount: values.length,
            firstChunkPreview: {
              id: values[0],
              textLength: values[1]?.length || 0,
              metadataLength: values[2]?.length || 0
            }
          });

          const stmt = await this.env.DB.prepare(sql);
          if(!stmt) {
            throw new Error("Failed to prepare SQL statement");
          }

          // Execute the batch insert
          const result = await stmt.bind(...values).run();

          console.log(`✅ Successfully stored batch ${i / batchSize + 1}`, {
            success: result.success,
            meta: result.meta
          });
          break; // Success, exit retry loop

        }catch(error) {
          attempts++;
          console.error(`❌ Attempt ${attempts} failed:`, error);
          if(attempts === maxAttempts) {
            throw error;
          }
          console.warn(`⚠️ Retry ${attempts}/${maxAttempts} for batch ${i / batchSize + 1}`);
          await new Promise(resolve=>setTimeout(resolve, 1000 * attempts));
        }
      }
    }
  }

  private async ensureWorkflowMetadataTable(): Promise<void>{
    try {
      await this.env.DB.prepare(`
        CREATE TABLE IF NOT EXISTS workflow_metadata (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workflow_id TEXT NOT NULL,
          file_id TEXT NOT NULL,
          metadata TEXT NOT NULL,
          error TEXT,
          created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(workflow_id, file_id)
        )
      `).run();

      // Create indexes if they don"t exist
      await this.env.DB.prepare(`
        CREATE INDEX IF NOT EXISTS idx_workflow_metadata_workflow_id 
        ON workflow_metadata(workflow_id)
      `).run();

      await this.env.DB.prepare(`
        CREATE INDEX IF NOT EXISTS idx_workflow_metadata_file_id 
        ON workflow_metadata(file_id)
      `).run();

      console.log("✅ Workflow metadata table and indexes ensured");
    }catch(error) {
      console.error("❌ Error ensuring workflow metadata table:", error);
      throw error;
    }
  }

  async run(event: WorkflowEvent<ChunksVectorizedEvent>, step: WorkflowStep): Promise<void>{
    const { files, vectorizeConfig, instanceId } = event.payload;

    // Step 0: Normalize processor config and initialize metadata
    const initialSetup = await step.do("initializeWorkflow", async ()=>{
      const file = files[0]; // Extract the file from the event payload

      // Add debug logging for processor config
      console.log("Workflow processor config:", {
        fileId: file.fileId,
        processorConfig: file.processorConfig,
      });

      // Validate and normalize processor config
      const normalizeProcessorConfig = (processor: string, config: any)=>{
        if(processor === "unstructured") {
          return {
            skipDeJunk: config?.skipDeJunk ?? true,
            chunkingStrategy: config?.chunkingStrategy || "by_title",
            maxCharacters: config?.maxCharacters || 1024,
            splitPdfPage: config?.splitPdfPage ?? true,
            splitPdfConcurrencyLevel: config?.splitPdfConcurrencyLevel || 5,
            splitPdfAllowFailed: config?.splitPdfAllowFailed ?? true,
            minTokens: config?.minTokens,
            maxTokens: config?.maxTokens
          };
        } else if(processor === "openparse") {
          // Normalize OpenParse config
          return {
            skipDeJunk: config?.skipDeJunk ?? true,
            semantic_chunking: config?.semantic_chunking ?? false,
            embeddings_provider: config?.embeddings_provider || "none",
            openparse: {
              semantic: config?.openparse?.semantic ?? config?.semantic_chunking ?? false,
              useTokens: config?.openparse?.useTokens ?? true,
              embeddings: {
                provider: config?.openparse?.embeddings?.provider || config?.embeddings_provider || "none"
              },
              minTokens: config?.openparse?.minTokens || config?.minTokens || 256,
              maxTokens: config?.openparse?.maxTokens || config?.maxTokens || 1024,
              chunkOverlap: config?.openparse?.chunkOverlap || 125,
            }
          };
        }
        return config;
      };

      const normalizedConfig = normalizeProcessorConfig(
        file.processor,
        file.processorConfig
      );

      // Initialize metadata
      const metadata = this.initializeMetadata({
        ...file,
        processorConfig: normalizedConfig
      });

      // Extract all necessary file information
      const fileInfo = {
        fileId: file.fileId,
        processor: file.processor,
        objectKey: file.objectKey,
        fileName: file.fileName,
        title: file.title,
        description: file.description,
        target: file.target,
        bucket: file.bucket, // Add the bucket property
        // Don't include formData directly as it's not serializable
        hasFormData: !!file.formData
      };

      return {
        normalizedConfig,
        metadata,
        fileInfo
      };
    });

    // Step 1: Ensure D1 table
    const ensureD1Table = await step.do("ensureD1Table", async ()=>{
      console.log("🪲 Debug - Cloudflare credentials:", {
        accountId: this.env.CLOUDFLARE_ACCOUNT_ID,
        apiTokenLength: this.env.CLOUDFLARE_API_TOKEN?.length,
      });

      await Promise.all([
        this.ensureWorkflowMetadataTable(),
        ensureChunksTable(vectorizeConfig.whitelabelId, this.env.DB)
      ]);
    });

    // Step 1: Validate and get R2 file metadata only
    const r2Result: R2ValidationResult = await step.do("validateAndGetR2File", async ()=>{
      const { objectKey, fileName } = initialSetup.fileInfo;
      // Access formData directly from the event payload if needed
      const formData = files[0].formData;
      const existingFile = await this.env.R2.get(objectKey);

      if(!existingFile && !formData) {
        throw new Error("❌ No file data provided for upload");
      }

      console.log("1️⃣ step('validateAndGetR2File')::objectKey:", objectKey);
      console.log("1️⃣ step('validateAndGetR2File')::existingFile metadata:", {
        key: existingFile?.key,
        size: existingFile?.size,
        contentType: existingFile?.httpMetadata?.contentType,
        customMetadata: existingFile?.customMetadata
      });

      // Only return metadata
      return {
        objectKey,
        fileName,
        hasExistingFile: !!existingFile,
        size: existingFile?.size || 0,
        mimeType: existingFile?.httpMetadata?.contentType || "application/octet-stream",
        customMetadata: existingFile?.customMetadata || {}
      };
    });

    // Step 2: Handle file upload if needed (as a separate step)
    if(!r2Result.hasExistingFile) {
      await step.do("uploadToR2", {
        retries: {
          limit: 3,
          delay: "5 seconds",
          backoff: "exponential"
        },
        timeout: "10 minutes"
      }, async ()=>{
        const file = files[0].formData.get("file") as File;
        if(!file) {
          throw new NonRetryableError("No file found in FormData");
        }

        try {
          // Get file buffer once to avoid multiple reads
          const fileBuffer = await file.arrayBuffer();

          // Add some logging
          console.log(`📤 Uploading file to R2: ${r2Result.objectKey}, size: ${fileBuffer.byteLength} bytes`);

          await this.env.R2.put(r2Result.objectKey, fileBuffer, {
            httpMetadata: {
              contentType: file.type || "application/octet-stream",
            },
            customMetadata: {
              fileName: r2Result.fileName,
              uploadedAt: new Date().toISOString(),
              size: fileBuffer.byteLength.toString()
            }
          });

          console.log(`✅ Successfully uploaded file to R2: ${r2Result.objectKey}`);
          return { uploaded: true, size: fileBuffer.byteLength };

        }catch(error) {
          console.error(`❌ R2 upload error for ${r2Result.objectKey}:`, error);

          // Check if it"s a connection error
          if(error.message.includes("connection") || error.message.includes("closed")) {
            // Make this error retryable
            throw new Error(`R2 connection error: ${error.message}`);
          }

          // For other errors, make them non-retryable
          throw new NonRetryableError(`R2 upload failed: ${error.message}`);
        }
      });
    }

    // Verify R2 upload result
    if(!r2Result.objectKey) {
      throw new Error("❌ R2 upload failed: No object key returned");
    }

    // Step 3: Create or get file record
    const fileRecord = await step.do("ensureFileRecord", async (): Promise<FileRecord>=>{
      console.log("🔍 Checking for existing file record:", {
        whitelabelId: vectorizeConfig.whitelabelId,
        objectKey: r2Result.objectKey
      });

      const existingRecord = await this.getFileRecord(
        vectorizeConfig.whitelabelId,
        r2Result.objectKey,
        vectorizeConfig.auth0Token,
      );

      console.log("📝 Existing record check result:", {
        found: !!existingRecord,
        recordDetails: existingRecord ? {
          _id: existingRecord._id,
          objectKey: existingRecord.objectKey,
          status: existingRecord.status
        } : "none"
      });

      if(existingRecord) {
        const record = {
          status: "success",
          data: {
            _id: existingRecord._id,
            target: existingRecord.target || "",
            title: existingRecord.title || "",
            chunkingTool: existingRecord.chunkingTool || "",
            originalFilename: existingRecord.originalName,
            uploadTimestamp: existingRecord.uploadTimestamp || Date.now(),
            updateTimestamp: existingRecord.updateTimestamp || Date.now(),
            rawFileKey: existingRecord.objectKey,
            fileKey: existingRecord.objectKey,
            status: existingRecord.status || "success",
            vectorIndexProcessing: existingRecord.vectorIndexProcessing || [],
            chunks: existingRecord.chunks || [],
            __v: existingRecord.__v || 0
          }
        } as FileRecord;

        console.log("📄 Returning existing file record:", {
          _id: record.data._id,
          objectKey: record.data.fileKey,
          status: record.status
        });

        return record;
      }

      const payload: FileRecordPayload = {
        bucket: "default",
        objectKey: r2Result.objectKey,
        originalName: r2Result.fileName,
        chunkingTool: initialSetup.fileInfo.processor,
        title: initialSetup.fileInfo.title || r2Result.fileName,
        description: initialSetup.fileInfo.description
      };

      console.log("📤 Creating new file record with payload:", payload);

      const fileRecord = await this.createFileRecord(
        vectorizeConfig.whitelabelId,
        payload,
        vectorizeConfig.auth0Token
      );

      console.log("✅ File record creation response:", {
        success: !!fileRecord?.data?._id,
        recordDetails: fileRecord?.data ? {
          _id: fileRecord.data._id,
          objectKey: fileRecord.data.objectKey,
          status: fileRecord.status
        } : "failed"
      });

      if(!fileRecord?.data?._id) {
        console.error("❌ File record creation failed:", {
          objectKey: r2Result.objectKey,
          response: fileRecord
        });
        throw new NonRetryableError("File record creation failed: No file ID returned");
      }

      // Update fileInfo with the file ID from the created record
      initialSetup.fileInfo.fileId = fileRecord.data._id;

      console.log("✅ File record created and assigned:", {
        fileId: initialSetup.fileInfo.fileId,
        objectKey: r2Result.objectKey,
        status: "file to chunks",
        fullRecord: fileRecord
      });

      await this.upsertWorkflowMetadata(
        instanceId,
        fileRecord.data._id,
        initialSetup.metadata,
      );

      return fileRecord;
    });

    // Add debug logging before processing
    console.log("🔄 Starting document processing with:", {
      fileRecord: {
        id: fileRecord?.data?._id,
        fileKey: fileRecord?.data?.fileKey, // Changed from objectKey to fileKey
        status: fileRecord?.status
      },
      processor: initialSetup.fileInfo.processor,
      config: initialSetup.normalizedConfig,
    });

    // Step 4: Process document in batches
    const documentChunks = [];
    const batchSize = 50;

    // Initialize processor once outside the loop
    const processorType = initialSetup.fileInfo.processor?.toLowerCase() || "unstructured";
    console.log(`🔄 Initializing processor: ${processorType}`, {
      config: initialSetup.normalizedConfig,
      fileName: initialSetup.fileInfo.fileName
    });

    const processor = processorType === "openparse"
      ? new OpenParseProcessor(
          this.env.OPENPARSE_API_KEY,
          this.env.OPENPARSE_API_URL,
          this.env,
          { useStreaming: true }
        )
      : new UnstructuredProcessor(
          this.env.UNSTRUCTURED_WORKER_URL,
          { useStreaming: true }
        );

    try {
      // Initialize processing session
      let session = await step.do("initializeProcessing", async ()=>{
        const fileUrl = `${this.env.R2_BUCKET_URL}/${r2Result.objectKey}`;
        console.log(`🔄 Initializing processing for file: ${fileUrl}`, {
          processor: processorType,
          config: initialSetup.normalizedConfig  // Use normalized config from initialSetup
        });

        return await processor.initializeSession(
          initialSetup.normalizedConfig,  // Use normalized config from initialSetup
          fileUrl
        );
      });

      // Process batches
      let batchNumber = 0;
      const hasMoreChunks = true;
      while(hasMoreChunks) {
        const batch = await step.do(`processDocumentBatch_${batchNumber}`, {
          retries: {
            limit: 3,
            delay: "5 seconds",
            backoff: "exponential"
          },
          timeout: "30 minutes",
        }, async ()=>{
          console.log(`📄 Processing batch ${batchNumber} for file: ${r2Result.objectKey}`); // Use r2Result.objectKey

          try {
            const result = await processor.processBatch(
              session.sessionId,
              batchNumber,
              batchSize
            );

            // Add validation for empty or invalid results
            if(!result) {
              console.log(`🏁 No chunks in batch ${batchNumber} - assuming end of processing`);
              return null;
            }

            console.log(`✅ Successfully processed batch ${batchNumber} with ${result.length} chunks`);
            return result;
          }catch(error) {
            console.error(`❌ Error processing batch ${batchNumber}:`, error);
            if(error.message.includes("Session expired")) {
              console.log("🔄 Session expired, reinitializing...");
              session = await processor.initializeSession(
                initialSetup.normalizedConfig,
                `${this.env.R2_BUCKET_URL}/${r2Result.objectKey}` // Use r2Result.objectKey
              );
              return processor.processBatch(
                session.sessionId,
                batchNumber,
                batchSize
              );
            }
            throw error;
          }
        });

        if(!batch) {
          console.log(`🏁 No more chunks to process after batch ${batchNumber}`);
          break;
        }

        documentChunks.push(...batch);
        console.log(`📊 Chunks accumulated: ${documentChunks.length} (added ${batch.length})`);

        batchNumber++;
      }
    } finally {
      // Ensure we clean up resources
      console.log(`📊 pre-processor.dispose documentChunks chunks preview: ${documentChunks[0]}`);
      await processor.dispose();
      console.log(`📊 post-processor.dispose documentChunks chunks preview: ${documentChunks[0]}`);
    }

    // Add final count logging
    console.log(`📊 Total chunks received: ${documentChunks.length}`);

    // Simple validation and transformation
    const validatedChunks = documentChunks
      .filter(chunk=>typeof chunk === "string" && chunk.trim().length > 0)
      .map((text, index)=>({
        id: createVectorId(initialSetup.fileInfo.fileId, index),
        text: text.trim(),
        metadata: {
          whiteLabelId: vectorizeConfig.whitelabelId,
          originalName: initialSetup.fileInfo.fileName,
          vectorId: createVectorId(initialSetup.fileInfo.fileId, index),
          fileObjectKey: initialSetup.fileInfo.objectKey,
          tokenCount: countTokens(text)
        }
      }));

    if(validatedChunks.length === 0) {
      throw new NonRetryableError("No valid chunks were generated during document processing");
    }

    console.log("✅ Chunks processed:", {
      original: documentChunks.length,
      valid: validatedChunks.length,
      sample: validatedChunks[0].text.substring(0, 100)
    });

    // Step 5: DeJunk Filtering
    const skipDeJunk = initialSetup.normalizedConfig?.skipDeJunk;
    console.log("🚮⚙️ DeJunk configuration:", { skipDeJunk });

    let filteredChunks;
    if(skipDeJunk) {
      console.log("🔄 Skipping DeJunk filtering");
      filteredChunks = validatedChunks;

      initialSetup.metadata.steps.deJunk.skipped = true;
      initialSetup.metadata.steps.deJunk.chunksBeforeDeJunk = validatedChunks.length;
      initialSetup.metadata.steps.deJunk.chunksAfterDeJunk = validatedChunks.length;
      initialSetup.metadata.steps.deJunk.duration = 0;
    } else {
      const deJunkStart = Date.now();
      filteredChunks = await step.do("filterChunks", async ()=>{
        initialSetup.metadata.steps.deJunk.chunksBeforeDeJunk = validatedChunks.length;

        console.log("🔄 Applying relevance filtering...");
        const relevanceThreshold = getRelevanceThreshold(initialSetup.normalizedConfig);

        const processedChunks = await this.filterRelevantChunks(validatedChunks, relevanceThreshold);

        if(!processedChunks?.length) {
          throw new NonRetryableError("No chunks remained after filtering");
        }

        initialSetup.metadata.steps.deJunk.chunksAfterDeJunk = processedChunks.length;
        initialSetup.metadata.steps.deJunk.duration = Date.now() - deJunkStart;

        return processedChunks;
      });
    }

    // Step: Store chunks in D1
    await step.do("storeChunksInD1", async ()=>{
      console.log(`📝 Storing ${filteredChunks.length} chunks in D1`);

      await this.storeChunksInD1(
        filteredChunks,
        {
          fileId: initialSetup.fileInfo.fileId,
          fileName: initialSetup.fileInfo.fileName,
          bucket: initialSetup.fileInfo.bucket,
          objectKey: initialSetup.fileInfo.objectKey
        },
        vectorizeConfig.whitelabelId
      );
    });

    // Step 6: Generate Embeddings and Store Vectors
    const vectorStep = await step.do("findOrCreateVectorIndex", async ()=>{
      const vectorizationStart = Date.now();
      const vectorIndex = `vector-index-${vectorizeConfig.ragId}`;

      // Ensure vector index exists first
      console.log(`🔄 Ensuring vector index exists: ${vectorIndex}`);
      const vectorizeResult = await findOrCreateVectorizeIndex(
        vectorIndex,
        `🔢 Vectors for chunks \n\n vectorIndex: ${vectorIndex}`,
        this.env.CLOUDFLARE_ACCOUNT_ID,
        this.env.CLOUDFLARE_API_TOKEN,
        this.env.CLOUDFLARE_API_URL || "https://api.cloudflare.com/client/v4"
      );

      return { vectorIndex, vectorizationStart };
    });

    await step.do("generateAndStoreEmbeddings", async ()=>{
      const vectorizeApi = new VectorizeAPI({
        accountId: this.env.CLOUDFLARE_ACCOUNT_ID,
        apiToken: this.env.CLOUDFLARE_API_TOKEN,
        ragName: "default"
      });

      // Process chunks in smaller batches
      const batchSize = 25;
      for(let i = 0; i < filteredChunks.length; i += batchSize) {
        const batch = filteredChunks.slice(i, batchSize);

        console.log("🔄 Processing batch for embeddings", {
          batchIndex: i / batchSize + 1,
          batchSize: batch.length,
          fileId: initialSetup.fileInfo.fileId,
          vectorIndex: vectorStep.vectorIndex
        });

        // Validate chunk data before processing
        const validBatch = batch.map((chunk, index)=>({
          id: createVectorId(initialSetup.fileInfo.fileId, index + (i * batchSize)),
          text: chunk.text,
          metadata: {
            ...chunk.metadata,
            whiteLabelId: vectorizeConfig.whitelabelId,
            originalName: initialSetup.fileInfo.fileName,
            vectorId: createVectorId(initialSetup.fileInfo.fileId, index + (i * batchSize)),
            fileObjectKey: initialSetup.fileInfo.objectKey,
            tokenCount: chunk.metadata?.tokenCount || countTokens(chunk.text)
          }
        })).filter(chunk=>{
          const isValid = chunk.id && chunk.text && chunk.metadata;
          if(!isValid) {
            console.warn("⚠️ Invalid chunk detected:", {
              hasId: !!chunk.id,
              hasText: !!chunk.text,
              hasMetadata: !!chunk.metadata
            });
          }
          return isValid;
        });

        if(validBatch.length === 0) {
          console.warn("⚠️ No valid chunks in batch, skipping");
          continue;
        }

        try {
          const vectorsWithEmbeddings = await Promise.all(
            validBatch.map(async (chunk)=>{
              // Define possible embedding result types
              interface EmbeddingResult {
                values?: number[],
                embedding?: number[],
                shape?: [number, number],
                data?: number[][],
              }

              const embeddingResult = await this.env.AI.run("@cf/baai/bge-base-en-v1.5", {
                text: chunk.text
              });

              // Log the embedding result to debug
              console.log("Embedding result for chunk:", {
                chunkId: chunk.id,
                embeddingType: typeof embeddingResult,
                embeddingValue: embeddingResult,
                isArray: Array.isArray(embeddingResult)
              });

              // Handle the new format with shape and data properties
              let embedding: number[];
              if(Array.isArray(embeddingResult)) {
                embedding = embeddingResult;
              } else if((embeddingResult as EmbeddingResult).values) {
                embedding = (embeddingResult as EmbeddingResult).values;
              } else if((embeddingResult as EmbeddingResult).embedding) {
                embedding = (embeddingResult as EmbeddingResult).embedding;
              } else if((embeddingResult as EmbeddingResult).data?.[0]) {
                // Extract the first row from the data array
                embedding = (embeddingResult as EmbeddingResult).data[0];
              } else {
                throw new Error(`Invalid embedding format received: ${JSON.stringify(embeddingResult)}`);
              }

              if(!Array.isArray(embedding)) {
                throw new Error(`Failed to extract embedding array from result: ${JSON.stringify(embeddingResult)}`);
              }

              return {
                id: chunk.id,
                values: embedding,
                metadata: {
                  whiteLabelId: vectorizeConfig.whitelabelId,
                  originalName: initialSetup.fileInfo.fileName,
                  vectorId: chunk.id,
                  fileObjectKey: initialSetup.fileInfo.objectKey,
                  tokenCount: chunk.metadata.tokenCount || countTokens(chunk.text),
                  tags: chunk.metadata.tags
                }
              };
            })
          );

          await vectorizeApi.upsert(vectorStep.vectorIndex, vectorsWithEmbeddings);

          console.log("✅ Successfully processed and stored batch", {
            batchIndex: i / batchSize + 1,
            totalBatches: Math.ceil(filteredChunks.length / batchSize)
          });
        }catch(error) {
          console.error("❌ Failed to process batch", {
            error: error.message,
            stack: error.stack,
            batchIndex: i / batchSize + 1
          });
          throw error;
        }
      }

      return {
        status: "vectorization_complete",
        totalChunks: filteredChunks.length,
        fileId: initialSetup.fileInfo.fileId
      };
    });

    // Store final metadata
    initialSetup.metadata.endTime = Date.now();
    initialSetup.metadata.totalDuration = initialSetup.metadata.endTime - initialSetup.metadata.startTime;

    await this.env.DB.prepare(`
      INSERT INTO workflow_metadata (
        workflow_id,
        file_id,
        metadata,
        created_at
      ) VALUES (?, ?, ?, datetime("now"))
      ON CONFLICT(workflow_id, file_id) 
      DO UPDATE SET 
        metadata = excluded.metadata,
        created_at = datetime("now")
    `).bind(
      instanceId,
      initialSetup.fileInfo.fileId,
      JSON.stringify(initialSetup.metadata),
    ).run();

    // Ensure filteredChunks is not empty and properly formatted
    if(!filteredChunks?.length) {
      console.error("❌ No chunks to process");
      throw new NonRetryableError("No chunks to process");
    }

    await this.updateFileStatus(
      vectorizeConfig.whitelabelId,
      initialSetup.fileInfo.fileId,
      RagVectorTextChunksStatus.EDITING,
      vectorizeConfig.auth0Token
    );

    // Add chunks
    const CHUNK_BATCH_SIZE = 50; // Adjust based on your payload size limits
    console.log(`🔄 Adding chunks in batches of ${CHUNK_BATCH_SIZE}`);

    // Process chunks in smaller batches to avoid "request entity too large" errors
    for(let i = 0; i < filteredChunks.length; i += CHUNK_BATCH_SIZE) {
      const chunkBatch = filteredChunks.slice(i, i + CHUNK_BATCH_SIZE);
      console.log(`🔄 Adding chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)} (${chunkBatch.length} chunks)`);

      const addChunksResponse = await fetch(
        `${this.env.API_HOST}/white-label/${vectorizeConfig.whitelabelId}/rag-vector/files/${initialSetup.fileInfo.fileId}/chunks/bulk`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${vectorizeConfig.auth0Token}`,
          },
          body: JSON.stringify({
            chunks: chunkBatch.map(chunk=>({
              text: chunk.text,
              tags: chunk.metadata?.tags || []
            }))
          })
        }
      );

      if(!addChunksResponse.ok) {
        const error = await addChunksResponse.json();
        console.error("❌ Failed to add chunks batch:", error, {
          batchIndex: Math.floor(i/CHUNK_BATCH_SIZE) + 1,
          batchSize: chunkBatch.length
        });

        // Update status to FAILED
        await this.updateFileStatus(
          vectorizeConfig.whitelabelId,
          initialSetup.fileInfo.fileId,
          RagVectorTextChunksStatus.FAILED_TO_CHUNK,
          vectorizeConfig.auth0Token
        );

        throw new Error(`Failed to add chunks batch: ${JSON.stringify(error)}`);
      }

      console.log(`✅ Successfully added chunks batch ${Math.floor(i/CHUNK_BATCH_SIZE) + 1}/${Math.ceil(filteredChunks.length/CHUNK_BATCH_SIZE)}`);
    }

    // Only update to COMPLETED after successful chunk addition
    await this.updateFileStatus(
      vectorizeConfig.whitelabelId,
      initialSetup.fileInfo.fileId,
      RagVectorTextChunksStatus.COMPLETED,
      vectorizeConfig.auth0Token
    );

    // After successful vector storage and status update
    if(vectorizeConfig.ragId) {
      await this.addFileToCustomRag(
        vectorizeConfig.whitelabelId,
        vectorizeConfig.ragId,
        initialSetup.fileInfo.fileId,
        vectorizeConfig.auth0Token
      );
    }

    // Update workflow metadata
    await this.upsertWorkflowMetadata(
      instanceId,
      initialSetup.fileInfo.fileId,
      initialSetup.metadata,
    );

    // Don"t return anything (void)
  }

  private async filterRelevantChunks(
    chunks: Array<{ text: string, metadata?: any }>,
    relevanceThreshold: number
  ): Promise<Array<{ text: string, metadata?: any }>>{
    if(!chunks.length) {
      return [];
    }

    const batchResults = [];

    // Process chunks in batches of FILTER_BATCH_SIZE
    for(let i = 0; i < chunks.length; i += FILTER_BATCH_SIZE) {
      const batch = chunks.slice(i, i + FILTER_BATCH_SIZE);

      try {
        const sentiments = await Promise.all(
          batch.map(chunk=>this.env.AI.run("@cf/huggingface/distilbert-sst-2-int8", {
              text: chunk.text
            })
          )
        );

        // Filter chunks based on positive sentiment score
        const filteredBatch = batch.filter((chunk, index)=>{
          const sentiment = sentiments[index];
          // Check if sentiment has the expected structure
          if(sentiment && Array.isArray(sentiment) && sentiment.length === 2) {
            const positiveScore = sentiment[1];
            return positiveScore >= relevanceThreshold;
          }
          // If sentiment structure is unexpected, keep the chunk by default
          return true;
        });

        batchResults.push(...filteredBatch);
      }catch(error) {
        console.error("Error during sentiment analysis:", error);
        // In case of error, keep all chunks from this batch
        batchResults.push(...batch);
      }
    }

    return batchResults;
  }

  private async updateFileStatus(
    whitelabelId: string,
    fileId: string,
    status: RagVectorTextChunksStatus,
    auth0Token: string
  ): Promise<void>{
    console.log(`🔄 Updating file status: ${fileId} -> ${status}`);

    if(!fileId) {
      console.error("❌ Cannot update file status: Invalid file ID (empty)");
      return;
    }

    try {
      const response = await fetch(
        `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/${fileId}/status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${auth0Token}`,
          },
          body: JSON.stringify({ status })
        }
      );

      if(!response.ok) {
        const errorData = await response.json();
        console.error(`❌ Failed to update file status:`, errorData);
        throw new Error(`❌ Failed to update file status: ${JSON.stringify(errorData)}`);
      }

      console.log(`✅ File status updated successfully: ${fileId} -> ${status}`);
    }catch(error) {
      console.error(`❌ Error updating file status:`, error);
      throw error;
    }
  }

  private async addFileToCustomRag(
    whitelabelId: string,
    ragId: string,
    fileId: string,
    auth0Token: string
  ): Promise<void>{
    console.log(`📝 Linking file ${fileId} to custom RAG ${ragId}`);

    // First, add to pending (using updatePendingAtomic)
    const pendingResponse = await fetch(
      `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/pending-file`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify({ fileId })
      }
    );

    if(!pendingResponse.ok) {
      const error = await pendingResponse.json();
      console.error("Failed to add file to pending:", error);
      throw new Error(`Failed to add file to pending: ${JSON.stringify(error)}`);
    }

    // After vectors are stored, move from pending to success
    const successResponse = await fetch(
      `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/${ragId}/success-file`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify({ fileId })
      }
    );

    if(!successResponse.ok) {
      const error = await successResponse.json();
      console.error("Failed to move file to success:", error);
      throw new Error(`Failed to move file to success: ${JSON.stringify(error)}`);
    }

    console.log(`✅ Successfully linked file ${fileId} to RAG ${ragId}`);
  }

  private async upsertWorkflowMetadata(
    workflowId: string,
    fileId: string,
    metadata: WorkflowMetadata,
    error?: string
  ): Promise<void>{
    try {
      // First try to delete any existing record
      await this.env.DB.prepare(`
        DELETE FROM workflow_metadata 
        WHERE workflow_id = ? AND file_id = ?
      `).bind(workflowId, fileId).run();

      // Then insert the new record
      await this.env.DB.prepare(`
        INSERT INTO workflow_metadata (
          workflow_id,
          file_id,
          metadata,
          error,
          created_at
        ) VALUES (?, ?, ?, ?, datetime("now"))
      `).bind(
        workflowId,
        fileId,
        JSON.stringify(metadata),
        error || null
      ).run();

      console.log(`✅ Workflow metadata upserted for workflow ${workflowId}, file ${fileId}`);
    }catch(error) {
      console.error("❌ Error upserting workflow metadata:", error);
      // Log but don"t throw - this shouldn"t fail the whole workflow
      console.warn("Continuing workflow despite metadata upsert failure");
    }
  }

  // Helper function to make API requests
  private async makeRequest(url: string, method: string, body?: any, token?: string){
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if(token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    // Clone the body if it"s a Response object
    const bodyToSend = body instanceof Response ?
      JSON.stringify(await body.clone().json()) :
      body ? JSON.stringify(body) : undefined;

    const response = await fetch(url, {
      method,
      headers,
      body: bodyToSend
    });

    // Log the request details
    console.log(`📡 API Request [${method}] ${url}:`, {
      headers,
      body: bodyToSend ? JSON.parse(bodyToSend) : undefined
    });

    if(!response.ok) {
      const errorData = await response.json().catch(()=>null);
      console.error("❌ Request failed:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      throw new Error(`Request failed: ${response.status} ${response.statusText}`);
    }

    return response;
  }

  // Helper method to get file record
  private async getFileRecord(whitelabelId: string, objectKey: string, auth0Token: string){
    try {
      const url = `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/get-record?objectKey=${objectKey}`;
      console.log("🔍 Attempting to get file record from:", url);

      const response = await this.makeRequest(url, "GET", undefined, auth0Token);

      console.log("📡 Get file record response status:", response.status);

      if(!response.ok) {
        console.warn("❌ Failed to get file record. Status:", response.status);
        const errorText = await response.text();
        console.warn("Error response:", errorText);
        return null;
      }

      const record = await response.json() as FileRecordResponse;
      console.log("✅ Got file record:", record);

      // Store the record in the class
      this.fileRecord = record?.data || null;

      return this.fileRecord;
    }catch(error) {
      console.error("💥 Error in getFileRecord:", error);
      return null;
    }
  }

  private async createFileRecord(
    whitelabelId: string,
    fileData: {
      bucket: string,
      objectKey: string,
      originalName: string,
      chunkingTool: string,
      title?: string,
      description?: string,
    },
    auth0Token: string
  ): Promise<any>{
    const url = `${this.env.API_HOST}/white-label/${whitelabelId}/rag-vector/files/create-record`;

    console.log("📤 Attempting to create file record:", {
      url,
      whitelabelId,
      objectKey: fileData.objectKey
    });

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${auth0Token}`,
        },
        body: JSON.stringify(fileData),
        // Add CF worker specific options
        cf: {
          cacheTtl: 0, // Don"t cache this request
          cacheEverything: false
        }
      });

      if(!response.ok) {
        const errorData = await response.json().catch(()=>null);
        console.error("❌ Failed to create file record:", {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        throw new Error(`Failed to create file record: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log("✅ File record created successfully:", result);
      return result;
    }catch(error) {
      console.error("💥 Error in createFileRecord:", {
        error: error.message,
        whitelabelId,
        objectKey: fileData.objectKey
      });
      throw error;
    }
  }

  private initializeMetadata(file: any): WorkflowMetadata{
    return {
      startTime: Date.now(),
      steps: {
        r2Processing: {
          duration: 0,
          originalChunks: 0
        },
        chunking: {
          duration: 0,
          originalChunks: 0,
          processor: file.processor || "unstructured",
          success: false,
          error: undefined
        },
        deJunk: {
          duration: 0,
          chunksBeforeDeJunk: 0,
          chunksAfterDeJunk: 0,
          skipped: false,
          relevanceThreshold: file.processorConfig?.relevanceThreshold || 0.3
        },
        vectorization: {
          duration: 0,
          totalBatches: 0,
          totalVectors: 0,
          batchSize: EMBEDDING_BATCH_SIZE,
          embeddingsProvider: file.processorConfig?.embeddings_provider || "cloudflare"
        }
      },
      fileInfo: {
        fileId: file.fileId,
        fileName: file.fileName,
        processor: file.processor,
        processorConfig: file.processorConfig,
        size: 0,
        mimeType: ""
      },
      status: "completed"
    };
  }

  // Add a helper method to get the file path
  private getFilePath(fileRecord: FileRecord): string{
    if(!fileRecord?.data?.fileKey) {
      throw new Error("No file record available");
    }
    return fileRecord.data.fileKey;
  }

}
