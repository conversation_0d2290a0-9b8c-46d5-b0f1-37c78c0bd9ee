FROM python:3.13.1

RUN pip install --upgrade pip

WORKDIR /home/<USER>/app

# Install required system dependencies
RUN apt-get update && apt-get install -y \
    cmake \
    pkg-config \
    build-essential \
    libprotobuf-dev \
    protobuf-compiler \
    && rm -rf /var/lib/apt/lists/*

# Install the python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

RUN apt-get update
RUN apt-get install -y software-properties-common
RUN apt-get install -y python3-launchpadlib apt-transport-https
RUN add-apt-repository ppa:savoury1/ffmpeg4
RUN apt-get update
RUN apt-get install -y ffmpeg


COPY . .

RUN mkdir -p /file-tmp && chmod -R 755 /file-tmp

CMD [ "python", "./server.py" ]