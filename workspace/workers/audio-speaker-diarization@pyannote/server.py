# Environment Variable Setup
from utils.config import load_env_files_with_dotenv
load_env_files_with_dotenv()
print("✅ Loaded env files.")

"""
Environment Variable Should be loaded first
openparse and potentially other libraries depend on the variables being set
"""

# Setup HTTP Server
import os
import json
import logging
from pathlib import Path

# Configure logging before app initialization
logging.basicConfig(
    level=os.getenv('LOG_LEVEL', 'DEBUG'),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


from flask import Flask, Response, request, abort, jsonify
from flask_cors import CORS

from src.allowed_files import allowed_file
from src.process_file import prepare_diarization, process_diarization # parsed_to_jsontext
from utils.json_stream import generator_to_json_array_stream
from utils.file import save_file_as_unique


app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

FORM_BODY_NAME = 'file'

@app.route('/', methods=['GET'])
def index():
    my_data = {
      "hello": "world",
      "whoami": "a speaker diarization processor"
    }
    return Response(
        json.dumps(my_data),
        status=200,
        mimetype='application/json'
    )

@app.route('/', methods=['POST'])
def upload_file():
    logger.info(f"📥 Received upload request")

    try:
        files = request.files.getlist(FORM_BODY_NAME)

        if len(files) == 0:
            logger.warning("❌ No file in request")
            return jsonify({"error": f"No {FORM_BODY_NAME} part in the request."}), 400

        if len(files) > 1:
            logger.warning(f"❌ Multiple files received: {len(files)}")
            return jsonify({"error": f"Only one {FORM_BODY_NAME} allowed."}), 400

        file = files[0]
        logger.info(f"📄 Processing file: {file.filename}")

        if not allowed_file(file.filename):
            logger.warning(f"❌ Invalid file type: {file.filename}")
            return jsonify({"error": "File extension not allowed."}), 400

        file_full_path = save_file_as_unique(file)
        # Process file
        try:
            dia, file_full_path = prepare_diarization(file_full_path)
            logger.info("✅ File processed successfully")
            return Response(
                generator_to_json_array_stream(process_diarization(dia)),
                status=200,
                mimetype='application/json'
            )

        except Exception as e:
            logger.error(f"❌ Error processing file: {str(e)}", exc_info=True)
            return jsonify({"error": str(e)}), 500
        finally:
            if os.path.exists(file_full_path):
                os.remove(file_full_path)

    except Exception as e:
        logger.error(f"❌ Server error: {str(e)}", exc_info=True)
        return jsonify({"error": "Internal server error"}), 500

if __name__ == '__main__':
    http_port = int(os.getenv("HTTP_PORT", "8084"))
    print("🌞 Starting server on: " + str(http_port))
    app.run(debug=True, host='0.0.0.0', port=http_port)