
import os
import uuid
import logging

logger = logging.getLogger(__name__)

UPLOAD_FOLDER = '/file-tmp'

if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)


def save_file_as_unique(uploaded_file):
    logger.info(f"📄 Processing file: {uploaded_file.filename}")

    _, ext = os.path.splitext(uploaded_file.filename)
    random_name = f"{uuid.uuid4()}{ext}"
    full_path = os.path.join(UPLOAD_FOLDER, random_name)
    uploaded_file.save(full_path)
    logger.debug(f"💾 Saved to: {full_path}")
    os.chmod(full_path, 0o644)
    logger.debug(f"Changed Permission {full_path}")


    return full_path

