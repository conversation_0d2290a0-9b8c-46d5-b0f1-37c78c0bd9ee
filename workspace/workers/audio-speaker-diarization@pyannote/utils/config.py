import os
from pathlib import Path
from dotenv import load_dotenv

def load_env_files_with_dotenv():
    print(f"🔍 Current working directory: {os.getcwd()}")
    env_folder = os.getenv('ENV_FOLDER')
    print(f"🔍 Looking for env files in: {env_folder}")
    if not env_folder:
        print("⚠️ No ENV_FOLDER specified")
        return

    cwd_contents = os.listdir(os.getcwd())
    print(cwd_contents)

    cwd_contents = os.listdir(env_folder)
    print(cwd_contents)

    env_path = Path(env_folder)
    if not env_path.exists():
        print(f"⚠️ ENV_FOLDER path doesn't exist: {env_folder}")
        return

    for env_file in env_path.glob('*.env'):
        previous_env = os.environ.copy()
        print(f"📝 Loading environment file: {env_file}")
        load_dotenv(dotenv_path=env_file)
        next_env = os.environ.copy()
        for key in next_env:
            if key not in previous_env or next_env[key] != previous_env[key]:
                print(f"✅ Loaded: {key}")

