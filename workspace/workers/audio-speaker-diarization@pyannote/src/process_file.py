
import os
import time
import logging
import json
import subprocess

logger = logging.getLogger(__name__)


HUGGING_FACE_TOKEN=os.environ.get("HUGGING_FACE_ACCESS_TOKEN")

if(HUGGING_FACE_TOKEN == None):
  raise Exception("Environment variable \"HUGGING_FACE_ACCESS_TOKEN\" is required")


from pyannote.audio import Pipeline
pipeline = Pipeline.from_pretrained(
    "pyannote/speaker-diarization-3.1",
    use_auth_token=HUGGING_FACE_TOKEN
)


"""
from pyannote.audio.pipelines.utils.hook import ProgressHook

class FileProgressHook(ProgressHook):
    def __init__(self, filepath: str):
        super().__init__()  # Call the parent constructor
        self.filename = os.path.basename(filepath)  # Store the filename

    def __call__(self, step_name, step_artifact, file=None, total=None, completed=None):
        # Call the original behavior
        super().__call__(step_name, step_artifact, file, total, completed)

        # Custom logging
        if completed is None:
            completed = total = 1

        progress = (completed / total) * 100 if total else 100
        logger.info(f"[{self.filename}] {step_name}: {progress:.2f}% completed")

    # Below exists so that we can run multiple progress hooks at one time
    def __enter__(self):
        # Simply return self without starting any live display.
        super().__enter__()
        # Override the live display's start method to disable it
        self.progress.start = lambda: None
        return self
    def __exit__(self, exc_type, exc_value, traceback):
        # No cleanup needed for the live display
        return super().__exit__(exc_type, exc_value, traceback)
"""

class FileProgressHook:
    def __init__(self, filepath: str):
        self.filename = os.path.basename(filepath)  # Store the filename

    def __call__(self, step_name, step_artifact, file=None, total=None, completed=None):
        if completed is None:
            completed = total = 1

        progress = (completed / total) * 100 if total else 100
        logger.info(f"[{self.filename}] {step_name}: {progress:.2f}% completed")
    def __enter__(self):
        return self
    def __exit__(self, exc_type, exc_value, traceback):
        return False


def prepare_diarization(file_full_path):
    file_full_path = save_flac_file(file_full_path)

    start_time = time.time()

    # Parse document
    with FileProgressHook(file_full_path) as hook:
      dia = pipeline(file_full_path, hook=hook)
      logger.info(f"Took {time.time() - start_time} to process file")

      return dia, file_full_path


def process_diarization(diarization):
    # Log details
    for item in diarization.itertracks(yield_label=True):
        speech_turn, _, speaker = item
        # logger.info(f"{speech_turn.start:4.1f} {speech_turn.end:4.1f} {speaker}")
        # logger.info(f" Tracks {json.dumps(track)}")
        yield { "start": speech_turn.start, "end": speech_turn.end, "speaker": speaker }


def save_wav_file(file_full_path):
    extension = os.path.splitext(file_full_path)[1].lower()
    if extension == ".wav":
        return file_full_path
    base_name = os.path.splitext(file_full_path)[0]
    wav_path = f"{base_name}-convert.wav"
    logger.info(f"Started converting {extension} into .wav")
    subprocess.run(
        [
            "ffmpeg",
            "-y", "-i", file_full_path,
            "-acodec", "pcm_s16le",
            # ac 1 means its mono instead of stereo, ar 16000 is the sample rate
            "-ac", "1", "-ar", "16000",
            wav_path
        ],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
    )
    logger.info(f"Finished converting {extension} into .wav")
    if os.path.exists(wav_path):
        os.remove(file_full_path)
    else:
        raise Exception("FFmpeg conversion failed: WAV file not created")
    return wav_path

def save_mp3_file(file_full_path):
    extension = os.path.splitext(file_full_path)[1].lower()
    base_name = os.path.splitext(file_full_path)[0]
    new_path = f"{base_name}-convert.mp3"
    logger.info(f"Started converting {extension} into .mp3")
    subprocess.run(
        [
            "ffmpeg",
            "-y", "-i", file_full_path,
            "-f", "mp3", "-acodec", "libmp3lame",
            # ac 1 means its mono instead of stereo, ar 16000 is the sample rate
            "-ac", "1", "-ar", "16000",
            new_path
        ],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
    )
    logger.info(f"Finished converting {extension} into .mp3")
    if os.path.exists(new_path):
        os.remove(file_full_path)
    else:
        raise Exception("FFmpeg conversion failed: WAV file not created")
    return new_path

def save_flac_file(file_full_path):
    return save_audio_file(file_full_path, "flac", "flac")

def save_audio_file(file_full_path, codec, new_extension):
    old_extension = os.path.splitext(file_full_path)[1].lower()
    base_name = os.path.splitext(file_full_path)[0]
    new_path = f"{base_name}-convert.{new_extension}"
    logger.info(f"Started converting {old_extension} into .{new_extension}")
    subprocess.run(
        [
            "ffmpeg",
            "-y", "-i", file_full_path,
            "-acodec", codec,
            "-ac", "1",
            "-ar", "16000",
            new_path
        ],
        stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
    )
    logger.info(f"Finished converting {old_extension} into .{new_extension}")
    if os.path.exists(new_path):
        os.remove(file_full_path)
    else:
        raise Exception("FFmpeg conversion failed: WAV file not created")
    return new_path
