
import os
import time
import logging
import json
import subprocess

logger = logging.getLogger(__name__)

import torch
from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline
from datasets import load_dataset


device = "cuda:0" if torch.cuda.is_available() else "cpu"
torch_dtype = torch.float16 if torch.cuda.is_available() else torch.float32

model_id = "openai/whisper-large-v3"

model = AutoModelForSpeechSeq2Seq.from_pretrained(
    model_id, torch_dtype=torch_dtype, low_cpu_mem_usage=True, use_safetensors=True
)
model.to(device)

processor = AutoProcessor.from_pretrained(model_id)

pipe = pipeline(
    "automatic-speech-recognition",
    model=model,
    tokenizer=processor.tokenizer,
    feature_extractor=processor.feature_extractor,
    torch_dtype=torch_dtype,
    device=device,

    # For bigger files
    chunk_length_s=30,
    batch_size=16,
)

class FileProgressHook(ProgressHook):
    def __init__(self, filepath: str):
        super().__init__()  # Call the parent constructor
        self.filename = os.path.basename(filepath)  # Store the filename

    def __call__(self, step_name, step_artifact, file=None, total=None, completed=None):
        # Call the original behavior
        super().__call__(step_name, step_artifact, file, total, completed)

        # Custom logging
        if completed is None:
            completed = total = 1

        progress = (completed / total) * 100 if total else 100
        logger.info(f"[{self.filename}] {step_name}: {progress:.2f}% completed")

def prepare_transcription(file_full_path):
    """
    # Converting to .wav if necessary
    extension = os.path.splitext(file_full_path)[1].lower()
    if extension != ".wav":
        base_name = os.path.splitext(file_full_path)[0]
        wav_path = f"{base_name}.wav"
        logger.info(f"Started converting {extension} into .wav")
        subprocess.run(
            ["ffmpeg", "-i", file_full_path, wav_path],
            stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=True
        )
        logger.info(f"Finished converting {extension} into .wav")
        if os.path.exists(wav_path):
            os.remove(file_full_path)
        else:
            raise Exception("FFmpeg conversion failed: WAV file not created")
        file_full_path = wav_path
    """
    start_time = time.time()

    # Parse document
    result = pipe(file_full_path, return_timestamps="word")
    logger.info(f"Took {time.time() - start_time} to process file")

    return result, file_full_path


def process_transcription(result):
    logger.info(f"Results {json.dumps(result)}")


