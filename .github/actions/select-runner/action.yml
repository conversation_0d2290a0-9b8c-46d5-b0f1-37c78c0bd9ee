name: "Select Runner"
description: "Select available runner"
inputs:
  github-token:
    description: "GitHub token for API authentication"
    required: true
outputs:
  runner:
    description: "Selected runner"
    value: ${{ steps.select-runner.outputs.runner }}
runs:
  using: "composite"
  steps:
    - id: select-runner
      shell: bash
      run: |
        # Extract organization and repository from GITHUB_REPOSITORY
        ORG_NAME=$(echo "${GITHUB_REPOSITORY}" | cut -d '/' -f 1)
        REPO_NAME=$(echo "${GITHUB_REPOSITORY}" | cut -d '/' -f 2)
        
        echo "🏢 Organization: ${ORG_NAME}"
        echo "📚 Repository: ${REPO_NAME}"
        
        # Debug: Show token presence (safely)
        if [ -n "${{ inputs.github-token }}" ]; then
          echo "✅ GitHub token is present"
        else
          echo "❌ GitHub token is missing"
          echo "runner=ubuntu-latest" >> $GITHUB_OUTPUT
          exit 0
        fi
        
        # Initialize variables to store runners from both API calls
        COMBINED_RUNNERS=""
        
        # -------------------------
        # 1. Check Repository-level runners first
        # -------------------------
        repo_api_url="https://api.github.com/repos/${GITHUB_REPOSITORY}/actions/runners"
        echo "🔍 Repository API URL: ${repo_api_url}"
        
        # Make API request with verbose error handling
        echo "📡 Making repository API request..."
        response=$(curl -s -w "\n%{http_code}" \
          -H "Authorization: token ${{ inputs.github-token }}" \
          -H "Accept: application/vnd.github.v3+json" \
          "${repo_api_url}")
        
        # Split response into body and status code
        http_code=$(echo "$response" | tail -n1)
        repo_body=$(echo "$response" | sed '$ d')
        
        echo "📊 Repository HTTP Status Code: ${http_code}"
        
        # Try with Bearer format if token format fails
        if [ "$http_code" != "200" ]; then
          echo "🔄 Trying repository API with Bearer format instead..."
          response=$(curl -s -w "\n%{http_code}" \
            -H "Authorization: Bearer ${{ inputs.github-token }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "${repo_api_url}")
          
          http_code=$(echo "$response" | tail -n1)
          repo_body=$(echo "$response" | sed '$ d')
          
          echo "📊 Repository HTTP Status Code with Bearer: ${http_code}"
        fi
        
        # Process repository-level runners if API call was successful
        if [ "$http_code" == "200" ]; then
          echo "✅ Repository API call successful"
          
          # Validate JSON response
          if echo "${repo_body}" | grep -v '^$' | jq -e . >/dev/null 2>&1 && \
             echo "${repo_body}" | grep -v '^$' | jq -e 'has("runners")' >/dev/null 2>&1; then
            
            echo "🏃 Repository Runners:"
            echo "${repo_body}" | grep -v '^$' | jq -r '.runners[] | {name, status, busy}'
            
            # Get repository-level available runners
            REPO_RUNNERS=$(echo "${repo_body}" | grep -v '^$' | jq -r '.runners[] | select(.status=="online") | select(.busy==false) | .name')
            
            if [ -n "$REPO_RUNNERS" ]; then
              echo "✅ Found available repository runner(s):"
              echo "$REPO_RUNNERS"
              COMBINED_RUNNERS="${REPO_RUNNERS}"
            fi
          else
            echo "⚠️ Invalid repository runners JSON or missing runners array"
          fi
        else
          echo "⚠️ Repository API request failed with status ${http_code}"
        fi
        
        # -------------------------
        # 2. Check Organization-level runners
        # -------------------------
        org_api_url="https://api.github.com/orgs/${ORG_NAME}/actions/runners"
        echo "🔍 Organization API URL: ${org_api_url}"
        
        # Make API request for organization runners
        echo "📡 Making organization API request..."
        response=$(curl -s -w "\n%{http_code}" \
          -H "Authorization: token ${{ inputs.github-token }}" \
          -H "Accept: application/vnd.github.v3+json" \
          "${org_api_url}")
        
        # Split response into body and status code
        http_code=$(echo "$response" | tail -n1)
        org_body=$(echo "$response" | sed '$ d')
        
        echo "📊 Organization HTTP Status Code: ${http_code}"
        
        # Try with Bearer format if token format fails
        if [ "$http_code" != "200" ]; then
          echo "🔄 Trying organization API with Bearer format instead..."
          response=$(curl -s -w "\n%{http_code}" \
            -H "Authorization: Bearer ${{ inputs.github-token }}" \
            -H "Accept: application/vnd.github.v3+json" \
            "${org_api_url}")
          
          http_code=$(echo "$response" | tail -n1)
          org_body=$(echo "$response" | sed '$ d')
          
          echo "📊 Organization HTTP Status Code with Bearer: ${http_code}"
        fi
        
        # Process organization-level runners if API call was successful
        if [ "$http_code" == "200" ]; then
          echo "✅ Organization API call successful"
          
          # Validate JSON response
          if echo "${org_body}" | grep -v '^$' | jq -e . >/dev/null 2>&1 && \
             echo "${org_body}" | grep -v '^$' | jq -e 'has("runners")' >/dev/null 2>&1; then
            
            echo "🏃 Organization Runners:"
            echo "${org_body}" | grep -v '^$' | jq -r '.runners[] | {name, status, busy}'
            
            # Get organization-level available runners
            ORG_RUNNERS=$(echo "${org_body}" | grep -v '^$' | jq -r '.runners[] | select(.status=="online") | select(.busy==false) | .name')
            
            if [ -n "$ORG_RUNNERS" ]; then
              echo "✅ Found available organization runner(s):"
              echo "$ORG_RUNNERS"
              
              # Add organization runners to combined list
              if [ -n "$COMBINED_RUNNERS" ]; then
                COMBINED_RUNNERS="${COMBINED_RUNNERS}"$'\n'"${ORG_RUNNERS}"
              else
                COMBINED_RUNNERS="${ORG_RUNNERS}"
              fi
            fi
          else
            echo "⚠️ Invalid organization runners JSON or missing runners array"
          fi
        else
          echo "⚠️ Organization API request failed with status ${http_code}"
        fi
        
        # -------------------------
        # 3. Make the final runner selection
        # -------------------------
        if [ -n "$COMBINED_RUNNERS" ]; then
          echo "✅ Found available self-hosted runner(s) (repo or org):"
          echo "$COMBINED_RUNNERS"
          echo "runner=self-hosted" >> $GITHUB_OUTPUT
        else
          # If no runners found and we're executing in a GitHub Actions test
          if [ -n "$ACT" ]; then
            echo "🧪 Running in ACT test environment, using self-hosted runner"
            echo "runner=self-hosted" >> $GITHUB_OUTPUT
          else
            echo "⚠️ No available self-hosted runners found (repo or org), using GitHub-hosted runner"
            echo "runner=ubuntu-latest" >> $GITHUB_OUTPUT
          fi
        fi
        
        # -------------------------
        # 4. List offline runners for maintenance
        # -------------------------
        echo "🔍 Checking for offline runners..."
        
        # Check repo-level offline runners
        if [ "$http_code" == "200" ] && echo "${repo_body}" | grep -v '^$' | jq -e 'has("runners")' >/dev/null 2>&1; then
          repo_offline=$(echo "${repo_body}" | grep -v '^$' | jq -r '.runners[] | select(.status=="offline") | .name')
          if [ -n "$repo_offline" ]; then
            echo "⚠️ Found offline repository runners that should be cleaned up:"
            echo "$repo_offline"
          fi
        fi
        
        # Check org-level offline runners
        if [ "$http_code" == "200" ] && echo "${org_body}" | grep -v '^$' | jq -e 'has("runners")' >/dev/null 2>&1; then
          org_offline=$(echo "${org_body}" | grep -v '^$' | jq -r '.runners[] | select(.status=="offline") | .name')
          if [ -n "$org_offline" ]; then
            echo "⚠️ Found offline organization runners that should be cleaned up:"
            echo "$org_offline"
          fi
        fi
